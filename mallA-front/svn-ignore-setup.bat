@echo off
echo 设置SVN忽略属性...

REM 在项目根目录设置忽略属性
svn propset svn:ignore "node_modules
dist
build
*.log
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.vscode
.idea
*.swp
*.swo
.DS_Store
Thumbs.db
desktop.ini
coverage
.nyc_output
.cache
.parcel-cache
tmp
temp
.eslintcache
.npm
pids
*.pid
*.seed
*.pid.lock
logs
*.tgz
*.tar.gz
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*" .

echo SVN忽略属性设置完成！
echo 请运行 'svn commit -m "设置忽略属性"' 来提交这些设置
pause