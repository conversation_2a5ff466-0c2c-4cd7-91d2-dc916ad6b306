{"name": "htgl", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "vite --mode test", "preview": "vite preview --host 0.0.0.0 --port 2030"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.7", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "qs": "^6.14.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sass": "^1.86.3", "terser": "^5.43.1", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.3.2", "vite-plugin-vue-devtools": "^7.7.5", "vite-plugin-vue-inspector": "^5.3.1"}}