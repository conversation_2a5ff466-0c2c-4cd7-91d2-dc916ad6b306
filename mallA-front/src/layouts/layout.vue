<template>
  <el-row class="tac" style="height: 100vh;">
    <!-- 左侧固定菜单 -->
    <el-col :span="4" class="left-menu">
      <el-menu
        default-active="1"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        router
      >
        <el-menu-item index="/technicalDrainage">
          <el-icon><icon-menu /></el-icon>
          <span>技术排水</span>
        </el-menu-item>
        <el-menu-item index="/otherPage1">
          <el-icon><icon-menu /></el-icon>
          <span>其他页面1</span>
        </el-menu-item>
        <el-menu-item index="/otherPage2">
          <el-icon><document /></el-icon>
          <span>其他页面2</span>
        </el-menu-item>
        <el-menu-item index="/otherPage3">
          <el-icon><setting /></el-icon>
          <span>其他页面3</span>
        </el-menu-item>
      </el-menu>
    </el-col>

    <!-- 右侧内容区域 -->
    <el-col :span="20" class="main-content">
      <router-view />
    </el-col>
  </el-row>
</template>

<script setup>
import {
  Document,
  Menu as IconMenu,
  Setting,
} from '@element-plus/icons-vue'

const handleOpen = (key, keyPath) => {
  console.log('打开菜单:', key, keyPath)
}
const handleClose = (key, keyPath) => {
  console.log('关闭菜单:', key, keyPath)
}
</script>

<style scoped>
.left-menu {
  background-color: #f5f7fa;
  height: 100%;
  padding: 20px 0;
}
.main-content {
  padding: 20px;
}
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 100%;
  min-height: 400px;
}
</style>