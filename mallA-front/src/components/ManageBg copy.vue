<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Setting } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

interface CompanyInfo {
  name: string;
  
}

const router = useRouter();
const company = ref<CompanyInfo>({
  name: '广州中业科技有限公司' 
});


const handleToStatistics = (): void => {
  router.push('/statistics');
};

const handleToCommodity = (): void => {
  router.push('/commodity');
};

const handleToAllCustomer = (): void => {
  router.push('/allCustomer');
};

const handleToAllOrders = (): void => {
  router.push('/allOrders');
};

const handleToSetting = (): void =>{
  router.push('/setting')
};

const handleTofinaceSetting = (): void =>{
  router.push('/financeSetting')
};

const handleToAllUser = ():void =>{
  router.push('/allUser')
};
const handleToCustomerServer = ():void =>{
  router.push('/customerServer')
};
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-aside class="aside" width="263px">
       
        <div class="asideTitle">
          <p class="name">{{ company.name }}</p>
        </div>
        
        
        <div class="menu-container">
          <div class="menu-button">公司</div>
          <div @click="handleToStatistics" class="menu-button">统计</div>
          <div @click="handleToCommodity" class="menu-button">商品</div>
          <div @click="handleToAllCustomer" class="menu-button">客户</div>
          <div @click="handleToAllUser" class="menu-button">用户</div>
          <div @click="handleToAllOrders" class="menu-button">订单</div>
          <div @click="handleToCustomerServer" class="menu-button">客服</div>
          <div @click="handleTofinaceSetting" class="menu-button">财务</div>
          <div class="menu-button">数据</div>
          <div class="menu-button">定位</div>
        </div>
      </el-aside>
      
      <el-container>
        <el-header>
          <div class="header-content">
            <div class="header-top">
              <el-icon @click="handleToSetting" class="setting-icon"><Setting /></el-icon>
            </div>
            <div class="header-line"></div>
            <!-- <img class="header-line" src="../images/line.png" alt="分割线"> -->
          </div>
        </el-header>
       <el-main>
        <slot />
       </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style lang="scss" scoped>
.common-layout {
  display: flex;
  height: 100vh;
  
  .aside {
    background: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    
    .asideTitle{
        background-color: #D1D9F7;
        width: 263px;
        border-bottom: 5px solid #0525F7;
        display: flex;
        justify-content: center;
        align-items: center;
        .name{
            width: 184px;
            color:#0525F7;
            font-size: 35px;
           
            
           
        }
    }
    
    .menu-container {
      width: 100%;
      padding: 30px; 
      position: relative;
      z-index: 2;
     
      .menu-button {
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #3A58CF;
        padding: 12px 20px;
        border-radius: 15px;
        cursor: pointer;
        background-color: #fff;
        transition: all 0.3s;
        border:1px solid #3A58CF;
        width: 153px;
        height: 65px;
        margin: 0 auto 15px;
        box-sizing: border-box;
        
        &:hover {
          background-color: #f0f4ff;
        }
      }
    }
  }
  
  .el-header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0;
    display: block;
    height: 122px;
    position: relative;
    z-index: 2;
    
    .header-content {
      display: flex;
      flex-direction: column;
      
      .header-top {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 10px 33px 10px 0;
        
        .setting-icon {
          font-size: 20px;
          color: #606266;
          cursor: pointer;
          
          &:hover {
            color: #3A5BDE;
          }
        }
      }
      
      .header-line {
        width: 100%;
        height: 81px;
      }
    }
  }

  .el-main{
    padding: 0;
  }
}
</style>