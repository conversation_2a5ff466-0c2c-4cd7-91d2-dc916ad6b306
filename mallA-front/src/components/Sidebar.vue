<template>
  <div class="layout-container">
    <!-- <div class="sidebar">
      <ul class="sidebar2">
        <li
          v-for="(item, index) in menuItem"
          :key="index"
          :class="{ active1: $route.path === item.path }"
        >
          <router-link :to="item.path">
            {{ item.label }}
          </router-link>
        </li>
      </ul>
    </div> -->
    <div class="sidebar">
      <ul class="sidebar2">
        <li
          v-for="(item, index) in menuItems"
          :key="index"
          :class="{ active: $route.path === item.path }"
        >
          <router-link :to="item.path">
            {{ item.label }}
          </router-link>
        </li>
      </ul>
    </div>
    <div class="main-content">
      <!-- 您的主内容区域 -->
      <router-view></router-view>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
const index = ref();
// 当前选中项
const menuItems = ref([
  { label: "交易数据明细", path: "/cooperativeEnterprise/transactionData" },
  { label: "分量设置", path: "/heftSetting" },
  { label: "关系链", path: "/relationshipChainSetting" },
  { label: "量化数", path: "/quantify" },
  { label: "量化值", path: "/quantizer" },
  { label: "核销数据", path: "/writeData" },
  { label: "量化值进化量", path: "/quantEvolution" },
  { label: "信用值进化量", path: "/creditEvolution" },
  { label: "授权表", path: "/authorization" },
]);
const menuItem = ref([
  { label: "中南惠", path: "/cooperativeEnterprise/transactionData" },
  { label: "about", path: "/systemSet" },
]);
const selectedItem = ref(null);

// 选择处理
const selectItem = (item) => {
  selectedItem.value = item;
  // 这里可以添加点击后的业务逻辑
};
// 格式化日期显示
const btn = (data) => {
  index.value = data;
};
const isActive = ref(false);
const power = () => {
  console.log(111);
  isActive.value = !isActive.value;
};
</script>
<style lang="scss" scoped>
/* 基础样式 */
html,
body {
  height: 100vh;
  margin: 0;
  padding: 0;
  color: #fff;
}
.active {
  background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
  color: black; /* 确保文字颜色在背景色上清晰可见 */
}
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }
  .sidebar1,
  .sidebar {
    width: 100%;
    height: auto;
  }
}
.sidebar {
  --active-bg: #2c3e50;
  --active-color: #ffffff;
  --hover-bg: #e9ecef;

  width: 240px;
  background: #f8f9fa;
  height: 100vh;
  border-right: 1px solid #dee2e6;

  .sidebar2 {
    list-style: none;
    padding: 20px 0;
    margin: 0;

    li {
      position: relative;
      transition: background-color 0.3s ease;

      a {
        display: block;
        // padding: 12px 24px;
        color: #2c3e50;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: var(--hover-bg);
      }

      &.active1 {
        background-color: var(--active-bg);
        border-left: 4px solid #3498db;

        a {
          color: var(--active-color);
          font-weight: 500;
        }
      }
    }
  }
}
.layout-container {
  height: 100vh;

  display: flex;
  // min-height: 100vh;
  position: fixed;
  z-index: 1000;
}
.sidebar2 li {
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.sidebar2 li a {
  color: #666;
  text-decoration: none;
}

.sidebar2 li.active {
  background: #2c3e50;
  border-left: 4px solid #3498db;
}

.sidebar2 li.active a {
  color: white;
}
.sidebar1,
.sidebar {
  // background: #f8f9fa; /* 侧边栏背景色 */
  padding: 15px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  height: 100%;
  width: 150px;
  border: 1px solid #000;
}

.sidebar3,
.sidebar2 {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 100%;
  overflow-y: auto;
}

.sidebar3 li,
.sidebar2 li {
  color: #ffff;
  padding: 10px 25px;
  margin-bottom: 5px;
  border-radius: 4px;
  transition: background 0.3s;
}

.sidebar3 li:hover,
.sidebar2 li:hover {
  background: #e9ecef;
}
.sidebar3 li:after,
.sidebar2 li:after {
  background: #e9ecef;
}

.sidebar3 a,
.sidebar2 a {
  text-decoration: none;
  color: #fff;
  display: block;
  text-align: center;
}

.main-content {
  flex: 1;
  padding: 20px;
}
</style>