<script setup>
import {ref} from "vue";

const route = useRoute()
// 当前激活菜单项
const openMenus = computed(() => {
  if (route.path.startsWith('/technicalDrainage')) {
    return ['/technicalDrainage']
  }
  return []
})

const props = defineProps({
  list: String,      // 类型校验
  count: {
    type: Number,
    default: 0        // 默认值
  }
});

</script>

<template>
  <el-container style="">
    <!-- 左侧菜单 -->
    <el-aside width="200px" class="sub-menu">
      <el-menu
          :default-active="$route.path"
          router
          background-color="#fff"
          text-color="#666"
          active-text-color="#3a5bde"
          :default-openeds="openMenus"
          class="aside-menu"
      >
        <el-submenu>
          <el-menu-item v-for="(item, index) in list" :index="item.moduleUrl">{{ item.moduleName }}</el-menu-item>
        </el-submenu>
      </el-menu>
    </el-aside>

    <!-- 主内容区域 -->
    <el-main class="main-content">
      <router-view />
    </el-main>

  </el-container>
</template>

<style lang="scss" scoped>

/* 去除二级菜单默认边框 */
:deep(.el-submenu .el-menu) {
  border-right: none !important;
  background-color: transparent !important;

}

:deep(.el-submenu__title) {
  background-color: #fff !important;
}

/* 侧边栏菜单样式 */
.sub-menu {
  //height: 100%;
  border-right: 1px solid #000;
  background-color: #fff;
}

.el-menu-item.is-active {
  background-color: #2c3e50 !important;
  color: #fff !important;
}

.aside-menu {
  border-right: none;
}

/* 主体内容样式 */
.main-content {
  padding: 20px;
  background-color: #f9f9f9;
}

/* 强制让 el-menu-item 内容居中 */
:deep(.el-menu-item) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  font-size: 30px;
}

/* 如果只想对基础系统的子菜单生效，可以加更具体的类名或结构选择器 */
</style>