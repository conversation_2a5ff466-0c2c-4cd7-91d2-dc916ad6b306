<script setup>
import {ref} from "vue";
import LeftTwo from './LeftTwo.vue'
import {useRouter} from 'vue-router'
const route = useRoute()
// 当前激活菜单项
const openMenus = computed(() => {
  if (route.path.startsWith('/technicalDrainage')) {
    return ['/technicalDrainage']
  }
  return []
})
const index = ref(0)
const router = useRouter()


const onclick = (item, _index) => {
  index.value = _index
  console.log("111")
  // 确保 item 存在 children 并且为空才跳转
  if (!item.children || item.children.length === 0) {
    console.log("222")
    if (item.moduleUrl) {
      console.log("222")
      router.push(item.moduleUrl)
    } else {
      console.log("33")
      console.warn('item.moduleUrl 不存在，无法跳转', item)
    }
  }
}

const props = defineProps({
  list: Array,      // 类型校验
  count: {
    type: Number,
    default: 0        // 默认值
  }
});

</script>

<template>
    <el-container >
      <!-- 左侧菜单 -->
      <el-aside width="180px" class="sub-menu">
        <el-menu
          :default-active="$route.path"
          router
          background-color="#fff"
          text-color="#666"
          active-text-color="#3a5bde"
          :default-openeds="openMenus"
          class="aside-menu"
        >
          <el-submenu>
            <el-menu-item @click="onclick(item,index)" v-for="(item, index) in list">{{ item.moduleName }}</el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>
      <!-- 主内容区域 -->
      <el-main v-if="list[index] != null &&  (list[index].children == null || list[index].children.length == 0)" class="main-content">
        <router-view />
      </el-main>
      <LeftTwo v-else-if="list[index] != null && list[index].children != null" :list="list[index].children" ></LeftTwo>
    </el-container>
</template>

<style lang="scss" scoped>

/* 去除二级菜单默认边框 */
:deep(.el-submenu .el-menu) {
  border-right: none !important;
  background-color: transparent !important;
}

:deep(.el-submenu__title) {
  background-color: #fff !important;
}

/* 侧边栏菜单样式 */
.sub-menu {
  //position: fixed;
  //z-index: 10000;
  //height: 100%;
  border-right: 1px solid #000;
  background-color: #fff;
}

.el-menu-item.is-active {
  background-color: #2c3e50 !important;
  color: #fff !important;
}

.aside-menu {
  border-right: none;

}

/* 主体内容样式 */
.main-content {
  padding: 20px;
  background-color: #f9f9f9;

}

/* 强制让 el-menu-item 内容居中 */
:deep(.el-menu-item) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  font-size: 16px;
  /* 强制换行 */
  white-space: normal !important;  // 允许换行
  word-break: break-all !important; // 打断长单词或中文连续字符
  line-height: 1.5 !important;     // 可选：调整行高
  //height: auto !important;         // 自动高度
  min-height: 50px;                // 可选：最小高度
  padding: 8px 8px !important;
  border:1px solid #000;
  width: 150px;
  margin: 10px auto;
  height: 50px;
  border-radius: 3px;
}

/* 如果只想对基础系统的子菜单生效，可以加更具体的类名或结构选择器 */
</style>