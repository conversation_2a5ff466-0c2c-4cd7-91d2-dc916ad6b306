<script setup>
import { ref } from 'vue'
</script>

<template>
  <div class="container">
    <div class="input-group">
      <input class="form-input" type="text" placeholder="姓名">
      <input class="form-input" type="text" placeholder="岗位">
      <input class="form-input" type="text" placeholder="工号">
      <input class="form-input" type="tel" placeholder="电话">
    </div>
    
    <div class="action-group">
      <div class="restrict-login">
        <span class="restrict-text">限制登入</span>
        <el-radio class="custom-radio" value="1" size="large"></el-radio>
      </div>
      <el-button class="responsible-btn">负责</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 20px;
  width: 560px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 22px;
  align-items: center;
  
  .form-input {
    background-color: #E5E5E5;
    width: 300px;
    height: 41px;
    border: none;
    border-radius: 4px;
    padding: 0 12px;
    font-size: 14px;
    align-items: center;

    
    &::placeholder {
      color: #999;
    }
    
    &:focus {
      outline: 1px solid #3A5BDE;
    }
  }
}

.action-group {
  display: flex;
  align-items: center;
  margin-top: 22px;
  gap: 20px;
  
  .restrict-login {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .restrict-text {
      font-size: 14px;
      color: #333;
      margin-left: 63px;
    }
    
    .custom-radio {
      :deep(.el-radio__inner) {
        border-radius: 4px;
        width: 16px;
        height: 16px;
        
        &::after {
          border-radius: 2px;
          width: 8px;
          height: 8px;
          background-color: #3A5BDE;
        }
      }
    }
  }
  
  .responsible-btn {
    background-color: #3A5BDE;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    width: 99px;
    height: 41px;
    margin-left: 192px;
    
    &:hover {
      background-color: #2c4ac7;
    }
    
    &:active {
      background-color: #1a399e;
    }
  }
}
</style>