
<template>
  <div class="common-layout">
    <el-container>
      <el-aside class="aside" width="auto">
        <!-- 公司昵称 -->
        <div class="asideTitle">
          <!-- <img class="CompanyName" src="../images/CompanyName.png" alt=""> -->
          <h1 class="CompanyName">玄数量化</h1>
        </div>
        <!-- 菜单按钮 -->
        <div class="menu-container">
          <div class="sidebar3">
            <el-row class="tac">
              <el-col :span="12">
                <div class="sidebar">
                  <ul class="sidebar2">
                    <li
                      v-for="(item, index) in menuItems"
                      :key="index"
                      :class="{ active1: isActiveMenu(item) }"
                    >
                      <router-link :to="{ path : item.moduleUrl,query:{ 'id' : item.id } }">
                        {{ item.moduleName }}
                      </router-link>
                    </li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-aside>

      <el-container>
        <el-header>
          <div class="header-content">
            <div class="header-top">
              <el-icon class="setting-icon"><Setting /></el-icon>
              <div class="user-info">
                <span>欢迎，{{ employee_name }}</span>
                <el-button
                  @click="logout"
                  link
                  type="danger"
                  style="margin-left: 10px"
                >
                  退出登录
                </el-button>
              </div>
            </div>
            <div class="header-line"></div>
            <!-- <img class="header-line" src="../images/line.png" alt="分割线"> -->
            <!-- 用户信息区域 -->
          </div>
        </el-header>
        <el-main style="width: 100%">
          <slot> </slot>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script setup lang="ts">
import { Setting ,} from "@element-plus/icons-vue";
import { ElMessage } from 'element-plus';

import { useRouter } from "vue-router";
import { ref ,onMounted} from "vue";
import { useRoute } from "vue-router";
import { Session } from '../utils/storage';

const route = useRoute();
const employee_name=ref('')
const index=ref('0')
// onMounted(() => {
//   const storedUsername = localStorage.getItem('username');
//   if (storedUsername) {
//     employee_name.value = storedUsername;
//   } else {
//     // 如果没有用户名，说明未登录或登录已过期
//     // employee_name.warning('请先登录');
//     router.push('/login');
//   }
// });

const onclick = (item,_index) => {
  index.value = _index
  if(item.children.length == 0){
    console.log("直接打开")
    router.push(item.path)
  }else{
    console.log("打开二级菜单")
  }
}

onMounted(()=>{
  const storeEmployeeName = localStorage.getItem('employee_name')
  if(storeEmployeeName){
    employee_name.value =storeEmployeeName
  }
})

const logout = () => {
  // 清除本地存储
  localStorage.removeItem('token');
  // 提示并跳转登录页
  ElMessage.success('退出成功');
  router.push('/login');
};
const menuItems = ref([]);
// const menuItems = ref([
//   {
//     label: "平台管理",
//     path: "/roleList",
//     children: []
//   },
//   {
//     label: "基础系统",
//     path: "/technicalDrainage",
//     children: []
//   },
//   {
//     label: "基础设置",
//     path: "/basicSet",
//     children: []
//   },
//   {
//     label: "合作企业",
//     path: "/cooperativeEnterprise",
//     children: []
//   },
//   {
//     label: "量化率",
//     path: "/quantification",
//     children: []
//   },
// ]);
const selectedItem = ref(null);
const isActive = ref(false); // 默认不激活
const selectedButton = ref(null); // 用来存储当前选中的按钮标识
const isActiveMenu = (item: { path: string }) => {
  return route.path.startsWith(item.path);
};
function selectButton(page) {
  selectedButton.value = page; // 更新选中的按钮标识
  router.push({ name: page }); // 跳转到对应的页面，确保你的路由配置中有对应的name属性
}
// 选择处理
const selectItem = (item) => {
  selectedItem.value = item;
  // 这里可以添加点击后的业务逻辑
};

onMounted(() => {
  menuItems.value = Session.get("menuList");
})

const router = useRouter();
</script>
<style lang="scss" scoped>
.common-layout {
  position: fixed;
  width: 100% ;
  display: flex;
  height: 100%;
  .active {
    background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
    color: black; /* 确保文字颜色在背景色上清晰可见 */
  }
  @media (max-width: 768px) {
    .layout-container {
      flex-direction: column;
    }
    .sidebar1,
    .sidebar {
      position: fixed;
      width: 100%;
      height: auto;
    }
  }
  .sidebar {
    position: fixed;
    --active-bg: #2c3e50;
    --active-color: #ffffff;
    --hover-bg: #e9ecef;

    width: 220px;
    // background: #f8f9fa;
    height: 100vh;
    // border-right: 1px solid #dee2e6;

    .sidebar2 {
      list-style: none;
      padding: 20px 0;
      margin: 0;

      li {
        position: relative;
        transition: background-color 0.3s ease;

        a {
          display: block;
          // padding: 12px 24px;
          color: #2c3e50;
          text-decoration: none;
          transition: color 0.3s ease;
        }

        &:hover {
          background-color: var(--hover-bg);
        }

        &.active1 {
          background-color: var(--active-bg);
          border-left: 4px solid #3498db;

          a {
            color: var(--active-color);
            font-weight: 500;
          }
        }
      }
    }
  }
  .aside {
    border-right: 1px solid black;
    background: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2; /* 确保侧边栏在背景图上方 */

    .asideTitle {
      background-color: #d1d9f7;
      width: 263px;
      height: 204px;
      border-bottom: 5px solid #0525f7;
      .CompanyName {
        font-size: 50px;
        height: 100px;
        margin-left: 15px;
        margin-top: 47px;

      }
    }
    .sidebar3 li {
      height: 80px;
      width: 183px;
      text-align: center;
      line-height: 60px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid #000;
      margin-top: 20px;
      // background-color: red;
    }
    el-menu {
      border-right: none;
    }
    .sidebar3 li.active a {
      color: white;
    }
    .sidebar3,
    .sidebar2 {
      list-style: none;
      padding: 0;
      margin: 0;
      height: 100%;
      overflow-y: auto;
    }
    .el-menu-vertical-demo {
      border-right: none;
    }
    /* 高亮样式 */
    .el-menu-vertical-demo .el-menu-item.is-active {
      background-color: #3a5bde !important;
      color: white;

      &:hover {
        background-color: #2c4ac7 !important;
      }
    }
    .sidebar3 li {
      color: #000;
      padding: 10px 25px;
      margin-bottom: 5px;
      border-radius: 4px;
      transition: background 0.3s;
    }

    .menu-container {
      // background-color: red ;
      width: 100%;
      padding: 30px;
      position: relative;
      z-index: 2;
      color: #000;

      .menu-button1,
      .menu-button,
      .menu-button4 {
        display: flex;
        align-items: center;
        justify-content: center;
        // color:#000;
        border: 1px solid #000;
        padding: 12px 20px;
        border-radius: 6px;
        cursor: pointer;
        // background-color: #3a5bde;
        transition: all 0.3s;
      }

      .menu-button1 {
        margin-top: 0;
      }
      .menu-button4 {
        margin-top: 63px;
        border-radius: 15px;
      }
      .menu-button {
        margin-top: 63px;

        &:hover {
          background: #2c4ac7;
        }
      }
    }
  }

  .el-header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0;
    display: block;
    height: 126px;
    position: relative;
    z-index: 2; /* 确保头部在背景图上方 */

    .header-content {
      display: flex;
      flex-direction: column;

      .header-top {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 10px 33px 10px 0;

        .setting-icon {
          font-size: 20px;
          color: #606266;
          cursor: pointer;

          &:hover {
            color: #3a5bde;
          }
        }
      }

      .header-line {
        width: 100%;
        height: 81px;
        background-color: #d2e0fb;
      }
    }
  }

  .el-main {
    padding: 0;
  }
  .active {
    background-color: blue; /* 或者其他高亮颜色 */
    color: white; /* 文字颜色 */
  }
}
</style>