<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '全部客户') router.push('/allCustomer')
  if(item === '资格申请') router.push('/eligibilityApply')
  if(item === '广告') router.push('/ad')
  if(item === '技术引流') router.push('/allCustomertechnicalDrainage')
}

const buttonList = [
  '全部客户', '资格申请', '广告', '技术引流'
]

// 表格数据
const tableData = ref([
  {
    id: 'AD001',
    title: '双十一大促活动',
    status: '进行中',
    createTime: '2023-10-15'
  },
  {
    id: 'AD002',
    title: '春节特惠活动',
    status: '已结束',
    createTime: '2023-01-10'
  },
  {
    id: 'AD003',
    title: '新用户注册礼包',
    status: '待审核',
    createTime: '2023-11-01'
  }
])

const searchForm = ref({
  adTitle: ''
})

const deleteAd = (id) => {
  console.log('删除广告:', id)
  // 实际项目中这里应该是调用API删除
  tableData.value = tableData.value.filter(item => item.id !== id)
}

const addAd = () => {
  router.push('/addAds')
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="rightBox">
        <div class="head">
          <el-form :model="searchForm" class="search-form">
            <el-form-item>
              <el-input 
                v-model="searchForm.adTitle" 
                placeholder="广告标题" 
                class="search-input"
              />
            </el-form-item>
            <el-button type="primary" class="search-btn">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <div class="action-buttons">
              <el-button type="warning" class="export-btn">
                导出
              </el-button>
              <el-button type="success" class="add-btn" @click="addAd">
                + 添加广告
              </el-button>
            </div>
          </el-form>
        </div>
        <div class="main">
          <div class="table-header">
            <span class="table-title">广告列表</span>
          </div>
          
          <el-table :data="tableData" class="data-table">
            <el-table-column prop="id" label="ID账号" width="300" />
            <el-table-column prop="title" label="标题" width="300" />
            <el-table-column prop="status" label="状态" width="200">
              <template #default="{row}">
                <el-tag :type="row.status === '进行中' ? 'success' : row.status === '已结束' ? 'info' : 'warning'">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="300" />
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="deleteAd(row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.head {
  margin-bottom: 20px;
  
  .search-form {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .search-input {
      width: 300px;
    }
    
    .action-buttons {
      margin-left: auto;
      display: flex;
      flex-direction: column;
      gap: 10px;
      
      .export-btn {
        background-color: #FF8D1A;
      }
      
      .add-btn {
        margin-left: 0px;
        width: 100%;
      }
    }
  }
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-header {
  margin-bottom: 15px;
  
  .table-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
}

.data-table {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  
  :deep(.el-table__cell) {
    .cell {
      padding: 0 10px;
    }
  }
}
.search-btn{
    margin-top: -20px;
}
</style>