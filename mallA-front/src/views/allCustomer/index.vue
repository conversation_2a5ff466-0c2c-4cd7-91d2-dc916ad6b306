<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '全部客户') router.push('/allCustomer')
  if(item === '资格申请') router.push('/eligibilityApply')
  if(item === '广告') router.push('/ad')
  if(item === '技术引流') router.push('/allCustomertechnicalDrainage')
  
}

const buttonList = [
  '全部客户', '资格申请', '广告', '技术引流'
]

// 表格数据
const tableData = ref([
  {
    account: 'user001',
    fans: '87,075',
    idCard: '310*****1990****',
    phone: '138****1234',
    company: '广州中业科技有限公司',
    registerTime: '2022-01-15',
    coupon: '1',
    commission: '55%',
    ip: '************'
  },
  {
    account: 'user002',
    fans: '10,000',
    idCard: '310*****1990****',
    phone: '138****1234',
    company: '广州中南网络科技有限公司',
    registerTime: '2026-01-15',
    coupon: '8',
    commission: '15%',
    ip: '************'
  },
  {
    account: 'user003',
    fans: '999,888,000',
    idCard: '310*****1990****',
    phone: '138****1234',
    company: '腾讯企鹅科技有限公司',
    registerTime: '2001-01-15',
    coupon: '115',
    commission: '25%',
    ip: '***********'
  },
  {
    account: 'user004',
    fans: '85,100,000',
    idCard: '310*****1990****',
    phone: '168****1277',
    company: '字节抖动股份责任制公司',
    registerTime: '2025-01-15',
    coupon: '89',
    commission: '28%',
    ip: '*************'
  },

])

const searchForm = ref({
  creditPhone: '',
  name: '',
  level: '',
  startTime: '',
  endTime: ''
})

const showIp = ref(null)

const toggleIp = (index) => {
  showIp.value = showIp.value === index ? null : index
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="rightBox">
        <div class="head">
          <span class="search">查询</span>
        </div>
        <div class="main">
          <div class="search-box">
            <div class="search-row">
              <el-input 
                v-model="searchForm.creditPhone"
                class="search-input"
                placeholder="企业信用号/手机号"
              />
              <el-input 
                v-model="searchForm.name"
                class="search-input"
                placeholder="姓名"
              />
              <el-button-group class="level-buttons">
                <el-button 
                  :type="searchForm.level === '1' ? 'primary' : ''"
                  @click="searchForm.level = '1'"
                >
                  权限一
                </el-button>
                <el-button 
                  :type="searchForm.level === '2' ? 'primary' : ''"
                  @click="searchForm.level = '2'"
                >
                  权限二
                </el-button>
                <el-button 
                  :type="searchForm.level === '3' ? 'primary' : ''"
                  @click="searchForm.level = '3'"
                >
                  权限三
                </el-button>
              </el-button-group>
            </div>
            <div class="search-row">
              <el-date-picker
                v-model="searchForm.startTime"
                type="date"
                placeholder="注册时间"
                class="date-picker"
              />
              <span class="to-text">至</span>
              <el-date-picker
                v-model="searchForm.endTime"
                type="date"
                placeholder="结束时间"
                class="date-picker"
              />
              <el-button type="primary" class="search-btn">搜索</el-button>
            </div>
          </div>
          
          <div class="table-info">
            <span>列表、总数 ({{ tableData.length }})</span>
          </div>

          <el-table :data="tableData" class="data-table">
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column prop="fans" label="粉丝量" width="120" />
            <el-table-column prop="idCard" label="身份证" width="180" />
            <el-table-column prop="phone" label="企业信用代号/手机号" width="160" />
            <el-table-column prop="company" label="公司名/姓名" width="200" />
            <el-table-column prop="registerTime" label="注册时间" width="120" />
            <el-table-column prop="coupon" label="平台促销券" width="120" />
            <el-table-column prop="commission" label="抵扣金/贷款" width="100" />
            <el-table-column label="操作" width="140">
              <el-button size="small" @click="toggleIp($index)">禁用</el-button>
              <el-button size="small" @click="toggleIp($index)">启用</el-button>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.head {
  margin-bottom: 20px;
  
  .search {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-box {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.search-input {
  width: 200px;
  margin-right: 15px;
}

.level-buttons {
  margin-right: 15px;
}

.date-picker {
  width: 180px;
}

.to-text {
  margin: 0 10px;
  color: #606266;
}

.search-btn {
  margin-left: 15px;
}

.table-info {
  margin-bottom: 15px;
  font-size: 14px;
  color: #909399;
}

.data-table {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.ip-info {
  margin-top: 5px;
  padding: 5px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}
</style>