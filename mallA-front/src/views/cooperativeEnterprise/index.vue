<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Left from "@/components/Left.vue";
import {Session} from "@/utils/storage.js";

const route = useRoute()


// 当前激活菜单项
const openMenus = computed(() => {
  if (route.path.startsWith('/technicalDrainage')) {
    return ['/technicalDrainage']
  }
  return []
})

onMounted(() => {
  const route = useRoute();
  nextTick(() => {
    const _menuList = Session.get("menuList");
    for (let i = 0; i < _menuList.length; i++) {
      const item = _menuList[i];
      if (item.id == route.query.id){
        console.log(item.children)
        menuList.value = item.children
      }
    }
  })
})

const menuList = ref([])
// const menuList = ref([
//   {
//     label: '中南惠',
//     path : '/technicalDrainage/cooperativeEnterprise',
//     children: [
//       {
//         label: '交易数据明细',
//         path : '/transactionData',
//       },
//       {
//         label: '分量设置',
//         path : '/heftSetting',
//       },
//       {
//         label: '引流设置',
//         path : '/technicalDrainageB',
//       },
//       {
//         label: '关系链',
//         path : '/relationshipChainSetting',
//       },
//       {
//         label: '量化数',
//         path : '/quantizer',
//       },
//       {
//         label: '量化值',
//         path : '/quantify',
//       },
//       {
//         label: '核销数据',
//         path : '/writeData',
//       },
//       {
//         label: '量化值进化量',
//         path : '/quantEvolution',
//       },
//       {
//         label: '量化进化量',
//         path : '/creditEvolution',
//       },
//       {
//         label: '授权表',
//         path : '/authorization',
//       },
//     ]
//   },
// ])

// 菜单按钮点击跳转

</script>

<template>
  <HomeBg>
    <el-container style="height: 100vh;">
      <Left :list="menuList"></Left>
<!--      &lt;!&ndash; 左侧菜单 &ndash;&gt;-->
<!--      <el-aside width="180px" class="sub-menu">-->
<!--        <el-menu-->
<!--          :default-active="$route.path"-->
<!--          router-->
<!--          background-color="#fff"-->
<!--          text-color="#666"-->
<!--          active-text-color="#3a5bde"-->
<!--          :default-openeds="openMenus"-->
<!--          class="aside-menu"-->
<!--        >-->
<!--          <el-submenu index="/cooperativeEnterprise">-->
<!--            <template  #title>合作企业</template>-->
<!--            <el-menu-item index="/cooperativeEnterprise/transactionData">中南惠</el-menu-item>-->
<!--            <el-menu-item index="/cooperativeEnterprise/about">about</el-menu-item>-->
<!--          </el-submenu>-->
<!--        </el-menu>-->
<!--      </el-aside>-->

<!--      &lt;!&ndash; 主内容区域 &ndash;&gt;-->
<!--      <el-main class="main-content">-->
<!--        <router-view />-->
<!--      </el-main>-->

    </el-container>
  </HomeBg>
</template>

<style lang="scss" scoped>

/* 去除二级菜单默认边框 */
:deep(.el-submenu .el-menu) {
  border-right: none !important;
  background-color: transparent !important;
}

:deep(.el-submenu__title) {
  background-color: #fff !important;
}

/* 侧边栏菜单样式 */
.sub-menu {
  position: fixed;
  height: 100%;
  border-right: 1px solid #000;
  background-color: #fff;
}

.el-menu-item.is-active {
  background-color: #2c3e50 !important;
  color: #fff !important;
}

.aside-menu {
  border-right: none;
}

/* 主体内容样式 */
.main-content {
  padding: 20px;
  background-color: #f9f9f9;
}

/* 强制让 el-menu-item 内容居中 */
:deep(.el-menu-item) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  font-size: 16px;
}

/* 如果只想对基础系统的子菜单生效，可以加更具体的类名或结构选择器 */
</style>