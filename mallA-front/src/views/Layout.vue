<template>
  <el-container style="height: 100vh;">
    <!-- 左侧一级菜单 -->
    <el-aside width="200px" class="main-menu">
      <div class="asideTitle">
        <h1>玄数量化</h1>
      </div>
      <el-menu
        :default-active="$route.path"
        router
        background-color="#f5f7fa"
        text-color="#333"
        active-text-color="#3a5bde"
      >
        <el-menu-item index="/roleList">平台管理</el-menu-item>
        <el-menu-item index="/technicalDrainage">基础系统</el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 右侧二级菜单 + 页面内容 -->
    <el-container>
      <!-- 右侧二级菜单 -->
      <el-aside width="180px" class="sub-menu">
        <el-menu
          v-if="$route.name && $route.meta.showSubMenu"
          :default-active="$route.path"
          router
          background-color="#fff"
          text-color="#666"
          active-text-color="#3a5bde"
        >
          <el-menu-item index="/technicalDrainage/systemSetting">系统设置</el-menu-item>
          <el-menu-item index="/technicalDrainage/systemSetting">系统更新</el-menu-item>
          <el-menu-item index="/technicalDrainage/stateData">状态数据</el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
// 这里可以添加逻辑控制是否显示二级菜单等
</script>

<style lang="scss" scoped>
.main-menu {
  background-color: #f5f7fa;
  border-right: 1px solid #e4e7ed;
  .asideTitle {
    background-color: #d1d9f7;
    text-align: center;
    padding: 30px 0;
    h1 {
      margin: 0;
      font-size: 20px;
      color: #333;
    }
  }
}
.sub-menu {
  border-right: 1px solid #e4e7ed;
  background-color: #fff;
}
.main-content {
  padding: 20px;
}
</style>