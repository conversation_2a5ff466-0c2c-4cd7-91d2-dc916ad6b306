<template>
  <MerchantBg>
    <div class="container">
      <div class="rightBox">

      </div>
    </div>
  </MerchantBg>
</template>
<script setup>
import MerchantBg from '../../components/MerchantBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
const router = useRouter()
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}
.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}
</style>