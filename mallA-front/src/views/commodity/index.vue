

<template>
  <ManageBg>
    <div class="data-container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="middle-container">
        <div class="mid">
          <!-- 商品名称 -->
          <div class="commBox">
            <span class="Text">商品名称:</span>
            <input class="Text" type="text" placeholder="">
          </div>
          <!-- 商品分类 -->
          <div class="category-row">
            <div class="commBox-small">
              <span class="Text">商品分类</span>
            </div>
            <div class="category-buttons">
              <el-radio-group v-model="categoryType">
                <el-radio-button label="一级分类" class="category-button" />
                <el-radio-button label="二级分类" class="category-button" />
              </el-radio-group>
              <div class="addBtn">
                <button class="addbtn">添加分类+</button>
              </div>
            </div>
          </div>
          <!-- 商品单位 -->
          <div class="commBox">
            <span class="Text">商品单位:</span>
            <input class="Text" type="text" placeholder="">
          </div>
        </div>
        <!-- 商品详情 -->
        <div class="product-pic-container_detail">
          <span class="product-pic-label1">商品详情:</span>
           <el-input
                v-model="textarea"
                :rows="4"
                type="textarea"
                placeholder="请输入商品详情"
            />
        </div>
        <!-- 商品视频 -->
        <div class="product-pic-container">
          <span class="product-pic-label1">商品视频:</span>
            <el-upload
                  class="upload-demo"
                  drag
                  action="https://your-upload-api.com/upload"
                  multiple
                  accept="video/*"
                  :before-upload="beforeUpload"
            >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处 <em>或点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                请上传MP4、MOV等视频格式文件，且不超过100MB
              </div>
            </template>
          </el-upload>
        </div>
         <!-- 上传产品 -->
        <div class="product-pic-container">
            <span class="product-pic-label1">上传产品:</span>
            <el-radio-group v-model="radio" class="ml-4" @change="handleChange">
              <el-radio label="1" size="large">同个价格有不同规格</el-radio>
              <el-radio label="2" size="large">不同规格不同价格</el-radio>
            </el-radio-group>
        </div>
        <div class="commodityList">
                  <el-table
                      :data="tableData"
                      :span-method="objectSpanMethod"
                      border
                      style="width: 100%; margin-top: 20px"
                  >
                    <el-table-column prop="imgSrc" label="图片" width="180">
                        <template #default="scope">
                          <img v-viewer :src="scope.row.imgSrc" width="100" height="100" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="size" label="规格" />
                    <el-table-column prop="praice" label="价格" />
                    <el-table-column prop="amount2" label="库存"/>
                  </el-table>
        </div>
        <div class="product-pic-container1">
          <span class="product-pic-label1">减库存方式:</span>
          <el-radio-group v-model="radio1" class="ml-4">
              <el-radio label="1" size="large">下单减库存</el-radio>
              <el-radio label="2" size="large">付款减库存</el-radio>
          </el-radio-group>
        </div>
        <div style="text-align: center">
              <el-button type="success" style="margin-top: 10px;">提交</el-button>
        </div>
        <div class="product-pic-container1">
          <span class="product-pic-label1">是否代销:</span>
          <el-radio-group v-model="radio2" class="ml-4">
              <el-radio label="1" size="large">是</el-radio>
              <el-radio label="2" size="large">否</el-radio>
          </el-radio-group>
        </div>
        <div class="product-pic-container1">
          <span class="product-pic-label1">代销权限佣金方式:</span>
          <el-radio-group v-model="radio3" class="ml-4">
              <el-radio label="1" size="large">固定金额
                  <el-input 
                    v-model="option1Input" 
                    size="small" 
                    style="width: 120px; margin-left: 8px;"
                    placeholder="请输入">
                  </el-input>￥
              </el-radio>
              <el-radio label="2" size="large">按金额比例
                  <el-input 
                    v-model="option1Input" 
                    size="small" 
                    style="width: 120px; margin-left: 8px;"
                    placeholder="请输入">
                  </el-input>%
              </el-radio>
          </el-radio-group>
        </div>
        <div class="product-pic-container1">
          <span class="product-pic-label1">是否上架:</span>
          <el-radio-group v-model="radio4" class="ml-4">
              <el-radio label="1" size="large">是</el-radio>
              <el-radio label="2" size="large">否</el-radio>
          </el-radio-group>
        </div>
        <div class="">
          <div style="text-align: center">
              <el-button type="success" style="margin-top: 10px;">提交</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 详情页面 -->
    <el-dialog v-model="dialogDetailVisible" title="Shipping address" width="800">
        <el-table :data="gridData">
          <el-table-column property="date" label="Date" width="150" />
          <el-table-column property="name" label="Name" width="200" />
          <el-table-column property="address" label="Address" />
        </el-table>
    </el-dialog>
    <!-- 编辑页面 -->
    <el-dialog v-model="dialogEditVisible" title="Shipping address" width="800">
        <el-table :data="gridData">
          <el-table-column property="date" label="Date" width="150" />
          <el-table-column property="name" label="Name" width="200" />
          <el-table-column property="address" label="Address" />
        </el-table>
    </el-dialog>
    <!-- 新增页面 -->
    <el-dialog v-model="dialogAddVisible" title="新增" width="500">
        <el-form :model="form">
          <el-form-item label="图片" :label-width=" formLabelWidth">
            <el-upload action="#" list-type="picture-card" :auto-upload="false" :limit="1">
                <el-icon><Plus /></el-icon>
                <template #file="{ file }">
                  <div>
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <span class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file)"
                      >
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span
                        v-if="!disabled"
                        class="el-upload-list__item-delete"
                        @click="handleRemove(file)"
                      >
                        <el-icon><Delete /></el-icon>
                      </span>
                    </span>
                  </div>
                </template>
            </el-upload>
          </el-form-item>
          <!-- 同个价格有不同规格 -->
          <div class="" v-if="radio==='1'">
              <el-form-item label="规格" :label-width="formLabelWidth">
                <el-input
                  v-model="inputValue"
                  @keyup.enter="addTag"
                  @blur="addTag"
                  placeholder="输入后按回车添加标签"
                />
                <div class="tags">
                  <el-tag
                    v-for="(tag, index) in tags"
                    :key="index"
                    closable
                    @close="removeTag(index)"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </el-form-item>
              <el-form-item label="价格" :label-width="formLabelWidth">
                <el-input-number v-model="form.price" :precision="2" :step="0.1" :max="10" />
              </el-form-item>
          </div>
          <!-- 不同规格不同价格 -->
          <div class="" v-else>
                <el-button @click="addRow">新增规格</el-button>
                <el-table :data="tableData1" style="width: 100%">
                  <el-table-column prop="size" label="规格" width="180">
                    <template #default="{ row, $index }">
                      <el-input v-model="row.size" placeholder="请输入"/>
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" label="价格" width="180">
                    <template #default="{ row, $index }">
                      <el-input-number v-model="row.price" :precision="2" :step="0.1" :max="10" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="{ $index }">
                      <el-button @click="removeRow($index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
          </div>
          <el-form-item label="库存" :label-width="formLabelWidth">
            <el-input-number v-model="form.inventory" :precision="0"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogAddVisibleReturn">返回</el-button>
            <el-button type="primary" @click="dialogAddVisibleConfirm">确定</el-button>
          </div>
        </template>
  </el-dialog>
  </ManageBg>
</template>
<script setup lang="ts">
import ManageBg from '../../components/ManageBg.vue'
import { ref,reactive } from 'vue'
import {useRouter} from 'vue-router'
import { Delete, Download, ZoomIn, Plus} from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
const router = useRouter()
const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址', '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]
const inputValue = ref('');
const tags = ref([]);

const addTag = () => {
  if (inputValue.value && !tags.value.includes(inputValue.value)) {
    tags.value.push(inputValue.value);
    inputValue.value = '';
  }
};

const removeTag = (index) => {
  tags.value.splice(index, 1);
};
const handleButtonClick = (item) =>{
   if(item === '商品列表'){
    router.push('/productList')
  }
  if(item === '发布商品'){
    router.push('/commodity')
  }
  if(item === '商品分类'){
    router.push('/productCategory')
  }
  if(item === '品牌管理'){
    router.push('/brandManage')
  }
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
  if(item === '商品代销申请') router.push('./productSellApply')
}
  const categoryType = ref('一级分类') // 分类单选按钮值
  const paymentReduceStock = ref(false)
//const url ='https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'
  const addPic = () => {
  // 添加图片的逻辑
  console.log('添加图片')
  }
  const dialogImageUrl = ref('')
  const dialogVisible = ref(false)

  //删除
  const handleRemove = (file: UploadFile) => {
    console.log(file)
  }
  const handlePictureCardPreview = (file: UploadFile) => {
      dialogImageUrl.value = file.url!
      dialogVisible.value = true
  }
  const handleChange=(value)=>{
      console.log('选中的值:', value);
      dialogAddVisible.value = true;
  }
  const num = ref(1)
  const inventoryNum=ref(1)
  const radio=ref('')
  const radio1 = ref('1')
  const radio2=ref('1')
  const radio3=ref('1')
  const radio4=ref('1')
  const protocol=ref('1')
  const textarea=ref('')
  const dialogEditVisible=ref(false)
  const dialogDetailVisible = ref(false)
  //新增数据弹窗
  const dialogAddVisible = ref(false)
  const dialogAddVisibleHandle = () => {
      dialogAddVisible.value = true;
  };
  const dialogAddVisibleReturn=()=>{
      dialogAddVisible.value = false;
  }
  const dialogAddVisibleConfirm=()=>{
      console.log(form,'form')
      // tableData.push
      tableData.push({
        price: form.price,
        inventory: form.inventory
      });
      dialogAddVisible.value = false;
      console.log(tableData,'tableData')
  }
  const tableData1 = ref([
        { size: '', price: '' }
  ]);

const addRow = () => {
  tableData1.value.push({ size: '', price: '' });
};

const removeRow = (index) => {
  tableData1.value.splice(index, 1);
};
  const form = reactive({
    imageUrl:'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    price: 0,
    inventory:0
})
const tableData= [
  {
    imgSrc:'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    size: 's',
    amount1: '234',
    amount2: '3',
    amount3: 10,
  },
  {
    imgSrc:'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    size: 'l',
    amount1: '165',
    amount2: '4',
    amount3: 12,
  },
  {
    imgSrc:'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    size: 's',
    amount1: '324',
    amount2: '1',
    amount3: 9,
  },
  {
    imgSrc:'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    size: 's',
    amount1: '621',
    amount2: '2',
    amount3: 17,
  },
  {
    imgSrc:'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    size: 's',
    amount1: '539',
    amount2: '4',
    amount3: 15,
  },
]
const objectSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  if (columnIndex === 0) {
    if (rowIndex % 2 === 0) {
      return {
        rowspan: 2,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.data-container {
  position: relative;
  display: flex;
  // height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.mid {
  margin-top: 10px;
  margin-left: 38px;
}

.middle-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  position: relative;
}
.productDetail{
  background-color: #CCCCCC;
  width: 323px;
  height: 41px;
  font-size: 24px;
  margin-left: 40px;
  margin-bottom:19px;
  display:flex;
  .Text{
    margin-left: 13px;
  }
}
.commBox {
  background-color: #CCCCCC;
  width: 659px;
  height: 41px;
  display: flex;
  align-items: center;
  font-size: 24px;
  margin-bottom: 32px;
  
  .Text {
    margin-left: 13px;
    
    &:first-child {
      min-width: 120px;
    }
  }
  
  input {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 24px;
  }
}

.category-row {
  display: flex;
  margin-bottom: 32px;
}

.commBox-small {
  width: 144px;
  height: 41px;
  background-color: #CCCCCC;
  font-size: 24px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  .Text {
    margin-left: -18px;
  }
}

.category-buttons {
  display: flex;
  align-items: center;
  .addBtn{
    margin-left: 98px;
    background-color: #3A58CF;
    font-size: 20px;
    width: 152px;
    height: 41px;
    display: flex;
    align-items: center;
    justify-content: center;
    .addbtn{
      color:#fff; 
    }
  }
  :deep(.el-radio-group) {
    display: flex;
    gap: 10px;
  }
  
  :deep(.el-radio-button) {
    &.category-button {
      width: 120px;
      height: 41px;
      background-color: #CCCCCC;
      border-radius: 0;
      border: none;
      
      .el-radio-button__inner {
        width: 100%;
        height: 100%;
        background: transparent;
        border: none;
        color: #333;
        font-size: 24px;
      }
      
      &.is-active {
        background-color: #3A58CF;
        
        .el-radio-button__inner {
          color: white;
          box-shadow: none;
        }
      }
    }
  }
}

.product-pic-container {
  display: flex;
  align-items: center;
  margin-left: 38px;
  margin-bottom: 32px;
  height: 160px;
  .product-pic-label1 {
    width: 20%;
    font-size: 24px;
  }
  .ml-3{
    width: 56px;
  }
  .ml-4{
    padding-left: 10px;
  }
}
.product-pic-container_detail{
  display: flex;
  align-items: center;
  margin-left: 38px;
  margin-bottom: 32px;
  height: 160px;
  .product-pic-label1 { 
    width: 20%;
    font-size: 24px;
  }
    :deep(.el-textarea) {
    width: 50% !important;
    border-color: #1a38af;
  }
}
.product-pic-container1 {
  display: flex;
  align-items: center;
  margin-left: 38px;
  margin-bottom: 32px;
    .product-pic-label1 {
    width: 20%;
    font-size: 24px;
  }
  .ml-3{
    width: 56px;
  }
  .ml-4{
    padding-left: 10px;
  }
}

.w-50{
  width: 30%;
  border: 1px solid red;
}
//清除input框中默认的上下箭头
.productPrice{
  margin-left: 15px;
  background-color: #CCCCCC;
  width: 530px;
  height: 41px;
   &[type="number"] {
    -moz-appearance: textfield;
    
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

.priceText{
  margin-left: 15px;
}
.media-buttons {
  display: flex;
  width: 659px;
  justify-content: space-between;
  margin-bottom: 32px;

  .media-button {
    width: 210px; 
    height: 80px;
    background: #F5F7FA;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #3A58CF;
      background: rgba(58, 88, 207, 0.05);
    }

    span {
      font-size: 18px;
      color: #606266;
      margin-bottom: 8px;
    }

    img {
      width: 24px;
      height: 24px;
    }
  }
}

input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
  
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none; /* Safari和Chrome */
    margin: 0;
  }
}
.commodityList{
    margin-left: 38px;
    margin-bottom: 32px;
}

</style>