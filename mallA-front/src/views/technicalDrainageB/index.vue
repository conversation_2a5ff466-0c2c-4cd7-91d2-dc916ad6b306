<script setup>
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addCitationSettings, getCitationSettings } from '@/api/technicalDrainageB'

const router = useRouter()

const form = ref({
  enabled: '0',
  citationValue: 0
})

onMounted(async () => {
  try {
    const res = await getCitationSettings()
    if (res.code === 200 && res.data) {
      form.value = {
        enabled: res.data.isEnabled || '0',
        citationValue: res.data.citationValue
      }
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage.error('网络错误，请重试')
  }
})

const submit = async () => {
  try {
    const payload = {
      isEnabled: form.value.enabled,
      citationValue: String(form.value.citationValue)
    }
    const res = await addCitationSettings(payload)
    if (res.code === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage.error('网络错误，请重试')
  }
}
</script>

<template>
  <div class="page-container">
    <div class="content-wrapper">
      <div class="page-header">
        <h1>引流设置</h1>
      </div>

      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>引流参数设置</span>
          </div>
        </template>

        <div class="setting-item">
          <el-radio-group v-model="form.enabled">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-row">
          <div class="setting-item">
            <span class="setting-label">单次引流</span>
            <el-input
              class="setting-input"
              placeholder="请您输入引流值"
              v-model="form.citationValue"
            />
          </div>
          <div class="setting-item">
            <el-button type="primary" @click="submit">保存</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  height: 24px;
  
  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border-radius: 8px;
  margin-bottom: 24px;
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-card__body {
    padding: 24px;
  }
}

.setting-card {
  .setting-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 24px;
    margin-top: 24px;
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .setting-label {
    margin-right: 12px;
    font-size: 14px;
    color: #262626;
    width: 80px;
  }
  
  .setting-input {
    width: 220px;
  }
}
</style>