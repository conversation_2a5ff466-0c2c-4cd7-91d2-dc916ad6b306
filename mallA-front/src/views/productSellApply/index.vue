<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="rightBox">
        <div class="head">
          <div class="product-row">
            <span class="pro">权限一</span>
            <span class="price">
              价格:<span class="original-price">￥100</span>/月
            </span>
            <!-- <button class="open-btn" @click="openPermissionOne(1)">开通</button> -->
            <button class="open-btn-gray">已开通</button>
          </div>
          <div class="arrows">
            <span class="longString">
                <span class="longStringBottom">V</span>
            </span>
          </div>
          <div class="product-row">
            <span class="pro">权限二</span>
            <span class="price">
              价格:<span class="original-price">￥200</span>/月
            </span>
            <button class="open-btn" @click="openPermissionTwo(2)">开通</button>
          </div>
          <div class="arrows">
            <span class="longString">
                <span class="longStringBottom">V</span>
            </span>
          </div>
          <div class="product-row">
            <span class="pro">权限三</span>
            <span class="price">
              价格:<span class="original-price">￥300</span>/月
            </span>
            <button class="open-btn" @click="openPermissionThree(3)">开通</button>
          </div>
        </div>
        <div class="bottom">
          <span class="pay">实付金额:￥50</span>
          <div class="payment-section">
            <button class="sure-btn">确认协议并支付
              <div class="agreement-check">
                <input type="checkbox" id="agreement" v-model="agreed">
                <label for="agreement">我已阅读并同意<a href="/service-agreement" class="agreement-link">《服务协议》</a></label>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </ManageBg>
</template>
<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
  if(item === '商品代销申请') router.push('./productSellApply')
}

const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址', '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]
const openPermissionOne=(item)=>{
    console.log(item,'item')
}
const openPermissionTwo=(item)=>{
    console.log(item,'item')
}
const openPermissionThree=(item)=>{
    console.log(item,'item')
}
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.rightBox {
  width: 1343px;
  height: 664px;
  margin-top: 37px;
  margin-left: 44px;
  border: 1px solid #3A58CF;
  display: flex;
  flex-direction: column;
}

.head {
  width: 100%;
  padding: 0 20px;
  margin-top: 37px; /* 添加顶部间距 */
}

.product-row {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-top: 37px; /* 添加顶部间距 */
}

.pro {
  font-size: 30px;
  color: #fff;
  background-color: #3A58CF;
  padding: 10px 20px;
  border-radius: 4px;
  min-width: 344px;
  text-align: center;
}
.arrows{
  margin-top: 10px;
  width: 344px;
  text-align: center;
}
.longString{
  width: 2px;
  height: 30px;
  background-color: #b5b5b5;
  display: inline-block;
  position: relative;
}
.longStringBottom{
  color: #b5b5b5;
  display: block;
  position: absolute;
  top: 22px;
  left: -4px;
}
.open-btn-gray{
  width: 142px;
  height: 52px;
  background-color: #b5b5b5;
  color: #fff;
  font-size: 30px;
  border: none;
  border-radius: 4px;
}
.price, .discount-price {
  font-size: 24px;
  color: #fff;
  background-color: #3A58CF;
  padding: 10px 20px;
  border-radius: 4px;
  white-space: nowrap;
}

.original-price {
  text-decoration: line-through;
}

.open-btn {
  width: 142px;
  height: 52px;
  background-color: #3A58CF;
  color: #fff;
  font-size: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  &:hover {
    background-color: #2a48bf;
  }
}

.bottom {
  width: 100%;
  height: 266px;
  margin-top: 39px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;

  .pay {
    font-size: 24px;
    font-weight: bold;
  }

  .payment-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .sure-btn {
    width: 267px;
    height: 85px;
    background-color: #FF8D1A;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 24px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #e67e17;
    }
  }

  .agreement-check {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    margin-left: 16px;
    .agreement-link {
      color: #3A58CF;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>