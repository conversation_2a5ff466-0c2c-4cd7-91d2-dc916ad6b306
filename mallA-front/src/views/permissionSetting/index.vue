<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const form = ref({
  openStatus: 'open', // 开启/关闭状态
  permission1: {
    originalPrice: '',
    discountPrice: ''
  },
  permission2: {
    originalPrice: '',
    discountPrice: ''
  },
  permission3: {
    originalPrice: '',
    discountPrice: ''
  }
})

const value = ref([])
const options = [
  {
    value: 'register',
    label: '注册'
  },
  {
    value: 'merchant',
    label: '商家'
  },
  {
    value: 'payment',
    label: '支付'
  }
]

const handleButtonClick = (item) => {
  if(item === '平台协议') router.push('/platformAgreement')
  if(item === '技术引流') router.push('/technicalDrainage')
  if(item === '权限设置') router.push('/permissionSetting')
  if(item === '关系链设置') router.push('/relationshipChainSetting')
  if(item === '分量设置') router.push('/heftSetting')
  if(item === '发票设置') router.push('/')
}

const buttonList = [
  '平台协议', '技术引流', '权限设置', '关系链设置', '分量设置','发票设置'
]
</script>

<template>
  <HomeBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <span class="fixed-title">权限设置</span>
        
        <div class="main-content">
          <!-- 开启/关闭单选框 -->
          <div class="toggle-section">
            <el-radio-group v-model="form.openStatus">
              <el-radio label="open" size="large">
                <span class="toggle-label">开启</span>
              </el-radio>
              <el-radio label="close" size="large">
                <span class="toggle-label">关闭</span>
              </el-radio>
            </el-radio-group>
          </div>
          
          <!-- 代销权限按钮区域 -->
          <div class="permission-buttons">
            <div class="permission-item">
              <el-button type="primary" class="permission-btn">代销权限一</el-button>
              <span class="product-desc">一个商品</span>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="permission-item">
              <el-button type="primary" class="permission-btn">代销权限二</el-button>
              <span class="product-desc">二个商品</span>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="permission-item">
              <el-button type="primary" class="permission-btn">代销权限三</el-button>
              <span class="product-desc">三个商品</span>
            </div>
          </div>
          
          <!-- 权限价格表单区域 -->
         <div class="price-form">
            <div class="price-row">
              <span class="price-label">权限一原价：</span>
              <el-input v-model="form.permission1.originalPrice" class="price-input" />
              <span class="price-unit">元/月</span>
              <span class="price-label">优惠：</span>
              <el-input v-model="form.permission1.discountPrice" class="price-input" />
              <span class="price-unit">折扣/月</span>
            </div>
            
            <div class="down-arrow">↓</div>
            
            <div class="price-row">
              <span class="price-label">权限二原价：</span>
              <el-input v-model="form.permission2.originalPrice" class="price-input" />
              <span class="price-unit">元/月</span>
              <span class="price-label">优惠：</span>
              <el-input v-model="form.permission2.discountPrice" class="price-input" />
              <span class="price-unit">折扣/月</span>
            </div>
            
            <div class="down-arrow">↓</div>
            
            <div class="price-row">
              <span class="price-label">权限三原价：</span>
              <el-input v-model="form.permission3.originalPrice" class="price-input" />
              <span class="price-unit">元/月</span>
              <span class="price-label">优惠：</span>
              <el-input v-model="form.permission3.discountPrice" class="price-input" />
              <span class="price-unit">折扣/月</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </HomeBg>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  position: relative;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  flex-shrink: 0;
  position: relative;
  z-index: 1;

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.right-content {
  flex: 1;
  
  position: relative;
}

.fixed-title {
  position: absolute;
  left: 20px;
  top: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  z-index: 2;
}

.main-content {
  margin-left: 225px;
  width: calc(100% - 40px);
  height: 600px;
  margin-top: 60px;
  max-width: 800px;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  
}

.toggle-section {
  margin-bottom: 30px;
  
  :deep(.el-radio) {
    margin-right: 30px;
    
    .el-radio__label {
      font-size: 16px;
    }
  }
}

.permission-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
}

.permission-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.permission-btn {
  width: 120px;
  height: 50px;
  margin-bottom: 10px;
  background-color: #3A58CF;
  border: none;
  
  &:hover {
    background-color: #2a48bf;
  }
}

.product-desc {
  font-size: 14px;
  color: #666;
}

.arrow {
  font-size: 24px;
  color: #999;
  margin: 0 30px;
  padding-top: 20px;
}

.price-form {
  width: 100%;
}

.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.price-label {
  width: 100px;
  font-size: 16px;
  color: #333;
}

.price-input {
  width: 120px;
  margin: 0 10px;
}

.price-unit {
  width: 60px;
  font-size: 14px;
  color: #666;
}
</style>