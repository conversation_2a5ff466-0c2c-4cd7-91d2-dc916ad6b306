<script setup>
import HomeBg from "../../components/HomeBg.vue";
import { ref, onMounted } from "vue";

import Sidebar from "../../components/Sidebar.vue";
import {
    Search,
    Refresh,
    Phone
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getMallBUsers, updateMallBUsersStatus } from "../../api/relationshipChain/index";
import { getErrorMessage } from "../../utils/errorHandler";


const form = ref({
    globalStatus: "open", // 全局开启/关闭状态
    permissionId: "", // 手机号搜索
    businessLicense: "", // 营业执照搜索
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 统计数据
const statisticsData = ref({
    normal: 0,      // 正常用户
    disabled: 0,    // 禁用用户
    invalid: 0,     // 失效用户
    sleeping: 0,    // 睡眠用户
    ineffective: 0  // 无效用户
});

// 获取统计数据
const getStatisticsData = async () => {
    try {
        // 获取全部数据用于统计（不分页）
        const params = {
            phone: form.value.permissionId || "",
            businessLicense: form.value.businessLicense || "",
            pageNum: 1,
            pageSize: 999999 // 设置一个很大的数字来获取全部数据
        };

        const response = await getMallBUsers(params);

        if (response.code === 200) {
            const allData = response.data.list || [];

            // 统计各种状态的用户数量
            statisticsData.value = {
                normal: allData.filter(item => item.status === '正常').length,
                disabled: allData.filter(item => item.status === '禁用').length,
                invalid: allData.filter(item => item.status === '失效').length,
                sleeping: allData.filter(item => item.status === '睡眠').length,
                ineffective: allData.filter(item => item.status === '无效').length
            };
        }
    } catch (error) {
        console.error('获取统计数据失败:', error);
        // 统计数据获取失败时不显示错误，保持默认值
    }
};

// 获取用户列表数据
const getUserList = async () => {
    loading.value = true;
    try {
        const params = {
            phone: form.value.permissionId || "",
            businessLicense: form.value.businessLicense || "",
            pageNum: currentPage.value,
            pageSize: pageSize.value
        };

        const response = await getMallBUsers(params);

        if (response.code === 200) {
            const data = response.data;
            tableData.value = data.list.map(item => ({
                ...item,
                // 根据后端status字段映射操作状态：
                // 当状态为'正常'时，下拉框选择'normal'
                // 当状态为'禁用'时，下拉框选择'disabled'
                // 当状态为其他值时，下拉框默认选择'normal'
                operationStatus: item.status === '正常' ? 'normal' :
                               item.status === '禁用' ? 'disabled' : 'normal',
                // 初始化更新状态标志
                updating: false
            }));
            total.value = data.total;
        } else {
            ElMessage.error(response.message || '获取数据失败');
        }
    } catch (error) {
        console.error('获取用户列表失败:', error);
        const errorMessage = getErrorMessage(error, '获取数据失败');
        ElMessage.error(errorMessage);
    } finally {
        loading.value = false;
    }
};

// 处理分页变化
const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
    getUserList();
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    getUserList();
};


const handleSearch = () => {
    currentPage.value = 1;
    getUserList();
    getStatisticsData(); // 搜索时也要更新统计数据
};

// 重置搜索
const handleReset = () => {
    form.value.permissionId = "";
    form.value.businessLicense = "";
    currentPage.value = 1;
    getUserList();
    getStatisticsData(); // 重置时也要更新统计数据
};

// 处理操作状态变化
const handleOperationChange = async (row) => {
    // 防止重复操作，添加loading状态
    if (row.updating) return;
    row.updating = true;

    try {
        // 将前端状态值转换为后端需要的格式：0=正常，1=禁用
        const status = row.operationStatus === 'normal' ? '0' : '1';

        const params = {
            id: row.id,
            status: status
        };

        const response = await updateMallBUsersStatus(params);

        if (response.code === 200) {
            ElMessage.success('状态更新成功');
            console.log(`用户 ${row.username} 的状态已更新为: ${row.operationStatus}`);
            // 状态更新成功后刷新列表数据和统计数据
            await getUserList();
            await getStatisticsData();
        } else {
            ElMessage.error(response.message || '状态更新失败');
            // 如果更新失败，恢复原来的状态
            row.operationStatus = row.operationStatus === 'normal' ? 'disabled' : 'normal';
        }
    } catch (error) {
        console.error('更新用户状态失败:', error);
        const errorMessage = getErrorMessage(error, '状态更新失败');
        ElMessage.error(errorMessage);
        // 如果更新失败，恢复原来的状态
        row.operationStatus = row.operationStatus === 'normal' ? 'disabled' : 'normal';
    } finally {
        // 清除loading状态
        row.updating = false;
    }
};


// 获取状态标签类型的辅助函数
const getStatusTagType = (status) => {
    switch (status) {
        case '正常': return 'success';
        case '禁用': return 'danger';
        case '失效': return 'info';
        case '睡眠': return 'warning';
        case '无效': return 'info';
        default: return 'info';
    }
};

// 页面加载时获取数据
onMounted(() => {
    getUserList();
    getStatisticsData(); // 页面加载时获取统计数据
});
</script>

<template>
    <!-- <HomeBg> -->
    <div class="container">
        <!--    <Sidebar></Sidebar>-->
        <!--    <div class="left-buttons">-->
        <!--      <el-button-->
        <!--        v-for="(item, index) in buttonList"-->
        <!--        :key="index"-->
        <!--        class="data-button"-->
        <!--        @click="handleButtonClick(item)"-->
        <!--      >-->
        <!--        {{ item }}-->
        <!--      </el-button>-->
        <!--    </div>-->

        <div class="right-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <div class="header-content">
                    <div class="title-section">
                        <h1 class="page-title">用户关系链信息</h1>
                    </div>
                    <div class="header-stats" style="margin-right: 30%;">
                        <div class="stat-item">
                            <div class="stat-number">{{ total }}</div>
                            <div class="stat-label">总用户数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ statisticsData.normal }}</div>
                            <div class="stat-label">正常用户</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ statisticsData.disabled }}</div>
                            <div class="stat-label">禁用用户</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ statisticsData.invalid }}</div>
                            <div class="stat-label">失效用户</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ statisticsData.sleeping }}</div>
                            <div class="stat-label">睡眠用户</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ statisticsData.ineffective }}</div>
                            <div class="stat-label">无效用户</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
                <el-card class="search-card" shadow="never">
                    <el-form :model="form" :inline="true" class="search-form">
                        <el-form-item label="手机号" class="search-item">
                            <el-input v-model="form.permissionId" placeholder="请输入手机号" class="search-input" clearable
                                @keyup.enter="handleSearch">
                                <template #prefix>
                                    <el-icon>
                                        <Phone />
                                    </el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="营业执照" class="search-item">
                            <el-input v-model="form.businessLicense" placeholder="请输入营业执照" class="search-input" clearable
                                @keyup.enter="handleSearch">
                            </el-input>
                        </el-form-item>
                        <el-form-item class="search-buttons">
                            <el-button type="primary" @click="handleSearch" :icon="Search">
                                搜索
                            </el-button>
                            <el-button @click="handleReset" :icon="Refresh">
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>

            <!-- 表格区域 -->
            <div class="table-section">
                <el-card class="table-card" shadow="never">
                    <div class="table-container">
                        <el-table :data="tableData" stripe style="width: 100%"
                            :header-cell-style="{ background: '#fafafa', color: '#606266' }" v-loading="loading"
                            :scroll-x="true">
                            <el-table-column prop="phone" label="手机号" min-width="135" align="center" />
                            <el-table-column prop="businessLicense" label="企业社会信用代码" min-width="200" align="center">
                                <template #default="scope">
                                    <span>{{ scope.row.businessLicense || '-' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="parentUsername" label="邀请人" min-width="135" align="center">
                                <template #default="scope">
                                    <span>{{ scope.row.parentUsername || '-' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" min-width="100" align="center">
                                <template #default="scope">
                                    <el-tag :type="getStatusTagType(scope.row.status)" size="small"
                                        :class="`status-${scope.row.status}`">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="flag" label="达标状态" min-width="130" align="center">
                                <template #default="scope">
                                    <el-tag :type="scope.row.flag.includes('达标') ? 'success' : 'info'" size="small"
                                        :class="`flag-${scope.row.flag}`">
                                        {{ scope.row.flag }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="deductionMoneyLimit" label="上限额度" min-width="120" align="center">
                                <template #default="scope">
                                    <span style="color: #409eff; font-weight: 500;">¥{{ scope.row.deductionMoneyLimit
                                        }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="注册时间" min-width="180" align="center" />
                            <el-table-column prop="jurisdiction" label="权限" min-width="130" align="center">
                                <template #default="scope">
                                    <el-tag
                                        :type="scope.row.jurisdiction.includes('权限一') ? 'warning' : scope.row.jurisdiction.includes('权限3') ? 'danger' : ''"
                                        size="small" :class="`jurisdiction-${scope.row.jurisdiction}`">
                                        {{ scope.row.jurisdiction }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="username" label="姓名" min-width="135" align="center" />
                            <el-table-column prop="fans" label="粉丝量" min-width="100" align="center">
                                <template #default="scope">
                                    <span style="color: #67c23a; font-weight: 500;">{{ scope.row.fans }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="address" label="地址" min-width="200" align="center" />
                            <el-table-column prop="loginAddress" label="定位" min-width="200" align="center" />

                            <el-table-column label="操作" width="200" align="center" fixed="right">
                                <template #default="scope">
                                    <el-select v-model="scope.row.operationStatus" placeholder="请选择" size="small"
                                        @change="handleOperationChange(scope.row)"
                                        popper-class="operation-select-dropdown" :loading="scope.row.updating"
                                        :disabled="scope.row.updating">
                                        <el-option label="正常" value="normal" />
                                        <el-option label="禁用" value="disabled" />
                                    </el-select>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-card>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[5, 10, 20, 50]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                />
            </div>
        </div>
    </div>

    <!-- </HomeBg> -->
</template>

<style lang="scss" scoped>
.container {
    display: flex;
    width: 100%;
    min-height: 100vh;
    box-sizing: border-box;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.right-content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 24px;
    }

    .title-section {
        flex: 1;
    }

    .page-title {
        font-size: 32px;
        font-weight: 700;
        margin: 0 0 8px 0;
        line-height: 1.2;
        background: linear-gradient(135deg, #fff 0%, #f1f5f9 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-description {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        line-height: 1.5;
    }

    .header-stats {
        display: flex;
        gap: 32px;
        align-items: center;
    }

    .stat-item {
        text-align: center;
        padding: 16px 24px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: white;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
}

/* 搜索区域 */
.search-section {
    .search-card {
        border: none;
        border-radius: 16px;
        background: white;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        :deep(.el-card__body) {
            padding: 32px;
        }
    }

    .search-form {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        .search-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;

            :deep(.el-form-item__label) {
                font-weight: 500;
                color: #374151;
                font-size: 14px;
                line-height: 32px;
                height: 32px;
                margin: 0;
                padding-right: 12px;
            }

            :deep(.el-form-item__content) {
                display: flex;
                align-items: center;
            }
        }

        .search-input {
            width: 280px;

            :deep(.el-input__wrapper) {
                border-radius: 8px;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                border: 1px solid #d1d5db;
                height: 32px;
                line-height: 32px;

                &:hover {
                    border-color: #9ca3af;
                }

                &.is-focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            }
        }

        .search-buttons {
            margin-bottom: 0;
            display: flex;
            gap: 12px;

            .el-button {
                border-radius: 8px;
                font-weight: 500;
                padding: 10px 20px;

                &.el-button--primary {
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                    border: none;
                    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);

                    &:hover {
                        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
                    }
                }

                &:not(.el-button--primary) {
                    background: #ffffff;
                    border: 1px solid #d1d5db;
                    color: #374151;

                    &:hover {
                        background: #f9fafb;
                        border-color: #9ca3af;
                    }
                }
            }
        }
    }
}

/* 表格区域 */
.table-section {
    flex: 1;

    .table-card {
        border: none;
        border-radius: 16px;
        height: 100%;
        background: white;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        :deep(.el-card__header) {
            padding: 24px 32px;
            border-bottom: 1px solid #f1f5f9;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 16px 16px 0 0;
        }

        :deep(.el-card__body) {
            padding: 0;
            height: calc(100% - 73px);
            display: flex;
            flex-direction: column;
        }
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .table-actions {
            display: flex;
            gap: 12px;

            .el-button {
                border-radius: 8px;
                font-weight: 500;

                &.el-button--primary {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border: none;

                    &:hover {
                        background: linear-gradient(135deg, #059669 0%, #047857 100%);
                    }
                }
            }
        }
    }

    .table-container {
        flex: 1;
        overflow: auto;
        padding: 0 32px 32px;
        position: relative;
    }
}

/* 表格样式优化 */
:deep(.el-table) {
    border-radius: 0 0 12px 12px;
    overflow: visible;

    .el-table__header-wrapper {
        .el-table__header {
            th {
                background: #f8fafc;
                color: #374151;
                font-weight: 600;
                font-size: 14px;
                border-bottom: 1px solid #e5e7eb;
                padding: 16px 12px;
            }
        }
    }

    .el-table__body-wrapper {
        .el-table__body {
            tr {
                &:hover {
                    background-color: #f8fafc;
                }

                td {
                    padding: 16px 12px;
                    border-bottom: 1px solid #f3f4f6;
                    font-size: 14px;
                    color: #374151;
                }
            }
        }
    }

    .el-table__fixed-right {
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);

        .el-table__fixed-body-wrapper {
            background: #fff;
        }
    }

    .el-table__fixed-right-patch {
        background: #fff;
    }

    .el-button {
        border-radius: 6px;
        font-size: 12px;
        padding: 6px 12px;
        font-weight: 500;

        &.el-button--primary {
            background: #3b82f6;
            border: none;

            &:hover {
                background: #2563eb;
            }
        }
    }

    .el-tag {
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        border: none;
        padding: 4px 12px;
        letter-spacing: 0.5px;

        &.el-tag--success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        &.el-tag--danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        &.el-tag--warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        &.el-tag--info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        &.status-正常 {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        &.status-禁用 {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        &.status-失效 {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
        }

        &.status-睡眠 {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }

        &.status-无效 {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
        }

        &.flag-达标一 {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
        }

        &.flag-达标二 {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        &.flag-未达标 {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        &.jurisdiction-权限一 {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        &.jurisdiction-权限二 {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }

        &.jurisdiction-权限三 {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }
    }

    .el-select {
        width: 140px;

        :deep(.el-input__wrapper) {
            border-radius: 6px;
            border: 1px solid #d1d5db;

            &:hover {
                border-color: #9ca3af;
            }

            &.is-focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            }
        }
    }
}



.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-start;
}
</style>