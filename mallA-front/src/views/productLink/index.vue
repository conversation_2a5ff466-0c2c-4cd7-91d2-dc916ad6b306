<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import {ref} from 'vue'
const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
  if(item === '商品代销申请') router.push('./productSellApply')
}

const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址', '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]

// 表格数据
const tableData = ref([
  {
    account: '***********',
    phone: '***********',
    linkCount: 1,
    productName: '商品1'
  },
  // 可以添加更多示例数据
])
</script>

<template>
    <ManageBg>
        <div class="container">
            <div class="left-buttons">
                <el-button 
                    v-for="(item, index) in buttonList" 
                    :key="index"
                    class="data-button"
                    @click="handleButtonClick(item)"
                >
                    {{ item }}
                </el-button>
            </div>
            
            <div class="content-area">
                <div class="headBox">
                    <div class="search-content">
                        <div class="search-area">
                            <div class="search-row">
                                <div class="search-item">
                                    <span class="label">账号</span>
                                    <input 
                                        class="number-input" 
                                        type="text" 
                                        pattern="[0-9]*" 
                                        inputmode="numeric"
                                        placeholder="请输入账号"
                                    >
                                </div>
                                <div class="search-item">
                                    <span class="label">手机号</span>
                                    <input 
                                        class="number-input" 
                                        type="text" 
                                        pattern="[0-9]*" 
                                        inputmode="numeric"
                                        placeholder="请输入手机号"
                                    >
                                </div>
                            </div>
                            <div class="search-row">
                                <div class="search-item">
                                    <span class="label">开始时间</span>
                                    <input class="date-input" type="date">
                                    <span class="to">至</span>
                                    <span class="label">结束时间</span>
                                    <input class="date-input" type="date">
                                </div>
                            </div>
                        </div>
                        <div class="search-button">
                            <el-button type="primary" :icon="Search" class="custom-search-btn" />
                        </div>
                    </div>
                </div>
                
                <div class="main">
                    <el-table :data="tableData" style="width: 100%">
                        <el-table-column prop="account" label="账号" width="270" />
                        <el-table-column prop="phone" label="手机号" width="270" />
                        <el-table-column prop="linkCount" label="商品链接数量" width="270" />
                        <el-table-column prop="productName" label="商品名称" width="270" />
                        <el-table-column label="操作" width="264">
                            <template #default>
                                <el-button size="small">禁用</el-button>
                                <el-button size="small">启用</el-button>
                                <el-button size="small" type="danger">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </ManageBg>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.content-area {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.headBox {
    width: 1347px;
    height: 290px;
    border: 3px solid #3A58CF;
    border-radius: 8px;
    margin-bottom: 20px;
    position: relative;
    
    .search-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        padding: 20px;
        
        .search-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            
            .search-row {
                display: flex;
                gap: 20px;
                justify-content: center;
                
                .search-item {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    
                    .label {
                        font-size: 18px;
                        min-width: 60px;
                    }
                    
                    .number-input {
                        width: 500px;
                        height: 52px;
                        border: 1px solid #dcdfe6;
                        border-radius: 4px;
                        padding: 0 15px;
                        font-size: 16px;
                        
                        &::-webkit-outer-spin-button,
                        &::-webkit-inner-spin-button {
                            -webkit-appearance: none;
                            margin: 0;
                        }
                        -moz-appearance: textfield;
                    }
                    
                    .date-input {
                        width: 500px;
                        height: 52px;
                        border: 1px solid #dcdfe6;
                        border-radius: 4px;
                        padding: 0 15px;
                        font-size: 16px;
                    }
                    
                    .to {
                        margin: 0 10px;
                        font-size: 16px;
                    }
                }
            }
        }
        
        .search-button {
          margin-top: 20px;
            margin-left: 1220px;
            right: 20px;
            bottom: 20px;
            
            :deep(.custom-search-btn) {
                width: 46px;
                height: 46px;
                padding: 0;
                
                .el-icon {
                    width: 24px;
                    height: 24px;
                    font-size: 24px;
                }
                
                span {
                    display: none;
                }
            }
        }
    }
}

.main {
    width: 1344px;
    height: 527px;
    border: 1px solid #3A58CF;
    border-radius: 8px;
    overflow: hidden;
    
    :deep(.el-table) {
        height: 100%;
        
        th {
            background-color: #3A58CF !important;
            color: white;
            height: 66px;
            font-size: 18px;
        }
        
        td {
            height: 60px;
            font-size: 16px;
        }
        
        .el-button {
            margin-right: 5px;
            &:last-child {
                margin-right: 0;
            }
        }
    }
}
</style>