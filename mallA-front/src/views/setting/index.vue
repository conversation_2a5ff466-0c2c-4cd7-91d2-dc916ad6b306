<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { Lock, Plus, TrendCharts, Message } from '@element-plus/icons-vue'

const router = useRouter()
const handleToChangePassword = () => {
  router.push('/')
}

const menuItems = [
  { icon: Lock, text: '修改密码' },
  { icon: Plus, text: '商家系统续租' },
 { img: new URL('../../images/Analytics.png', import.meta.url).href, text: '睡眠状态' },
  { icon: Message, text: '短信验证码' },
  { img: new URL('../../images/sleep.png', import.meta.url).href, text: '睡眠状态' },
  { img: new URL('../../images/shop.png', import.meta.url).href, text: '商家引流量' }
]
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left">
        <div class="leftBox">
          <span class="title">基础设置</span>
        </div>
      </div>
      <div class="rightBox">
        <ul>
          <li v-for="(item, index) in menuItems" :key="index" @click="handleToChangePassword">
            <span class="icon">
              <component :is="item.icon" v-if="item.icon" />
              <img v-else :src="item.img" :alt="item.text">
            </span>
            <span class="text">{{ item.text }}</span>
          </li>
        </ul>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  height: 100vh;
  
  .left {
    width: 163px;
    height: 100%;
    background-color: #3A58CF;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .leftBox {
      color: #fff;
      font-size: 40px;
      writing-mode: vertical-rl;
      letter-spacing: 20px;
      text-orientation: upright;
      line-height: 1.2;
    }
  }
  
  .rightBox {
    flex: 1;
    padding: 40px;
    background: linear-gradient(to bottom, 
      rgba(42, 145, 219, 0.1) 0%, 
      rgba(42, 145, 219, 0.05) 100%);
    
    ul {
      list-style: none;
      padding: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;
      
      li {
        margin-left: 9px;
        width: 1264px;
        height: 108px;
        display: flex;
        align-items: center;
        padding: 0 30px;
        background: linear-gradient(to right, 
          #3A58CF 0%, 
          rgba(58, 88, 207, 0.7) 50%, 
          rgba(58, 88, 207, 0.3) 100%);
        border-radius: 12px;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .icon {
          width: 69px;
          height: 69px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 30px;
          
          .el-icon {
            width: 100%;
            height: 100%;
            color: white;
          }
          
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        
        .text {
          font-size: 36px;
          color: #000;
          font-weight: 500;
        }
      }
    }
  }
}
</style>

