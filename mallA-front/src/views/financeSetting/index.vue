<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue';

const centerDialogVisible = ref(false)
const router = useRouter()
const voucherOption = ref('on') // 补贴券选项
const promotionOption = ref('on') // 促销券选项
const wechatSettlementOption = ref('on') // 微信结算选项
const alipaySettlementOption = ref('on') // 支付宝结算选项
const bankSettlementOption = ref('on') // 银行卡结算选项
const wechatMaxAmount = ref('0.00') // 微信单笔最高金额
const alipayMaxAmount = ref('0.00') // 支付宝单笔最高金额
const bankMaxAmount = ref('0.00') // 银行卡单笔最高金额
const settlementFeeRate = ref('') // 结算手续费率
const settlementAmountLimit = ref('0.00') // 结算金额限制
const wechatDailyLimit = ref(1) // 微信每日次数
const alipayDailyLimit = ref(1) // 支付宝每日次数
const bankDailyLimit = ref(1) // 银行卡每日次数

const buttonList = [
  '设置', '平台补贴券明细', '平台促销券明细', '抵扣金明细', '货款明细',
  '已核销平台补贴券明细', '结算统计', '结算记录',
  '广告收益', '结算审核', '量化值进化量明细', '量化进化量明细'
]

const handleButtonClick = (item) => {
  if (item === '设置') router.push('./financeSetting')
  if (item === '平台补贴券明细') router.push('./platformVoucherDetail')
  if (item === '平台促销券明细') router.push('./platformCouponDetail')
  if (item === '抵扣金明细') router.push('./commissionDetail')
  if (item === '货款明细') router.push('./loanDetail')
  if (item === '已核销平台补贴券明细') router.push('./verifyVoucherDetail')
  if (item === '结算统计') router.push('./settlementCount')
  if (item === '结算记录') router.push('./settlementRecord')
  if (item === '广告收益') router.push('./')
  if (item === '结算审核') router.push('./settlementReview')
  if (item === '量化值进化量明细') router.push('./quantifyEvolutionDetail')
  if (item === '量化进化量明细') router.push('./creditEvolutionDetail')
}
const saveSettings = () => {
  console.log('保存设置', {
    voucherOption: voucherOption.value,
    promotionOption: promotionOption.value,
    wechatSettlementOption: wechatSettlementOption.value,
    alipaySettlementOption: alipaySettlementOption.value,
    bankSettlementOption: bankSettlementOption.value,
    wechatMaxAmount: wechatMaxAmount.value,
    alipayMaxAmount: alipayMaxAmount.value,
    bankMaxAmount: bankMaxAmount.value,
    settlementFeeRate: settlementFeeRate.value,
    settlementAmountLimit: settlementAmountLimit.value,
    wechatDailyLimit: wechatDailyLimit.value,
    alipayDailyLimit: alipayDailyLimit.value,
    bankDailyLimit: bankDailyLimit.value
  });
  centerDialogVisible.value = true; // 显示成功对话框
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <div class="coupon-item">
          <span class="coupon-label">补贴券</span>
          <div class="radio-group">
            <label class="radio-option">
              <input 
                type="radio" 
                name="voucher" 
                value="on" 
                v-model="voucherOption"
                class="square-radio"
              />
              <span>开启</span>
            </label>
            
            <label class="radio-option">
              <input 
                type="radio" 
                name="voucher" 
                value="off" 
                v-model="voucherOption"
                class="square-radio"
              />
              <span>关闭</span>
            </label>
          </div>
        </div>
        
        <div class="coupon-item">
          <span class="coupon-label">促销券赠送</span>
          <div class="radio-group">
            <label class="radio-option">
              <input 
                type="radio" 
                name="promotion" 
                value="on" 
                v-model="promotionOption"
                class="square-radio"
              />
              <span>开启</span>
            </label>
            
            <label class="radio-option">
              <input 
                type="radio" 
                name="promotion" 
                value="off" 
                v-model="promotionOption"
                class="square-radio"
              />
              <span>关闭</span>
            </label>
          </div>
        </div>

        <div class="settlement-item">
          <div class="settlement-header">
            <span class="settlement-label">手动结算到微信</span>
            <div class="radio-group">
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="wechat" 
                  value="on" 
                  v-model="wechatSettlementOption"
                  class="square-radio"
                />
                <span>开启</span>
              </label>
              
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="wechat" 
                  value="off" 
                  v-model="wechatSettlementOption"
                  class="square-radio"
                />
                <span>关闭</span>
              </label>
            </div>
          </div>
          
          <div class="amount-setting">
            <div class="daily-limit">
              <span>每日</span>
              <input 
                type="number" 
                v-model.number="wechatDailyLimit" 
                class="daily-limit-input"
                min="1"
              />
              <span>次</span>
            </div>
            <span class="max-amount-label">单笔最高金额</span>
            <div class="amount-input">
              <input 
                type="text" 
                v-model="wechatMaxAmount" 
                class="amount-field"
                placeholder="0.00"
              />
              <span class="currency">￥</span>
            </div>
          </div>
        </div>

        <div class="settlement-item">
          <div class="settlement-header">
            <span class="settlement-label">手动结算到支付宝</span>
            <div class="radio-group">
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="alipay" 
                  value="on" 
                  v-model="alipaySettlementOption"
                  class="square-radio"
                />
                <span>开启</span>
              </label>
              
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="alipay" 
                  value="off" 
                  v-model="alipaySettlementOption"
                  class="square-radio"
                />
                <span>关闭</span>
              </label>
            </div>
          </div>
          
          <div class="amount-setting">
            <div class="daily-limit">
              <span>每日</span>
              <input 
                type="number" 
                v-model.number="alipayDailyLimit" 
                class="daily-limit-input"
                min="1"
              />
              <span>次</span>
            </div>
            <span class="max-amount-label">单笔最高金额</span>
            <div class="amount-input">
              <input 
                type="text" 
                v-model="alipayMaxAmount" 
                class="amount-field"
                placeholder="0.00"
              />
              <span class="currency">￥</span>
            </div>
          </div>
        </div>

        <div class="settlement-item">
          <div class="settlement-header">
            <span class="settlement-label">手动结算到银行卡</span>
            <div class="radio-group">
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="bank" 
                  value="on" 
                  v-model="bankSettlementOption"
                  class="square-radio"
                />
                <span>开启</span>
              </label>
              
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="bank" 
                  value="off" 
                  v-model="bankSettlementOption"
                  class="square-radio"
                />
                <span>关闭</span>
              </label>
            </div>
          </div>
          
          <div class="amount-setting">
            <div class="daily-limit">
              <span>每日</span>
              <input 
                type="number" 
                v-model.number="bankDailyLimit" 
                class="daily-limit-input"
                min="1"
              />
              <span>次</span>
            </div>
            <span class="max-amount-label">单笔最高金额</span>
            <div class="amount-input">
              <input 
                type="text" 
                v-model="bankMaxAmount" 
                class="amount-field"
                placeholder="0.00"
              />
              <span class="currency">￥</span>
            </div>
          </div>
        </div>

        <!-- 结算手续费模块 -->
        <div class="fee-settings-item">
          <div class="fee-setting">
            <span class="fee-label">结算手续费</span>
            <div class="fee-input">
              <input 
                type="text" 
                v-model="settlementFeeRate" 
                class="fee-field"
                placeholder=""
              />
              <span class="percent">%</span>
            </div>
          </div>

          <div class="amount-limit-setting">
            <span class="amount-limit-label">结算金额限制</span>
            <div class="amount-input">
              <input 
                type="text" 
                v-model="settlementAmountLimit" 
                class="amount-field"
                placeholder="0.00"
              />
              <span class="currency">￥</span>
            </div>
          </div>
        </div>

        <!-- 居中的保存按钮 -->
        <div class="save-button-container">
          <el-button 
            type="primary" 
            @click="saveSettings"
            class="save-btn"
          >
            提交
          </el-button>
        </div>
      </div>
    </div>

    <!-- 提交成功对话框 -->
    <el-dialog
      v-model="centerDialogVisible"
      title="提示"
      width="500"
      align-center
    >
      <span>提交成功</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="centerDialogVisible = false">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </ManageBg>    
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.coupon-item, .settlement-item, .fee-settings-item {
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.coupon-item {
  align-items: center;
  flex-direction: row;
  
  .coupon-label {
    font-size: 24px;
    font-weight: 500;
    margin-right: 20px;
    min-width: 120px;
  }
}

.settlement-item {
  gap: 15px;
}

.settlement-header {
  display: flex;
  align-items: center;
  
  .settlement-label {
    font-size: 24px;
    font-weight: 500;
    margin-right: 20px;
    min-width: 160px;
  }
}

.amount-setting {
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin-left: 180px;
  
  .daily-limit {
    display: flex;
    align-items: center;
    margin-right: 30px;
    font-size: 16px;
    color: #666;

    .daily-limit-input {
      width: 50px;
      padding: 5px;
      margin: 0 5px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      text-align: center;
      
      &:focus {
        border-color: #3A58CF;
        outline: none;
      }
    }
  }
  
  .max-amount-label {
    margin-right: 10px;
    font-size: 16px;
    color: #666;
  }
  
  .amount-input {
    position: relative;
    display: flex;
    align-items: center;
    
    .amount-field {
      width: 100px;
      padding: 8px 10px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      text-align: right;
      padding-right: 25px;
      
      &:focus {
        border-color: #3A58CF;
        outline: none;
      }
    }
    
    .currency {
      position: absolute;
      right: 10px;
      color: #666;
    }
  }
}

.fee-settings-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.fee-setting, .amount-limit-setting {
  display: flex;
  align-items: center;
  padding: 10px 0;
  
  .fee-label, .amount-limit-label {
    font-size: 16px;
    color: #666;
    min-width: 120px;
    margin-right: 20px;
  }
  
  .fee-input, .amount-input {
    position: relative;
    display: flex;
    align-items: center;
    
    .fee-field, .amount-field {
      width: 100px;
      padding: 8px 10px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      text-align: right;
      padding-right: 25px;
      
      &:focus {
        border-color: #3A58CF;
        outline: none;
      }
    }
    
    .percent, .currency {
      position: absolute;
      right: 10px;
      color: #666;
    }
  }
}

.save-button-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;

  .save-btn {
    padding: 12px 40px;
    font-size: 16px;
    background-color: #3A58CF;
    border: none;

    &:hover {
      background-color: #2a48bf;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.radio-group {
  display: flex;
  gap: 15px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 隐藏原生单选框 */
.square-radio {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
}

/* 选中状态样式 */
.square-radio:checked {
  background-color: #3A58CF;
  border-color: #3A58CF;
}

/* 选中时的内部标记 */
.square-radio:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
</style>