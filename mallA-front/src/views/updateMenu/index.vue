<template>
    <HomeBg>
      <div class="container">
      <img class="bg" src="../../images/bigBackground.png" alt="背景图">
      <div class="form-container">
        <el-form :model="state.ruleForm">
          <el-form-item label="" prop="menuName">
            <div class="input-group">
                <el-input 
                    class="form-input" 
                    v-model="state.ruleForm.menuName" 
                    placeholder="菜单名称" 
                />
            </div>
          </el-form-item>
          <el-form-item label="" prop="parent">
            <div class="input-group">
                <el-tree-select
                  v-model="state.ruleForm.parent"
                  :data="state.parentData"
                  check-strictly
                  :render-after-expand="false"
                  style="width: 240px"
                />
            </div>
          </el-form-item>
          <el-form-item label="" prop="menuType">
            <div class="input-group">
              <el-input 
                class="form-input" 
                v-model="state.ruleForm.menuType" 
                placeholder="权限标识" 
              />
            </div>
          </el-form-item>
          <el-form-item label="" prop="component">
            <div class="input-group">
              <el-input 
                class="form-input" 
                v-model="state.ruleForm.component" 
                placeholder="组件路径"
              />
            </div>
          </el-form-item>
        </el-form>
        <div class="save-button-container">
          <el-button class="save-button" @click="handleToNormalManager">保存</el-button>
        </div>
      </div>
        </div>
    </HomeBg>
</template>
<script setup>
import HomeBg from '../../components/HomeBg.vue'
import {ref,reactive} from 'vue'
import {useRouter} from 'vue-router'
import {platformMenu} from '../../stores/platformMenu'
// import { Session } from './storage';
import {Session} from '../../utils/storage'
    const router = useRouter();
    const state = reactive({
      ruleForm: {
          parentId: 51,
          parent:"一级菜单",
          menuName: "测试",
          path: "/yser",
          component: "/user/index",
          menuType: "C",
          remark: "cillum Lorem laboris"
      },
      parentData:[
      ]
    });
    const handleToNormalManager = async() => {
        router.push('/menuManager')
      try {
        let result=await platformMenu().SaveMenu(state.ruleForm); // 调用存储菜单方法
      } finally {
        //alert("系统繁忙请联系管理员");
      }
  }
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  .form-container {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    width: 100%;
    max-width: 1200px;

    .input-group {
      display: flex;
      align-items: center;
      margin-bottom: 61px;
      width: 100%;
      margin-left: 399px;

      .form-input {
        width: 805px;
        :deep(.el-input__wrapper) {
          height: 53px;
          font-size: 16px;
        }
      }
      :deep(.el-select__wrapper){
          height: 53px;
      }
      .reserved-text {
        margin-left: 55px;
        color: #FF8D1A;
        font-size: 16px;
        white-space: nowrap;
      }
    }
.save-button-container {
      width: 805px;
      margin: 0 auto;
      text-align: center;
      
      .save-button {
        width: 120px;
        height: 40px;
        background-color: #14097A;
        color: white;
        border: none;
        font-size: 16px;
        border-radius: 4px;
        
        &:hover {
          background-color: #1a0da0;
        }
        
        &:active {
          background-color: #0f0657;
        }
      }
    }
  }
}
</style>