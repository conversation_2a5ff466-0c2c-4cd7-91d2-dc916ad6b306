<script lang="ts" setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref, reactive } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  id: '',
  phone: '',
  incomeExpense: '',
  startTime: '',
  endTime: '',
  searchText: ''
})

// 表格数据 - 更新数据结构以匹配新标题
const tableData = ref([
  {
    settlementDate: '2025-11-4 16:30',
    bankCard: '500.00',
    wechatPay: '300.00',
    alipay: '200.00',
    total: '1000.00'
  },
  
])

const onSubmit = () => {
  console.log('提交表单:', form)
}





const router = useRouter()
const buttonList = [
  '设置', '平台补贴券明细', '平台促销券明细', '抵扣金明细', '货款明细',
  '已核销平台补贴券明细', '结算统计', '结算记录',
  '广告收益', '结算审核', '量化值进化量明细', '量化进化量明细'
]

const handleButtonClick = (item) => {
  if (item === '设置') router.push('./financeSetting')
  if (item === '平台补贴券明细') router.push('./platformVoucherDetail')
  if (item === '平台促销券明细') router.push('./platformCouponDetail')
  if (item === '抵扣金明细') router.push('./commissionDetail')
  if (item === '货款明细') router.push('./loanDetail')
  if (item === '已核销平台补贴券明细') router.push('./verifyVoucherDetail')
  if (item === '结算统计') router.push('./settlementCount')
  if (item === '结算记录') router.push('./settlementRecord')
  if (item === '广告收益') router.push('./')
  if (item === '结算审核') router.push('./settlementReview')
  if (item === '量化值进化量明细') router.push('./quantifyEvolutionDetail')
  if (item === '量化进化量明细') router.push('./creditEvolutionDetail')
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <div class="filter-container">
          <div class="filter-header">
            <span class="filter-title">时间表</span>
          </div>
          
          <el-form :model="form" class="filter-form">
            <div class="form-row">
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="form.startTime"
                  type="datetime"
                  placeholder="选择开始时间"
                />
              </el-form-item>
              
              <span class="time-separator">至</span>
              
              <el-form-item label="结束时间">
                <el-date-picker
                  v-model="form.endTime"
                  type="datetime"
                  placeholder="选择结束时间"
                />
              </el-form-item>
              <el-form-item class="search-item">
                <el-input 
                  v-model="form.searchText" 
                  placeholder="搜索" 
                  class="search-input"
                >
                  <template #append>
                    <el-button :icon="Search" />
                  </template>
                </el-input>
              </el-form-item>
            </div>
          </el-form>
          
          <!-- 分隔线 -->
          <el-divider />
        </div>
        
        <!-- 表格区域 - 更新列标题 -->
        <div class="table-container">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="settlementDate" label="结算日期" width="320" />
            <el-table-column prop="bankCard" label="银行卡" width="230" />
            <el-table-column prop="wechatPay" label="微信" width="230" />
            <el-table-column prop="alipay" label="支付宝" width="230" />
            <el-table-column prop="total" label="总计" width="350" />
          
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>    
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #3A58CF;
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .filter-title {
    font-size: 24px;
    font-weight: bold;
    color: white;
  }

  .search-item {
    margin-left: auto;
    
    :deep(.el-input-group__append) {
      background-color: #3A58CF;
      border: none;
      
      .el-button {
        color: white;
      }
    }
  }
}

.filter-form {
  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 0;
      
      :deep(.el-form-item__label) {
        padding-bottom: 0;
        font-weight: normal;
      }
    }
    
    .time-separator {
      margin: 0 10px;
      color: #666;
    }
    
    .export-btn {
      margin-left: 20px;
      background-color: #FF8D1A;
    }
  }
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  flex: 1;
  
  :deep(.el-table) {
    font-size: 14px;
    
    th {
      background-color: #f5f7fa;
      color: #333;
      font-weight: bold;
    }
    
    .cell {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}

:deep(.shelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  background-color: pink;
  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.unshelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  margin-left: 8px;
  margin-left: 0px;
  background-color: cyan;
  &:hover {
    transform: translateY(-1px);
  }
}
</style>