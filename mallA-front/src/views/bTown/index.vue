<template>
  <div class="container">
    <div class="content">
      <el-button
          type="primary"
          icon="ArrowLeft"
          @click="handleBack"
      >返回授权表</el-button>
      <div class="content-top">
        <h1>授权表</h1>
      </div>

      <!-- 表格区域 -->
      <div class="content-footer">
        <el-table :data="tableData" border style="width: 100%" v-loading="loading">
          <el-table-column prop="phone" label="手机号" style="width: 20%" />
          <el-table-column prop="username" label="姓名" style="width: 20%" />
          <el-table-column prop="areaName" label="授权镇、街" style="width: 20%" />
          <el-table-column label="时间" style="width: 20%">
            <template #default="scope">
              {{ scope.row.startTime }} ~ {{ scope.row.endTime }}
            </template>
          </el-table-column>
         <!-- <el-table-column prop="value" label="量化值" style="width: 15%" /> -->
          <el-table-column label="操作" style="width: 15%">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @current-change="handlePageChange"
            @size-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { queryAreaAuthorize, deleteAreaAuthorize } from '../../api/transactionData/index'
import { getErrorMessage } from '../../utils/errorHandler'

const router = useRouter()

// 表格数据
const tableData = ref([])
// 加载状态
const loading = ref(false)
// 当前页码
const currentPage = ref(1)
// 每页条数
const pageSize = ref(10)
// 总数据量
const total = ref(0)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      type: 'B',
      level: '4', // 镇、街级
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    const response = await queryAreaAuthorize(params)

    if (response.code === 200) {
      tableData.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      // 去掉"暂无数据"的错误提示，让表格自然显示空状态
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    // 如果是"暂无数据"的情况，不显示错误提示
    const errorMessage = getErrorMessage(error, '获取数据失败')
    if (errorMessage !== '暂无数据') {
      ElMessage.error(errorMessage)
    }
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handlePageChange = () => {
  fetchData()
}

// 处理删除
const handleDelete = async (row: any) => {
  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除用户 ${row.username} 的授权记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 执行删除
    const response = await deleteAreaAuthorize({ id: row.id })
    
    if (response.code === 200) {
      ElMessage.success('删除成功！')
      // 重新获取数据
      fetchData()
    } else {
      ElMessage.error(response.message || '删除失败！')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      const errorMessage = getErrorMessage(error, '删除失败')
      ElMessage.error(errorMessage)
    }
  }
}

// 返回事件
const handleBack = () => {
  router.push('/transactionData?id=4')
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script><style lang="scss" scoped>
.container {
height: 100vh;
width: 100vw;
//   display: flex;
//   justify-content: center;
//   align-items: center;
background-color: #f0f0f0;
}

.content {
width: 80%;   
height: 100%;
//   max-width: 1000px;
background-color: white;
color: #333;
padding: 20px;
box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
border-radius: 8px;
}
:deep(.el-table thead tr th) {
background-color: #f5f7fa !important;
font-size: 18px;
font-weight: bold;
color: #333;
}
:deep(.el-table .el-table__row) {
height: 70px; // 设置每行高度
}
:deep(.el-table .el-table__cell) {
font-size: 16px; // 字体大小

}
.content-top {
  width: 80%;
font-size: 24px;
text-align: center;
margin-bottom: 20px;
}
.el-table {

  .el-table__row {
    height: 80px; // 设置每行高度为 80px
  }

  .el-table__cell {
    padding: 0;
    font-size: 16px; // 表格字体大小
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.content-footer {
  width: 80%;
//    margin-left: 50px;
margin-top: 20px;
}
.el-table__row{
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.el-table ,.cell {
  box-sizing: border-box;
  line-height: 23px;
  /* overflow: hidden; */
  overflow-wrap: break-word;
  padding: 0 12px;
  text-overflow: ellipsis;
  white-space: normal;
  display: flex;
  justify-content: center;
}
.el-table__body-wrapper{
  height: 800px;
}
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-start;
}
</style>
