<script setup>
import ManageBg from '../../components/ManageBg.vue'
import {useRouter} from 'vue-router'
import { ref } from 'vue'

const value = ref('')

const options = [
  {
    value: 'product1',
    label: '商品1',
  },
  {
    value: 'product2',
    label: '商品2',
  }
]
const router = useRouter()
const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址',  '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]
const handleButtonClick = (item) =>{
  if(item === '商品列表'){
    router.push('/productList')
  }
  if(item === '发布商品'){
    router.push('/commodity')
  }
  if(item === '商品分类'){
    router.push('/productCategory')
  }
  if(item === '品牌管理'){
    router.push('/brandManage')
  }
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
   if(item === '退货地址') router.push('./returnAddress')
   if(item === '商品链接') router.push('./productLink')
   if(item === '商品链接生成') router.push('./buildProductLink')
   if(item === '商品链接导入') router.push('./productLinkImport')
   if(item === '商品代销申请') router.push('./productSellApply')
}
const tableData = ref([
  {
    date: '2016-05-03',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    time:"2025-5-9",
    status:"正常",
    zip: 'CA 90036',
    selected: false
  },

  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    time:"2025-5-9",
    status:"正常",
    zip: 'CA 90036',
    selected: false
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    time:"2025-5-9",
    status:"正常",
    zip: 'CA 90036',
    selected: false
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    time:"2025-5-9",
    status:"正常",
    zip: 'CA 90036',
    selected: false
  }
  
])
</script>

<template>
    <ManageBg>
        <div class="container">
             <div class="left-buttons">
             <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="header">
        <button class="putaway">上架中(5)</button>
        <button class="soldOut">下架(7)</button>
        <button class="allProduct">全部商品(78)</button>
      </div>
      <div class="main">
        <div class="filter-row">
          <el-select
            v-model="value"
            placeholder="请选择在售中"
            size="large"
            class="product-selector"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="commBox">
            <span class="Text">账号</span>
            <input class="Text" type="text" placeholder="">
          </div>
           <div class="filter-row1">
          <el-select
            v-model="value"
            placeholder="请选择分类"
            size="large"
            class="product-selector1"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
           </div>
           <div class="filter-row1">
          <el-select
            v-model="value"
            placeholder="请选择分类"
            size="large"
            class="product-selector1"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
           </div>
           <div class="brandBox">
              <button class="brand">品牌</button>
           </div>
           
        </div>
        <div class="priceBox">
          <text class="pirceRange">价格区间</text>
          <input class="leftPrice" placeholder="">
          <text class="to">至</text>
          <input class="rightPrice" placeholder="">
          <button class="nullBtn"></button>
        </div>
        <div class="product-type-row">
                    <span class="product-type-label">商品类型</span>
                    <div class="checkbox-container">
                        <el-checkbox class="square-checkbox" label="新品" />
                        <el-checkbox class="square-checkbox" label="热卖" />
                    </div>
                    <el-button class="export-btn" type="primary">导出</el-button>
        </div>
          <el-divider/>
        <div class="allButton">
          <button class="button">批量上架</button>
          <button class="button">批量下架</button>
          <button class="button">批量删除</button>
          </div>
           <div class="table-container">
                    <el-table 
                        :data="tableData" 
                        style="width: 100%" 
                        height="340"
                        header-row-class-name="custom-header"
                        row-class-name="custom-row"
                    >
                        <el-table-column width="150">
                            <template #header>
                                <el-checkbox class="select-all-checkbox">全选</el-checkbox>
                            </template>
                            <template #default="{ row }">
                                <el-checkbox v-model="row.selected" class="row-checkbox">
                                    选择
                                </el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="账号" width="120" />
                        <el-table-column prop="state" label="商品" width="120" />
                        <el-table-column prop="city" label="价格" width="320" />
                        <el-table-column prop="address" label="库存" width="200" />
                        <el-table-column prop="time" label="审核时间" width="200" />
                        <el-table-column prop="status" label="商品状态" width="200" />
                        <el-table-column label="操作">
                            <template #default>
                                <el-button link type="primary" size="small">编辑</el-button>
                                <el-button link type="danger" size="small">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
          
        </div>
      </div>

      <div class="bottom">
      
        </div>
    </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.header {
  display: flex;
  align-items: center;
  flex:1;
  height: 61px;
  padding:0;
  background-color: #83A4EB;
  .putaway {
    width: 172px;
    height: 31px;
    margin-left: 167px;
    font-size: 24px;
    color:#000;
  }
  .soldOut {
    width: 182px;
    height: 33px;
    margin-left: 277px;
    margin-right: 277px;
    font-size: 24px;
    color:#000;
  }
  .allProduct {
    width: 224px;
    height: 42px;
    margin-right: 122px;
    font-size: 24px;
    color:#000;
  }
}

.main {
  position: absolute;
  top: 61px;
  left: 235px;
  right: 0;
  padding-top: 54px;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-left: 93px;
  gap: 20px;
}

.product-selector {
  width: 229px;
  height: 52px;
}

.commBox {
  background-color: #CCCCCC;
  width: 281px;
  height: 42px;
  display: flex;
  align-items: center;
  font-size: 24px;
  margin-top: -10px;
  
  .Text {
    margin-left: 13px;
    
    &:first-child {
      min-width: 120px;
    }
  }
  
  input {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 24px;
  }
}

.filter-row1 {
  display: flex;
  align-items: center;
  margin-left: 40px;
  gap: 20px;
}

.product-selector1 {
  width: 196px;
  height: 52px;
}

.brandBox{
  background-color: #3A58CF;
  width: 145px;
  height: 42px;
  margin-top: -10px;
  display:flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
  .brand{
    font-size: 24px;
    color:#fff;
  }
}

.priceBox {
  margin-left: 113px;
  margin-top: 25px;
  display: flex;
  align-items: center; /* 添加这行确保垂直居中 */
  
  .pirceRange {
    font-size: 30px;
    line-height: 1; /* 确保文本行高一致 */
  }
  
  .leftPrice {
    width: 277px;
    height: 32px;
    margin-left: 10px;
    background-color: #ccc;
    vertical-align: middle; /* 添加垂直对齐 */
  }
  
  .to {
    margin-left: 10px;
    line-height: 1; /* 确保文本行高一致 */
  }
  
  .rightPrice {
    width: 277px;
    height: 32px;
    margin-left: 10px;
    background-color: #ccc;
    vertical-align: middle; /* 添加垂直对齐 */
  }
  
  .nullBtn {
    width: 277px;
    height: 32px;
    margin-left: 105px;
    background-color: #fff;
    border: 3px solid #3A58CF;
    vertical-align: middle; /* 添加垂直对齐 */
    line-height: 1; /* 重置按钮行高 */
    padding: 0; /* 移除默认内边距 */
  }
}

.product-type-row {
    display: flex;
    align-items: center;
    margin-left: 113px;
    margin-top: 25px;
    gap: 20px;
    
    .product-type-label {
        font-size: 30px;
        min-width: 120px;
    }
    
    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-left: 129px;
        
        :deep(.el-checkbox.square-checkbox) {
            .el-checkbox__inner {
                border-radius: 4px; /* 方形复选框 */
                width: 20px;
                height: 20px;
                
                &::after {
                    top: 2px;
                    left: 6px;
                }
            }
            
            .el-checkbox__label {
                font-size: 24px;
                color: #333;
            }
        }
    }
    
    .export-btn {
        margin-left: 430px;
        width: 120px;
        height: 42px;
        font-size: 20px;
        background-color: #FF8D1A;
        border: none;
        color: #000;
        
    }
}

.allButton{
  margin-top: 32px;
  margin-left: 303px;
  .button{
    width: 247px;
    height: 59px;
    margin-right: 41px;
    border-radius: 30px;
    border: 2px solid  #3A58CF;
    font-size: 30px;
   
    color:#3A58CF;
  }
}

.table-container {
    margin-top: 20px;
    padding: 0 20px;
    
    :deep(.custom-header) {
        th {
            background-color: #83A4EB !important;
            color: #000;
            font-weight: bold;
            font-size: 16px;
            
            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }
                .el-checkbox__label {
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }
    
    :deep(.custom-row) {
        td {
            background-color: #D2E0FB;
            
            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }
                .el-checkbox__label {
                    font-size: 14px;
                }
            }
        }
        
        &:hover td {
            background-color: #b8cdf9 !important;
        }
    }
    
    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;
        
        .el-table__cell {
            padding: 12px 0;
        }
    }
}



</style>