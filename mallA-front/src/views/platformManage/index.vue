<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { User,Tickets} from '@element-plus/icons-vue'

</script>

<template>
    <ManageBg>
       <div class="container">
            <img class="bg" src="../../images/bigBackground.png">
            <div class="content">
                <div class="picBox">
                    <img class="managePic" src="../../images/managePic.png">
                </div>
                <div class="buttons">
                    <button class="button left-button">
                        <div class="button-content">
                            <el-icon class="user-icon"><User /></el-icon>
                            <span class="text">今日新增客户量</span>
                        </div>
                    </button>
                    <button class="button right-button">
                        <div class="button-content">
                            <el-icon class="user-icon"><Tickets /></el-icon>
                            <span class="text">今日订单量</span>
                        </div>
                    </button>
                </div>
            </div>
       </div>
    </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  .content {
    z-index: 2;
    display: flex;
    flex-direction: column;
  }

  .picBox {
    .managePic {
      width: 1233px;
      height: 346px;
      margin-left: 250px;
      margin-top: 64px;
    }
  }

  .buttons {
    display: flex;
    margin-left: 250px;
    margin-top: 20px;
    gap: 39px;

    .button {
      width: 597px;
      height: 346px;
      border: none;
      cursor: pointer;
      background: #ffffff;
      border: 2px solid #3A58CF;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;

      &-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 30px;
        height: 100%;
      }

      .user-icon {
        font-size: 83px;
        color: #3A58CF;
      }

      .text {
        font-size: 28px;
        color: #333;
        text-align: center;
        width: 100%;
      }
    }
  }
}
</style>