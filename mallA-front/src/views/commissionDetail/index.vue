<script lang="ts" setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref, reactive } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  id: '',
  phone: '',
  incomeExpense: '',
  startTime: '',
  endTime: '',
  searchText: ''
})

// 表格数据
const tableData = ref([
  {
    time: '2025-11-4 16:30',
    account: 'user001',
    companyName: '游戏公司',
    name: '李四',
    todayVoucher: '8200',
    total: '500.00'
  },
  // 可以添加更多数据...
])

const onSubmit = () => {
  console.log('提交表单:', form)
}

const onExport = () => {
  console.log('导出Excel')
}

const handleShelf = (row: any) => {
  console.log('上架操作:', row)
}

const handleUnshelf = (row: any) => {
  console.log('下架操作:', row)
}

const router = useRouter()
const buttonList = [
  '设置', '平台补贴券明细', '平台促销券明细', '抵扣金明细', '货款明细',
  '已核销平台补贴券明细', '结算统计', '结算记录',
  '广告收益', '结算审核', '量化值进化量明细', '量化进化量明细'
]

const handleButtonClick = (item) => {
  if (item === '设置') router.push('./financeSetting')
  if (item === '平台补贴券明细') router.push('./platformVoucherDetail')
  if (item === '平台促销券明细') router.push('./platformCouponDetail')
  if (item === '抵扣金明细') router.push('./commissionDetail')
  if (item === '货款明细') router.push('./loanDetail')
  if (item === '已核销平台补贴券明细') router.push('./verifyVoucherDetail')
  if (item === '结算统计') router.push('./settlementCount')
  if (item === '结算记录') router.push('./settlementRecord')
  if (item === '广告收益') router.push('./')
  if (item === '结算审核') router.push('./settlementReview')
  if (item === '量化值进化量明细') router.push('./quantifyEvolutionDetail')
  if (item === '量化进化量明细') router.push('./creditEvolutionDetail')
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <div class="filter-container">
          <div class="filter-header">
            <span class="filter-title">筛选</span>
            <el-form-item class="search-item">
              <el-input 
                v-model="form.searchText" 
                placeholder="搜索" 
                class="search-input"
              >
                <template #append>
                  <el-button :icon="Search" />
                </template>
              </el-input>
            </el-form-item>
          </div>
          
          <el-form :model="form" class="filter-form">
            <!-- 第一行：手机号、手机号、收支 -->
            <div class="form-row">
              <el-form-item label="手机号">
                <el-input v-model="form.id" placeholder="请输入手机号" />
              </el-form-item>
              
              <el-form-item label="手机号">
                <el-input v-model="form.phone" placeholder="请输入手机号" />
              </el-form-item>
              
              <el-form-item label="收支">
                <el-input v-model="form.incomeExpense" placeholder="请输入收支" />
              </el-form-item>
            </div>
            
            <!-- 第二行：开始时间至结束时间 -->
            <div class="form-row">
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="form.startTime"
                  type="datetime"
                  placeholder="选择开始时间"
                />
              </el-form-item>
              
              <span class="time-separator">至</span>
              
              <el-form-item label="结束时间">
                <el-date-picker
                  v-model="form.endTime"
                  type="datetime"
                  placeholder="选择结束时间"
                />
              </el-form-item>
              
              <el-button type="primary" @click="onExport" class="export-btn">
                导出Excel
              </el-button>
            </div>
          </el-form>
          
          <!-- 分隔线 -->
          <el-divider />
          
          <!-- 统计信息 -->
          <div class="summary-info">
            <span>数据总数 0.00 / 金额 0.00 / 今日</span>
          </div>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-container">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="time" label="时间" width="180" />
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="公司名称/姓名">
              <template #default="{row}">
                <div>{{ row.companyName }}</div>
                <div>{{ row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="todayVoucher" label="抵扣金明细" width="200" />
            <el-table-column prop="total" label="总计" width="120" />
            <el-table-column label="操作" width="180">
              <template #default="{row}">
                <el-button 
                
                  
                  class="shelf-btn"
                  @click="handleShelf(row)"
                >
                  上架
                </el-button>
                <el-button 
                  
                 
                  class="unshelf-btn"
                  @click="handleUnshelf(row)"
                >
                  下架
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>    
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #3A58CF;
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .filter-title {
    font-size: 24px;
    font-weight: bold;
    color: white;
  }

  .search-item {
    margin-left: auto;
    
    :deep(.el-input-group__append) {
      background-color: #3A58CF;
      border: none;
      
      .el-button {
        color: white;
      }
    }
  }
}

.filter-form {
  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 0;
      
      :deep(.el-form-item__label) {
        padding-bottom: 0;
        font-weight: normal;
      }
    }
    
    .time-separator {
      margin: 0 10px;
      color: #666;
    }
    
    .export-btn {
      margin-left: 20px;
      background-color: #FF8D1A;
    }
  }
}

.summary-info {
  padding: 10px 0;
  color: #666;
  font-size: 14px;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  
  flex: 1;
  
  :deep(.el-table) {
    font-size: 14px;
    
    th {
      background-color: #f5f7fa;
      color: #333;
      font-weight: bold;
    }
    
    .cell {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}


:deep(.shelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  background-color: pink;
  &:hover {
    transform: translateY(-1px);
    
  }
}

:deep(.unshelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  margin-left: 0px;
  background-color: cyan;
  &:hover {
    transform: translateY(-1px);

  }
}
</style>