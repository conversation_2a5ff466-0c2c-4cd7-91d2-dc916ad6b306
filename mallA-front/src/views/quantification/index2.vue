<template>
  <div ref="chartDom" style="width: 600px; height: 400px;"></div>
</template>

<script>
import * as echarts from 'echarts'
import { onMounted, ref } from 'vue'

export default {
  setup() {
    const chartDom = ref(null)
    onMounted(() => {
      const chart = echarts.init(chartDom.value)
      chart.setOption({
        title: {
          text: 'Vue 3 Composition API 中使用 ECharts'
        },
        tooltip: {},
        xAxis: {
          data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
        },
        yAxis: {},
        series: [
          {
            name: '销量',
            type: 'line',
            data: [5, 20, 36, 10, 10, 20]
          }
        ]
      })
    })
    
    return { chartDom }
  }
}
</script>