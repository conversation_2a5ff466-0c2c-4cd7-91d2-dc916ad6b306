<template>
    <div ref="chartDom" style="width:600px;heigth:300px"></div>
</template>
<script lang="ts" setup>
import { ref, onMounted,onUnmounted,nextTick } from "vue"
import * as echarts from 'echarts';
const chartDom=ref(null);
let chartInstance:any=null;
onMounted(async()=>{
   await nextTick();
   chartInstance =echarts.init(chartDom.value);
   const option={
             title:{
                  text:'Echarts入门示例'              

             } ,
             tooltip:{
 
             },
             xAxios:{
                 data:[ '衬衫'   ] 
             },
             yAxios:{},
             series:[
                  {
                       name:"销量",
                       type:"bar",
                        data:[5,20,36,10,10,20]
                   }
             ]
 
   };
   chartInstance.setOption(option)
});
   onUnmounted(()=>{
     if(chartInstance !=null && chartInstance.dispose){
     	chartInstance.dispose()
      }
    })
</script>