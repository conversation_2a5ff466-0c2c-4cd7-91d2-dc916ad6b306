
<template>
  <HomeBg>
    <div class="quantification-page">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">量化数据分析</h1>
          <p class="page-subtitle">实时监控量化率变化趋势，优化数据表现</p>
        </div>
      </div>

      <!-- 核心指标卡片区域 -->
      <div class="metrics-section">
        <div class="metrics-grid">
          <!-- 量化值输入卡片 -->
          <el-card class="metric-card primary-card" shadow="hover">
            <div class="card-header">
              <div class="card-icon primary-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <h3>量化值设置</h3>
                <!--<p>设置基准量化值</p>  -->
              </div>
            </div>
            <div class="card-content">
              <div class="input-section">
                <el-input
                  v-model="creditValue"
                  placeholder="请输入量化值"
                  size="large"
                  class="value-input"
                >
                  <template #suffix>
                    <span class="input-unit">%</span>
                  </template>
                </el-input>
                <el-button
                  type="primary"
                  size="large"
                  @click="evolution1"
                  class="evolution-btn"
                >
                  <el-icon><Promotion /></el-icon>
                  进化
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 量化显示卡片 -->
          <el-card class="metric-card success-card" shadow="hover">
            <div class="card-header">
              <div class="card-icon success-icon">
                <el-icon><Medal /></el-icon>
              </div>
              <div class="card-info">
                <h3>量化</h3>
              <!--  <p>当前信用评级</p> -->
              </div>
            </div>
            <div class="card-content">
              <div class="metric-value">
                <span class="value">{{ creditDisplayValue }}</span>
                <span class="unit">分</span>
              </div>
            </div>
          </el-card>

          <!-- 量化值进化量卡片 -->
          <el-card class="metric-card warning-card" shadow="hover">
            <div class="card-header">
              <div class="card-icon warning-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="card-info">
                <h3>量化值进化量</h3>
              <!--  <p>进化增长率</p> -->
              </div>
            </div>
            <div class="card-content">
              <div class="metric-value">
                <span class="value">{{ quantifyEvolutionRate }}</span>
                <span class="unit">%</span>
              </div>
            </div>
          </el-card>

          <!-- 量化进化量卡片 -->
          <el-card class="metric-card info-card" shadow="hover">
            <div class="card-header">
              <div class="card-icon info-icon">
                <el-icon><DataLine /></el-icon>
              </div>
              <div class="card-info">
                <h3>量化进化量</h3>
              <!--  <p>信用增长率</p>  -->
              </div>
            </div>
            <div class="card-content">
              <div class="metric-value">
                <span class="value">{{ creditEvolutionRate }}</span>
                <span class="unit">%</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 图表分析区域 -->
      <div class="chart-section">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <div class="header-left">
                <h3 class="chart-title">
                  <el-icon><TrendCharts /></el-icon>
                  日程量化率走势图
                </h3>
                <p class="chart-subtitle">实时监控量化率变化趋势</p>
              </div>
              <div class="header-center">
                <!-- 当天数据显示 -->
                <div v-if="todayData" class="today-data-text">
                  {{ todayData }}
                </div>
              </div>
              <div class="header-right">
                <div class="month-selector">
                  <span class="selector-label">选择月份：</span>
                  <el-date-picker
                    v-model="selectedMonth"
                    type="month"
                    placeholder="选择月份"
                    value-format="YYYY-MM"
                    size="default"
                    class="month-picker"
                  />
                </div>
              </div>
            </div>
          </template>

          <div class="chart-container">
            <!-- 图表容器始终存在，确保ECharts能获取正确尺寸 -->
            <div
              ref="chartDom"
              class="chart-content"
            ></div>
            <!-- 空数据提示覆盖在图表上方 -->
            <div
              v-show="chartData.rates.length === 0"
              class="no-data-overlay"
            >
              <el-empty
                description="暂无数据"
                :image-size="120"
                class="empty-state"
              >
                <template #image>
                  <el-icon class="empty-icon"><DataBoard /></el-icon>
                </template>
                <el-button type="primary" @click="fetchChartData(selectedMonth)">
                  刷新数据
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 进化确认弹窗 -->
      <el-dialog
        v-model="showModal3"
        title="量化值进化"
        width="500px"
        :before-close="closeModal2"
        class="evolution-dialog"
      >
        <div class="dialog-content">
          <div class="dialog-icon">
            <el-icon class="evolution-icon"><Promotion /></el-icon>
          </div>
          <h3 class="dialog-title">请输入进化值</h3>
          <p class="dialog-desc">请输入小于 {{ creditValue }} 的正整数</p>

          <el-input
            v-model.number="inputValue"
            type="number"
            placeholder="请输入数值"
            size="large"
            class="evolution-input"
            ref="inputRef"
          />

          <div v-if="errorMessage" class="error-message">
            <el-icon><WarningFilled /></el-icon>
            {{ errorMessage }}
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeModal2" size="large">取消</el-button>
            <el-button
              type="primary"
              :disabled="!isInputValid"
              @click="confirmInput"
              size="large"
            >
              确认进化
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </HomeBg>
</template>
<script lang="ts" setup>
import dayjs, { Dayjs } from "dayjs";
import { ref, onMounted, watch, nextTick, computed } from "vue";
import * as echarts from "echarts";
import HomeBg from "../../components/HomeBg.vue";
import { ElMessage } from "element-plus";
import { getQuantizationRate } from '@/api/quantification';
import {
  TrendCharts,
  Medal,
  DataAnalysis,
  DataLine,
  Promotion,
  DataBoard,
  WarningFilled
} from '@element-plus/icons-vue';
const errorMessage = ref("");
const inputValue = ref(null);
const resultValue = ref("");
const inputRef = ref(null);
const loading = ref(false);
const evolution1 = async () => {
  showModal3.value = true;
  inputValue.value = null;
  errorMessage.value = "";
  // nextTick(() => {
  //   if (inputRef.value) inputRef.value.focus();
  // });
};
const showModal3 = ref(false);
const closeModal2 = () => {
  showModal3.value = false;
  inputValue.value = ""; // 清空临时输入
};
const confirmInput = () => {
  if (!isInputValid.value) {
    ElMessage.warning("请输入小于基准值的正整数");
    return;
  }
  resultValue.value = inputValue.value.toString(); // 渲染到表单上
  ElMessage.success("输入有效：" + inputValue.value);
  closeModal2();
};
const isInputValid = computed(() => {
  const inputNum = parseFloat(inputValue.value);
  const baseNum = parseFloat(creditValue.value);

  return (
    !isNaN(inputNum) && !isNaN(baseNum) && inputNum > 0 && inputNum <= baseNum
  );
});
const creditValue = ref("");

// 计算属性
const creditDisplayValue = computed(() => {
  return creditValue.value || "0";
});

// 从API数据中获取量化值进化量
const quantifyEvolutionRate = ref("0");

// 从API数据中获取量化进化量
const creditEvolutionRate = ref("0");

// 获取当天（最新一天）的数据 - 固定显示，不随月份切换改变
const todayData = ref<string>('');

// 更新当天数据的函数
const updateTodayData = (data: any[]) => {
  if (data && data.length > 0) {
    // 获取最后一天的数据
    const lastItem = data[data.length - 1];
    const rate = parseFloat(lastItem.quantizationRate); // 新字段名
    const percentage = (rate * 100).toFixed(6);
    todayData.value = `今天量化率：${percentage}%`;
  }
};

// DOM 引用
const chartDom = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

// 当前月份，默认是当前月
const currentMonth = ref(dayjs().format("YYYY-MM"));
const selectedMonth = ref(currentMonth.value);

// 🔁 根据月份生成日期数组（1~31）
const generateDaysArray = (month: string): number[] => {
  const startDate: Dayjs = dayjs(month);
  const endDate: Dayjs = startDate.endOf("month");
  const days: number[] = [];
  let currentDate: Dayjs = startDate;

  while (currentDate.isSame(endDate) || currentDate.isBefore(endDate)) {
    days.push(currentDate.date());
    currentDate = currentDate.add(1, "day");
  }

  return days;
};

// 模拟数据（你可以替换为真实接口请求）
// const fetchData = (month: string): number[] => {
//   const daysInMonth = generateDaysArray(month);
//   const dataLength = daysInMonth.length;
//   return Array.from(
//     { length: dataLength },
//     (_, i) => Math.floor(Math.random() * 100) + 100
//   );
// };
const chartData = ref({
  dates: [],   // x轴数据 ['2025-06-04', '2025-06-05', ...]
  rates: []    // y轴数据 [0.433333, 0.26, ...]
});

// 获取图表数据
const fetchChartData = async (searchMonth: string) => {
  try {
    const res = await getQuantizationRate({ searchMonth});
    console.log('接口返回:', res);
    if (res.code === 200 && res.data && Array.isArray(res.data.currentMonthQuantizationRate)) {
      const list = res.data.currentMonthQuantizationRate;

      // 更新进化量数据
      quantifyEvolutionRate.value = res.data.quantifyToCredit || "0";
      creditEvolutionRate.value = res.data.creditToCoupon || "0";
      console.log('进化量数据更新:', {
        quantifyToCredit: quantifyEvolutionRate.value,
        creditToCoupon: creditEvolutionRate.value
      });

      chartData.value = {
        dates: list.map((item: any) => {
          // 格式化日期显示，例如：06-30 (新字段名：quantifyDate)
          const dateParts = item.quantifyDate.split('-');
          const formattedDate = `${dateParts[1]}-${dateParts[2]}`;
          console.log('原始日期:', item.quantifyDate, '格式化后:', formattedDate);
          return formattedDate;
        }),
        rates: list.map((item: any, index: number) => {
          // 直接使用原始数值 (新字段名：quantizationRate)
          const rate = parseFloat(item.quantizationRate);
          console.log(`数据点${index + 1}:`, {
            date: item.quantifyDate,
            originalRate: item.quantizationRate,
            parsedRate: rate
          });
          return rate;
        })
      };
      console.log('最终图表数据:', chartData.value);

      // 只在当天数据为空时更新（保持固定显示最新数据）
      if (!todayData.value) {
        updateTodayData(list);
      }
    } else if (res.code === 500) {
      // 处理无数据的情况 (code: 500, message: "本月暂无量化率")
      console.log('本月暂无量化率数据:', res.message);
      chartData.value = { dates: [], rates: [] };
      // 不更新进化量数据，保持之前的值
    } else {
      console.log('API返回异常:', res);
      chartData.value = { dates: [], rates: [] };
    }
  } catch (err) {
    console.error('请求失败:', err);
    chartData.value = { dates: [], rates: [] };
  }
};
// 初始化图表
// 更新图表数据的方法
// 更新图表数据的方法
const updateChartData = async (searchMonth: string) => {
  console.log('开始更新图表数据，月份:', searchMonth);

  if (!searchMonth) {
    console.error("月份参数无效");
    return;
  }

  if (!myChart) {
    console.error("ECharts实例不存在，等待初始化");
    // 如果图表还没初始化，等待一下再试
    setTimeout(() => updateChartData(searchMonth), 500);
    return;
  }

  loading.value = true;

  try {
    await fetchChartData(searchMonth);
    console.log('获取数据完成:', chartData.value);

    if (chartData.value.dates.length > 0 && chartData.value.rates.length > 0) {
      console.log('准备更新图表，数据点数量:', chartData.value.dates.length);

      // 更新数据，确保图表配置完整
      const updateOption = {
        xAxis: {
          type: "category",
          data: chartData.value.dates
        },
        series: [{
          name: "量化率",
          type: "line",
          data: chartData.value.rates,
          smooth: false,
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF',
            borderColor: '#fff',
            borderWidth: 2
          }
        }]
      };

      console.log('设置图表选项:', updateOption);

      // 使用 nextTick 确保在正确的时机更新图表
      nextTick(() => {
        myChart.setOption(updateOption, false); // false表示合并配置
        console.log('图表更新完成');
      });
    } else {
      console.log('没有数据可显示');
      clearChart();
    }
  } catch (error) {
    console.error('更新图表失败:', error);
  } finally {
    loading.value = false;
  }
};

function showNoDataTip(chart: echarts.ECharts, message: string) {
  chart.setOption({
    graphic: {
      elements: [
        {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: message,
            fontSize: 18,
            fill: '#666'
          }
        }
      ]
    }
  });
}

// 清空图表数据但保持配置
const clearChart = () => {
  if (myChart) {
    // 不使用clear()，而是设置空数据，保持图表配置
    myChart.setOption({
      xAxis: {
        data: []
      },
      series: [{
        data: []
      }]
    });
    console.log('图表数据已清空，配置保留');
  }
};

// 初始化图表
const initChart = () => {
  if (!chartDom.value) {
    console.error('图表DOM元素不存在');
    return;
  }

  // 检查容器尺寸
  const rect = chartDom.value.getBoundingClientRect();
  console.log('图表容器尺寸:', {
    width: rect.width,
    height: rect.height,
    clientWidth: chartDom.value.clientWidth,
    clientHeight: chartDom.value.clientHeight
  });

  if (rect.width === 0 || rect.height === 0) {
    console.warn('图表容器尺寸为0，延迟初始化');
    setTimeout(() => initChart(), 100);
    return;
  }

  myChart = echarts.init(chartDom.value);
  console.log('ECharts instance initialized:', myChart);

  const option = {
    tooltip: {
      trigger: "axis",
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#303133',
        fontSize: 14
      },
      formatter: function(params: any) {
        const data = params[0];
        // 格式化日期显示
        let dateDisplay = data.name;
        if (data.name.includes('-')) {
          // 如果是 MM-DD 格式，转换为更友好的显示
          const [month, day] = data.name.split('-');
          dateDisplay = `${parseInt(month)}月${parseInt(day)}日`;
        } else if (!data.name.includes('日')) {
          // 如果只是数字，添加"日"后缀
          dateDisplay = data.name + '日';
        }

        return `
          <div style="padding: 8px;">
            <div style="font-weight: 600; margin-bottom: 4px;">${dateDisplay}</div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${data.color}; border-radius: 50%; margin-right: 8px;"></span>
              量化率：<span style="font-weight: 600; color: ${data.color};">${(data.value * 100).toFixed(6)}%</span>
            </div>
          </div>
        `;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        show: true,
        interval: 'auto', // 自动计算显示间隔，避免标签重叠
        color: '#606266',
        fontSize: 12,
        margin: 12,
        rotate: 0, // 不旋转标签
        formatter: function(value: string) {
          // 如果是月-日格式，直接返回
          if (value.includes('-')) {
            return value;
          }
          // 如果只是数字，添加日期后缀
          return value + '日';
        }
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: '#e4e7ed'
        }
      },
    },
    yAxis: {
      type: "value",
      scale: true, // 自动缩放，不从0开始
      axisLabel: {
        color: '#606266',
        fontSize: 12,
        formatter: function(value: number) {
          // 将小数值转换为百分比显示
          return (value * 100).toFixed(6) + '%';
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: "量化率",
        type: "line",
        data: [],
        smooth: false, // 不平滑，确保每个点都清晰可见
        symbol: 'circle',
        symbolSize: 8, // 稍大的点，更容易看到
        showSymbol: true, // 强制显示所有数据点
        lineStyle: {
          width: 2,
          color: '#409EFF'
        },
        itemStyle: {
          color: '#409EFF',
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            color: '#409EFF',
            borderColor: '#409EFF',
            borderWidth: 3,
            symbolSize: 12,
            shadowBlur: 10,
            shadowColor: 'rgba(64, 158, 255, 0.3)'
          }
        }
      },
    ],
  };

  myChart.setOption(option);

  console.log('图表初始化完成');
};
// 监听月份变化，更新图表
watch(
    () => selectedMonth.value,
    (newMonth) => {
      console.log("【月份变化】", newMonth); // 调试日志
      if (newMonth) {
        updateChartData(newMonth);
      }
    }
);

// 组件挂载时初始化
onMounted(() => {
  console.log('组件挂载，当前选择月份:', selectedMonth.value);

  // 多重延迟确保DOM完全渲染
  setTimeout(() => {
    nextTick(() => {
      initChart();
      if (myChart) {
        updateChartData(selectedMonth.value);
      }
    });
  }, 200); // 给足够的时间让DOM渲染完成
});
</script>
<style lang="scss" scoped>
// 现代化大厂风格样式
.quantification-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 122px);

  // 页面头部
  .page-header {
    margin-bottom: 32px;

    .header-content {
      .page-title {
        font-size: 28px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 8px 0;
        line-height: 1.2;
      }

      .page-subtitle {
        font-size: 16px;
        color: #6b7280;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  // 指标卡片区域
  .metrics-section {
    margin-bottom: 32px;

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;

      .metric-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1) !important;
        }

        :deep(.el-card__body) {
          padding: 24px;
        }

        .card-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;

            .el-icon {
              font-size: 24px;
              color: white;
            }
          }

          .card-info {
            h3 {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin: 0 0 4px 0;
              line-height: 1.2;
            }

            p {
              font-size: 14px;
              color: #6b7280;
              margin: 0;
              line-height: 1.4;
            }
          }
        }

        .card-content {
          .input-section {
            display: flex;
            gap: 12px;
            align-items: stretch;

            .value-input {
              flex: 1;

              :deep(.el-input__wrapper) {
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                border: 1px solid #e5e7eb;

                &:hover {
                  border-color: #3b82f6;
                }

                &.is-focus {
                  border-color: #3b82f6;
                  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
              }

              .input-unit {
                color: #6b7280;
                font-weight: 500;
              }
            }

            .evolution-btn {
              border-radius: 8px;
              padding: 0 20px;
              font-weight: 500;
              box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
              }
            }
          }

          .metric-value {
            display: flex;
            align-items: baseline;
            gap: 8px;

            .value {
              font-size: 32px;
              font-weight: 700;
              line-height: 1;
            }

            .unit {
              font-size: 16px;
              font-weight: 500;
              color: #6b7280;
            }
          }
        }

        // 不同类型卡片的主题色
        &.primary-card {
          .card-icon.primary-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          }

          .metric-value .value {
            color: #3b82f6;
          }
        }

        &.success-card {
          .card-icon.success-icon {
            background: linear-gradient(135deg, #10b981, #047857);
          }

          .metric-value .value {
            color: #10b981;
          }
        }

        &.warning-card {
          .card-icon.warning-icon {
            background: linear-gradient(135deg, #f59e0b, #d97706);
          }

          .metric-value .value {
            color: #f59e0b;
          }
        }

        &.info-card {
          .card-icon.info-icon {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
          }

          .metric-value .value {
            color: #8b5cf6;
          }
        }
      }
    }
  }

  // 图表区域
  .chart-section {
    .chart-card {
      border: none;
      border-radius: 12px;
      overflow: hidden;

      :deep(.el-card__header) {
        background: #ffffff;
        border-bottom: 1px solid #f3f4f6;
        padding: 24px;
      }

      :deep(.el-card__body) {
        padding: 0;
      }

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-left {
          flex: 1;

          .chart-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;

            .el-icon {
              font-size: 24px;
              color: #3b82f6;
            }
          }

          .chart-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
          }
        }

        .header-center {
          flex: 0 0 auto;
          display: flex;
          justify-content: center;

          .today-data-text {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            white-space: nowrap;
          }
        }

        .header-right {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 24px;

          .month-selector {
            display: flex;
            align-items: center;
            gap: 8px;

            .selector-label {
              font-size: 14px;
              color: #374151;
              font-weight: 500;
              white-space: nowrap;
            }

            .month-picker {
              :deep(.el-input__wrapper) {
                border-radius: 6px;
                border: 1px solid #d1d5db;

                &:hover {
                  border-color: #3b82f6;
                }
              }
            }
          }

          .current-month {
            :deep(.el-tag) {
              border-radius: 6px;
              font-weight: 500;
              padding: 8px 16px;
              font-size: 14px;
            }
          }
        }
      }

      .chart-container {
        padding: 24px;
        position: relative; // 为覆盖层定位

        .chart-content {
          width: 100%;
          height: 500px;
          border-radius: 8px;
        }

        .no-data-overlay {
          position: absolute;
          top: 24px;
          left: 24px;
          right: 24px;
          bottom: 24px;
          background: rgba(255, 255, 255, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          z-index: 10;

          .empty-state {
            .empty-icon {
              font-size: 80px;
              color: #d1d5db;
            }

            :deep(.el-empty__description) {
              color: #6b7280;
              font-size: 16px;
              margin: 16px 0 24px 0;
            }

            :deep(.el-button) {
              border-radius: 8px;
              padding: 12px 24px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

// 进化弹窗样式
:deep(.evolution-dialog) {
  .el-dialog {
    border-radius: 12px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 32px 24px 24px;

    .dialog-content {
      text-align: center;

      .dialog-icon {
        margin-bottom: 16px;

        .evolution-icon {
          font-size: 48px;
          color: #3b82f6;
          padding: 16px;
          background: rgba(59, 130, 246, 0.1);
          border-radius: 50%;
        }
      }

      .dialog-title {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 8px 0;
      }

      .dialog-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 0 0 24px 0;
        line-height: 1.5;
      }

      .evolution-input {
        margin-bottom: 16px;

        :deep(.el-input__wrapper) {
          border-radius: 8px;
          border: 2px solid #e5e7eb;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #3b82f6;
          }

          &.is-focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }

      .error-message {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #ef4444;
        font-size: 14px;
        background: rgba(239, 68, 68, 0.1);
        padding: 12px;
        border-radius: 8px;
        margin-top: 16px;

        .el-icon {
          font-size: 16px;
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 12px;

      .el-button {
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 500;
        min-width: 100px;

        &:first-child {
          background: #f3f4f6;
          border-color: #d1d5db;
          color: #374151;

          &:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .quantification-page {
    .metrics-section .metrics-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .chart-section .chart-card .chart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-center {
        order: -1; // 在小屏幕上将当天数据移到顶部
        width: 100%;
        justify-content: flex-start;

        .today-data-text {
          font-size: 14px;
        }
      }

      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

@media (max-width: 768px) {
  .quantification-page {
    padding: 16px;

    .page-header .header-content .page-title {
      font-size: 24px;
    }

    .metrics-section {
      .metrics-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .metric-card .card-content .input-section {
        flex-direction: column;
        gap: 12px;
      }
    }

    .chart-section .chart-card {
      .chart-header .header-right {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .chart-container .chart-content {
        height: 350px;
      }
    }
  }

  :deep(.evolution-dialog) {
    .el-dialog {
      width: 90% !important;
      margin: 5vh auto !important;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quantification-page {
  animation: fadeInUp 0.6s ease-out;
}

.metrics-section .metric-card {
  animation: fadeInUp 0.6s ease-out;

  @for $i from 1 through 4 {
    &:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }
}

.chart-section .chart-card {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

</style>