<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
}

const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址', '平台促销券', '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]

const handleToReturnAddress = () =>{
    router.push('/returnAddress')
}

// 地址选择数据
const provinces = ['广东省', '北京市', '上海市', '浙江省']
const cities = {
  '广东省': ['深圳市', '广州市', '东莞市'],
  '北京市': ['朝阳区', '海淀区', '东城区'],
  '上海市': ['浦东新区', '静安区', '黄浦区'],
  '浙江省': ['杭州市', '宁波市', '温州市']
}
const districts = {
  '深圳市': ['南山区', '福田区', '罗湖区'],
  '广州市': ['天河区', '越秀区', '海珠区'],
  '朝阳区': ['三里屯街道', '建国门街道'],
  '浦东新区': ['陆家嘴街道', '张江镇']
}
const streets = {
  '南山区': ['科技园街道', '蛇口街道'],
  '福田区': ['华强北街道', '香蜜湖街道'],
  '陆家嘴街道': ['世纪大道', '浦东大道']
}

const selectedProvince = ref('')
const selectedCity = ref('')
const selectedDistrict = ref('')
const selectedStreet = ref('')
</script>

<template>
    <ManageBg>
        <div class="container">
            <!-- 背景图放在最底层 -->
            <div class="bg">
                <img src="../../images/bigBackground.png" alt="">
            </div>
            
            <!-- 左侧按钮 -->
            <div class="left-buttons">
                <el-button 
                    v-for="(item, index) in buttonList" 
                    :key="index"
                    class="data-button"
                    @click="handleButtonClick(item)"
                >
                    {{ item }}
                </el-button>
            </div>
            
            <!-- 主内容区 -->
            <div class="main">
                <input class="form-input" type="text" placeholder="收件人">
                <input class="form-input" type="tel" placeholder="手机号" pattern="[0-9]*">
                
                <div class="addressBox">
                    <span class="address-label">地址</span>
                    <el-select 
                        v-model="selectedProvince" 
                        placeholder="请选择省份"
                        class="address-select"
                        @change="selectedCity = ''; selectedDistrict = ''; selectedStreet = ''"
                    >
                        <el-option
                            v-for="item in provinces"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    
                    <el-select 
                        v-model="selectedCity" 
                        placeholder="请选择城市"
                        class="address-select"
                        :disabled="!selectedProvince"
                        @change="selectedDistrict = ''; selectedStreet = ''"
                    >
                        <el-option
                            v-for="item in cities[selectedProvince] || []"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    
                    <el-select 
                        v-model="selectedDistrict" 
                        placeholder="请选择区县"
                        class="address-select"
                        :disabled="!selectedCity"
                        @change="selectedStreet = ''"
                    >
                        <el-option
                            v-for="item in districts[selectedCity] || []"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                    
                    <el-select 
                        v-model="selectedStreet" 
                        placeholder="请选择街道"
                        class="address-select"
                        :disabled="!selectedDistrict"
                    >
                        <el-option
                            v-for="item in streets[selectedDistrict] || []"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                </div>
                
                <input class="form-input" type="text" placeholder="详细地址">
                <button @click="handleToReturnAddress" class="confirm-btn">确认</button>
            </div>
        </div>
    </ManageBg>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
    padding-left: 0; 
}

.bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    
    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.left-buttons {
    width: 235px;
    height: 100%;
    margin: 0; /* 移除margin */
    overflow-y: auto;
    background: #fff;
    border-radius: 0 8px 8px 0; /* 只保留右侧圆角 */
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
    flex-shrink: 0; /* 防止被压缩 */

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        transition: background-color 0.3s;
        
        &:hover {
            background-color: #2a48bf;
        }
        
        &:first-child {
            border-top-right-radius: 8px;
        }
        
        &:last-child {
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.main {
    position: relative;
    z-index: 2;
    margin-left: 220px; /* 减少与左侧边栏的间距 */
    margin-top: 200px;
    width: 805px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    
    .form-input {
        width: 100%;
        height: 53px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 0 15px;
        font-size: 16px;
        outline: none;
        transition: border-color 0.3s;
        
        &:focus {
            border-color: #3A58CF;
        }
    }
    
    .addressBox {
        display: flex;
        align-items: center;
        gap: 5px;
        
        .address-label {
            font-size: 16px;
            min-width: 40px;
        }
        
        .address-select {
            flex: 1;
            
            :deep(.el-input__inner) {
                height: 53px;
                line-height: 53px;
            }
        }
    }
    
    .confirm-btn {
        margin-top: 100px;
        margin-left: 308px;
        width: 154px;
        height: 73px;
        background-color: #14097A;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 30px;
        cursor: pointer;
        transition: background-color 0.3s;
        
        &:hover {
            background-color: #2a48bf;
        }
    }
}
</style>