<script setup>
import Background from '../../components/Background.vue'
import { ref } from 'vue'
import { User, Cellphone } from '@element-plus/icons-vue'
const account = ref('')
const phone = ref('')
const verifyCode = ref('')
</script>

<template>
  <Background>
    <div class="forgot-password-container">
      <div class="form-box">
        <el-input 
          v-model="account" 
          placeholder="输入找回账号" 
          class="account-input"
        >
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
        </el-input>
        
        <div class="phone-input-group">
          <el-input 
            v-model="phone" 
            placeholder="输入手机号" 
            class="form-input"
          >
            <template #prefix>
              <el-icon><Cellphone /></el-icon>
            </template>
          </el-input>
          <div class="code-btn-container">
            <el-button class="get-code-btn" type="text">获取短信验证码</el-button>
          </div>
        </div>
        
        <div class="verify-code-group">
          <el-input 
            v-model="verifyCode" 
            placeholder="验证码" 
            class="form-input"
          />
          <el-button class="confirm-btn">确定</el-button>
        </div>
        
        <router-link to="/resetPassword" class="next-btn">下一步</router-link>
      </div>
    </div>
  </Background>
</template>

<style lang="scss" scoped>
.forgot-password-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  padding: 138px 0 118px;
  box-sizing: border-box;
}

.form-box {
  width: 642px;
  min-height: 824px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: #5170ED;
  border-radius: 8px;
  padding: 20px 0;
}

.account-input {
  width: 465px;
  height: 70px;
  margin: 80px auto 40px;
  
  :deep(.el-input__inner) {
    height: 70px;
    font-size: 18px;
    border-radius: 4px;
    background: white;
  }
}

.form-input {
  width: 465px;
  height: 70px;
  margin: 0 auto;
  
  :deep(.el-input__inner) {
    height: 70px;
    font-size: 18px;
    border-radius: 4px;
    background: white;
  }
}

.phone-input-group {
  width: 465px;
  display: flex;
  flex-direction: column;
  margin: 0 auto; 
  position: relative;

  .code-btn-container {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
    
    .get-code-btn {
      width: 212px;
      height: 29px;
      margin-left: 373px;
      padding: 0;
      color: #000;
      font-size: 16px;
      border: none;
      background: transparent;
      
      &:hover {
        color: #f0f0f0;
        background: transparent;
      }
      
      &:active {
        color: #e0e0e0;
      }
    }
  }
}

.verify-code-group {
  width: 465px;
  display: flex;
  margin: 23px auto 0; /* 确定按钮与获取验证码按钮间距23px */
  align-items: center;

  .form-input {
    flex: 1;
  }

  .confirm-btn {
    width: 153px;
    height: 68px;
    background: #E88205;
    color: white;
    font-size: 30px;
    border-radius: 4px;
    margin-left: 28px;
    border: none;
    
    &:hover {
      background: #d67300;
    }
  }
}

:deep(.el-input__prefix) {
  left: 12px;
  display: flex;
  align-items: center;
}

.next-btn {
  width: 465px;
  height: 70px;
  font-size: 20px;
  background: #14097A;
  color: white;
  border-radius: 4px;
  margin: 20px auto 0;
  text-decoration: none;
  display: flex;
  justify-content: center;
  align-items: center;
  
  &:hover {
    background: #0e075e;
  }
}
</style>