<template>
  <HomeBg>
    <div class="page-container">
      <!-- 顶部按钮 -->
      <div class="header-buttons">
        <el-button @click="handleToCreateUser" class="create-btn">创建用户+</el-button>
      </div>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input 
          class="search-input" 
          placeholder="账号"
          clearable
          v-model="searchQuery"
          @keyup.enter="handleSearch"
        />
        <el-button class="search-btn" type="primary" @click="handleSearch">搜索</el-button>
      </div>

      <!-- 表格区域 -->
      <div class="table-wrapper">
          <el-table
                :data="tableData"
                style="width: 100%; margin-bottom: 20px"
                row-key="id"
                border
                default-expand-all
              >
                <el-table-column prop="date" label="账号" />
                <el-table-column prop="name" label="手机号" />
                <el-table-column prop="address" label="状态"/>
                <el-table-column label="操作" width="180">
                  <template #default>
                    <el-button link type="primary" @click="openPasswordDialog">修改</el-button>
                    <el-button link type="primary" @click="openPasswordDelete">删除</el-button>
                  </template>
                </el-table-column>
          </el-table>
      </div>

      <!-- 固定在右下角的分页 -->
      <div class="fixed-pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </HomeBg>
</template>
<script setup lang="ts">
import HomeBg from '../../components/HomeBg.vue'
import { ref } from 'vue'
import {useRouter} from 'vue-router'


const router = useRouter();
const handleToCreateUser = ()=>{
    router.push({path:'/CreateUser'})
}
// 表格数据
const tableData: User[] = [
    {
        id: 1,
        date: '公司',
        name: 'wangxiaohu',
        address: 'No. 189, Grove St, Los Angeles',
    },
    {
        id: 2,
        date: '统计',
        name: 'wangxiaohu',
        address: 'No. 189, Grove St, Los Angeles',
    },
    {
        id: 3,
        date: '商品',
        name: 'wangxiaohu',
        address: 'No. 189, Grove St, Los Angeles',
    },
    {
        id: 4,
        date: '客户',
        name: 'wangxiaohu',
        address: 'No. 189, Grove St, Los Angeles',
    }
]
// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 搜索
const searchQuery = ref('')

// 搜索功能
const handleSearch = () => {
  console.log('搜索内容:', searchQuery.value)
  // 这里可以添加实际的搜索逻辑
}

</script>
<style lang="scss" scoped>
.page-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;

  .header-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 20px;

    .create-btn {
      width: 200px;
      height: 50px;
      background-color: #3A58CF;
      color: white;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .search-area {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    .search-input {
      flex: 1;
      
      :deep(.el-input__wrapper) {
        height: 50px;
        font-size: 16px;
      }
    }

    .search-btn {
      width: 100px;
      height: 50px;
      font-size: 16px;
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: auto;
    margin-bottom: 60px; /* 为分页留出空间 */
    
    :deep(.el-table) {
      font-size: 14px;
    }
    
    :deep(.el-table__cell) {
      padding: 12px 0;
    }
  }

  .fixed-pagination {
    position: fixed;
    right: 20px;
    bottom: 20px;
    background: white;
    padding: 10px;
    border-radius: 4px;
   ;
    z-index: 10;
  }
}
</style>