<template>

  <div class="container">


    <div class="right-top">
      <div class="right3" right-top style="height: 600px;margin-top: 10px;">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="enterpriseDataSwitch" @change="getEnterpriseDataSwitch" class="ml-4">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <div class="content">
          <el-row
              style="margin-left: 50px; margin-top: 10px; margin-bottom: 10px"
          >交易数据读取名称
          </el-row
          >
          <el-row style="margin-left: 50px; margin-top: 5px">
            <span style="margin-right: 50px">企业名称</span>
            <el-select
              style="width: 200px;margin-right: 10px"
              v-model="inputValue1"
              placeholder="请选择企业"
              :key="`select-${selectKey}`"
              clearable
            >
              <el-option
                  v-for="item in connAll"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
              />
            </el-select>
            <el-button type="primary" @click="addToTable1"  :disabled="enterpriseDataSwitch === '1'">添加</el-button>
          </el-row>
          <el-scrollbar height="150px">
            <el-row>
              <el-row
                  style="
                    width: 1400px;
                    text-align: center;
                    margin-left: 30px;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                    background: #fff;
                  "
              >
                <el-col :span="6" style="margin-top: 20px">企业名称</el-col>
                <!-- <el-col :span="6"> 数值</el-col> -->
                <el-col :span="4" style="margin-top: 20px"> 操作</el-col>
              </el-row>

              <el-row>
                <el-row
                    style="width: 1200px"
                    v-for="(item, index) in tableData1"
                    v-key="index"
                >
                  <el-col
                      :span="6"
                      style="margin-left: 50px; margin-top: 10px"

                  >
                    <div style="border: 1px solid #ccc;font-size: 15px"  v-if="item" disabled>{{item.name}}</div>
                  </el-col>
                  <!-- <el-col :span="6">
                      <el-input v-if="item" v-model="item.number"></el-input>
                    </el-col> -->
                  <el-col
                      :span="6"
                      style="margin-left: 120px; margin-top: 10px"
                  >
                    <el-button
                        style="width: 100px"
                        size="small"
                        type="danger"
                        @click="handleDelete1(item.enterpriseId)"

                    >
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-row>
            </el-row>
          </el-scrollbar>
        </div>
        <div class="content">
          <el-row style="padding: 10px; margin-top: 10px">
              <span style="margin-right: 5px; margin-left: 40px"
              >数据自定义名称1</span
              >
            <el-form-item style="margin-right: 5px">
              <el-input
                  value=""
                  name=""
                  placeholder="请输入你想要添加的名称"
                  v-model="formData.trade_name"
              />
            </el-form-item>
            <!-- 只读显示数值 -->

            <el-button type="primary" @click="addToTable" :disabled="enterpriseDataSwitch === '1'">添加</el-button>
          </el-row>
            <el-row>
              <el-scrollbar height="150px">
                <el-row>
                  <el-row style="width: 1400px;text-align: center;position: sticky;top: 0;z-index: 10; background: #fff;">
                    <el-col :span="6"> 名称</el-col>
                    <el-col :span="6"> 数值</el-col>
                    <el-col :span="2"> 操作</el-col>
                  </el-row>
                  <el-row style="width: 1200px" v-for="(item, index) in tableData" :key="index">
                    <el-col :span="6" style="margin-left: 50px; margin-top: 10px">
                      <div v-if="item" style="border: 1px solid #ccc;font-size: 15px">{{item.trade_name}}</div>
                    </el-col>
                    <el-col :span="6" style="margin: 10px">
                      <div v-if="item" style="border: 1px solid #ccc;font-size: 15px" @blur="totalItems()">{{item.trade_amount}}</div>
                    </el-col>
                  <el-col :span="6" style="margin-left: 10px">
                    <el-button style="width: 100px;margin-left: 60px; margin-top: 10px;height: 30px;" size="small"
                               type="danger" @click="handelDeleteEnterpriseDataName(item.id)" :disabled="enterpriseDataSwitch === '1'"  >
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-row>
            </el-scrollbar>
          </el-row>
        </div>

        <div style="margin-left: -80px; margin-top: 5px">
          <el-row>
              <span
                  style="font-size: 18px; margin-top: 15px; margin-left: 130px"
              >
                每日自动更新合计
              </span>
            <div class="count">
              <div style="font-size: 15px">{{ tradeAmountTotal }}</div>
            </div>
          </el-row>
        </div>
      </div>

      <div class="right2">
        <div style="margin-top: 50px; margin-left: 30px">
          <el-row style="margin-top: -50px">
            <el-radio-group v-model="customConstantsSwitch" @change="getCustomConstantsSwitch">
              <el-radio value="0" size="large">开启</el-radio>
              <el-radio value="1" size="large">关闭</el-radio>
            </el-radio-group>
          </el-row>
          <el-row style="line-height: 40px; margin-left: 50px">
            <el-col>
              <span class="evolution-value">自定义常数</span>
            </el-col>
            <el-row style="line-height: 40px; margin-top: 20px">
              <el-form-item>
                <el-input
                    style="width: 200px"
                    :disabled="customConstantsSwitch === '1'"
                    placeholder="请您输入数据值"
                    v-model="message"
                />
              </el-form-item>
            </el-row>
          </el-row>
            <el-button type="primary" style="border-radius: 5px; margin-left: 110px" @click="submitCustomConstants" :disabled="customConstantsSwitch === '1'"
          >保存
          </el-button
          >

          <div class="number">{{resultValue}}</div>
        </div>

      </div>
      <div class="right3" style="height: 600px">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="quantityDataSwitch" @change="getQuantityDataSwitch" class="ml-4">
              <el-radio value="0" size="large">开启</el-radio>
              <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <div class="content">
          <el-row
              style="margin-left: 50px; margin-top: 10px; margin-bottom: 10px"
          >各企业系统每日更新总累计量化数
          </el-row
          >
          <el-row style="margin-left: 50px; margin-top: 5px">
            <span style="margin-right: 50px">企业名称</span>
            <el-form-item style="margin-right: 5px">
              <el-select style="width: 200px;margin-right: 10px" v-model="enterpriseInputValue">
                <el-option
                    v-for="item in enterpriseAll"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-button type="primary" :disabled="quantityDataSwitch === '1'" @click="addToEnterprise">添加</el-button>
          </el-row>
          <el-scrollbar height="150px">
            <el-row>
              <el-row
                  style="
                    width: 1400px;
                    text-align: center;
                    margin-left: 30px;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                    background: #fff;
                  "
              >
                <el-col :span="6">企业名称</el-col>
                <!-- <el-col :span="6"> 数值</el-col> -->
                <el-col :span="5"> 操作</el-col>
              </el-row>

              <el-row>
                <el-row
                    style="width: 1200px"
                    v-for="(item, index) in enterpriseData"
                    v-key="index"
                >
                  <el-col
                      :span="6"
                      style="margin-left: 50px; margin-top: 10px"
                  >
                    <div style="border: 1px solid #ccc;font-size: 15px"  v-if="item" disabled>{{item.name}}</div>
                  </el-col>
                  <!-- <el-col :span="6">
                      <el-input v-if="item" v-model="item.number"></el-input>
                    </el-col> -->
                  <el-col
                      :span="6"
                      style="margin-left: 120px; margin-top: 10px"
                  >
                    <el-button
                        style="width: 100px"
                        size="small"
                        type="danger"
                        @click="handelDeleteEnterprise(item.enterpriseId)"
                        :disabled="quantityDataSwitch === '1'"
                    >
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-row>
            </el-row>
          </el-scrollbar>
        </div>
        <div class="content">
          <el-row style="padding: 10px; margin-top: 10px">
              <span style="margin-right: 5px; margin-left: 40px"
              >数据自定义名称</span
              >
            <el-form-item style="margin-right: 5px">
              <el-input
                  value=""
                  name=""
                  placeholder="请输入你想要添加的名称"
                  v-model="enterpriseTransctionData.trade_name"
              />
            </el-form-item>
            <el-button type="primary" @click="addToEnterpriseTable" :disabled="quantityDataSwitch === '1'">添加</el-button>
          </el-row>
          <el-row>
            <el-scrollbar height="150px">
              <el-row>
                <el-row style="width: 1400px;text-align: center;position: sticky;top: 0;z-index: 10; background: #fff;">
                  <el-col :span="6"> 名称</el-col>
                  <el-col :span="6"> 数值</el-col>
                  <el-col :span="2"> 操作</el-col>
                </el-row>
                <el-row style="width: 1200px" v-for="(item, index) in enterpriseQuantityData" :key="index">
                  <el-col :span="6" style="margin-left: 50px; margin-top: 10px">
                    <div v-if="item" style="border: 1px solid #ccc;font-size: 15px">{{item.trade_name}}</div>
                  </el-col>
                  <el-col :span="6" style="margin: 10px">
                    <div v-if="item" style="border: 1px solid #ccc;font-size: 15px">{{item.trade_amount}}</div>

                  </el-col>
                  <el-col :span="6" style="margin-left: 10px">
                    <el-button style="width: 100px;margin-left: 60px; margin-top: 10px;height: 30px;" size="small"
                               type="danger" @click="handelDeleteEnterpriseQuantityName(item.id)" :disabled="quantityDataSwitch === '1'"  >
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-row>
            </el-scrollbar>
          </el-row>
        </div>
        <div style="margin-left: -80px; margin-top: 5px">
          <el-row>
              <span
                  style="font-size: 18px; margin-top: 15px; margin-left: 130px"
              >
                每日自动更新合计
              </span>
            <div class="count">
              <div  style="font-size: 15px">{{ enteypriseTradeAmountTotal }}</div>
            </div>
          </el-row>
        </div>
      </div>
      <div class="right6">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="dailyTradePercentageSwitch" @change="handleDailyTradePercentageSwitchChange">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <el-row style="height: 40px; line-height: 40px; text-align: left">
          <el-col>
              <span class="evolution-value" style="padding-left: 30px">
                所有企业各ID每日每笔新交易数据的量化数比
              </span>
          </el-col>

          <el-input
              style="width: 200px;margin-left:85px;margin-bottom: 30px;margin-top: 15px;"
              placeholder=""
              v-model="dailyTradePercentageForm.dailyTradePercentage"
              :disabled="dailyTradePercentageSwitch === '1'"
          />
          <span class="percent">%</span>
          <el-col>
              <span class="evolution-value" style="padding-left: 30px">
                排位 权限
              </span>
          </el-col>
          <el-input
              class="rate3"
              placeholder="排位 权限"
              v-model="dailyTradePercentageForm.ranking1"
              :disabled="dailyTradePercentageSwitch === '1'"
          />
          <el-input
              class="rate3"
              placeholder=""
              v-model="dailyTradePercentageForm.ranking1Percentage"
              :disabled="dailyTradePercentageSwitch === '1'"
          />
          <span class="percent">%</span>

          <el-col>
              <span class="evolution-value" style="padding-left: 30px">
                排位
              </span>
          </el-col>

          <el-input
              class="rate3"
              placeholder="排位 权限"
              v-model="dailyTradePercentageForm.ranking2"
              :disabled="dailyTradePercentageSwitch === '1'"
          />
          <el-input
              class="rate3"
              placeholder=""
              v-model="dailyTradePercentageForm.ranking2Percentage"
              :disabled="dailyTradePercentageSwitch === '1'"
          />
          <span class="percent">%</span>
        </el-row>
        <el-button type="primary" class="save2" @click="submitDailyTradePercentage" :disabled="dailyTradePercentageSwitch === '1'"
        >保存
        </el-button
        >
      </div>
      <div class="right3">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="partnerEnterpriseAdminSwitch" @change="handlePartnerEnterpriseAdminSwitchChange">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <el-row style="height: 40px; line-height: 40px; text-align: left">
          <el-col>
              <span class="evolution-value" style="padding-left: 30px">
                所有合作企业Admain的各ID每日每笔数据量化数比
              </span>
          </el-col>

          <el-input
              class="rate"
              placeholder=""
              v-model="partnerEnterpriseAdminForm.dailyDataPercentage"
              :disabled="partnerEnterpriseAdminSwitch === '1'"
          />
          <span class="percent">%</span>
        </el-row>
        <el-button type="primary" class="save" @click="submitPartnerEnterpriseAdminData" :disabled="partnerEnterpriseAdminSwitch === '1'"
        >保存
        </el-button
        >
      </div>

      <div class="right5">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="bSettingsSwitch" @change="handleBSettingsSwitchChange">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <el-row
            style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
        >
          <el-col>
              <span class="evolution-value" style="padding-left: 30px"
              >合作企业各IDB设置</span
              >
            <hr class="underline" />
          </el-col>

          <span style="margin-left: 30px;margin-top: 10px;">量化值进化量化</span>

          <el-input
              class="rate1"
              placeholder=""
              v-model="bSettingsForm.quantifyToCredit"
              :disabled="bSettingsSwitch === '1'"
          />
          <span class="percent" style="margin-top: 18px;">%</span>

          <el-row>
            <span style="padding-left: 30px; margin-top: 20px;">量化进化平台促销金</span>
            <el-input
                class="rate4"
                placeholder=""
                v-model="bSettingsForm.creditToCoupon"
                :disabled="bSettingsSwitch === '1'"
            />
            <span class="percent" style="margin-top: 30px;">%</span>
          </el-row>
        </el-row>
        <el-button type="primary" class="save1" @click="submitBSettings" :disabled="bSettingsSwitch === '1'"
        >保存
        </el-button
        >
      </div>

      <div class="right5">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="cSettingsSwitch" @change="handleCSettingsSwitchChange">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <el-row
            style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
        >
          <el-col>
              <span class="evolution-value" style="padding-left: 30px"
              >合作企业各IDC设置</span
              >
            <hr class="underline" />
          </el-col>
          <span style="margin-left: 30px;margin-top: 15px;"> C每ID每日自动量化值进化量化</span>

          <el-input
              class="rate1"
              placeholder=""
              v-model="cSettingsForm.quantifyToCredit"
              :disabled="cSettingsSwitch === '1'" style="margin-left: 20px;"
          />
          <span class="percent" style="margin-top: 20px;">%</span>

          <el-row>
            <span style="padding-left: 80px;margin-top: 15px;">  C每ID每日自动量化进化平台补贴金</span>
            <el-input
                class="rate4"
                placeholder=""
                v-model="cSettingsForm.creditToCoupon"
                :disabled="cSettingsSwitch === '1'" style="margin-left: 100px;"
            />
            <span class="percent" style="margin-top: 30px;">%</span>
          </el-row>
        </el-row>
        <el-button type="primary" class="save1" @click="submitCSettings" :disabled="cSettingsSwitch === '1'"
        >保存
        </el-button
        >
      </div>

      <!-- 分量进化设置 -->
      <div class="right5">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="quantityEvolutionSwitch" @change="handleQuantityEvolutionSwitchChange">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <el-row
            style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
        >
          <el-col>
              <span class="evolution-value" style="padding-left: 30px"
              >分量进化设置</span
              >
            <hr class="underline" />
          </el-col>
          <span style="margin-left: 30px;margin-top: 15px;">每日每ID的分量进化量化数</span>

          <el-input
              class="rate1"
              placeholder=""
              v-model="quantityEvolutionForm.proportion"
              :disabled="quantityEvolutionSwitch === '1'" style="margin-left: 20px;"
          />
          <span class="percent" style="margin-top: 20px;">%</span>
        </el-row>
        <el-button type="primary" class="save1" @click="submitQuantityEvolution" :disabled="quantityEvolutionSwitch === '1'"
        >保存
        </el-button
        >
      </div>

      <div class="right7">
        <el-row style="margin-left: 30px">
          <el-radio-group v-model="areaSettingsSwitch">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </el-row>
        <el-row
            style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
        >
          <el-col>
              <span class="evolution-value" style="padding-left: 30px"
              >所合作企业区域的设置</span
              >
            <hr class="underline" />
          </el-col>
          <el-row style="margin-left: 50px; margin-top: 5px">
            <span style="margin-right: 50px">企业名称</span>
            <el-form-item style="margin-right: 5px">
              <el-select
                  v-model="selectedEnterprise"
                  placeholder="请选择企业"
                  @change="handleEnterpriseChange"
                  style="width: 380px"
              >
                <el-option
                    v-for="item in enterpriseList"
                    :key="item.id"
                    :label="item.enterpriseName"
                    :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-row>

          <el-col>
            <el-row style="margin-left: 50px; margin-top: 5px">
              <span style="margin-right: 50px">手机号</span>
              <el-form-item style="margin-right: 5px">
                <el-input
                    style="width: 200px"
                    placeholder="请输入手机号"
                    v-model="phoneNumber"
                />
              </el-form-item>
            </el-row>
          </el-col>
          <div class="address-authorization-system">
            <!-- 时间范围选择器 -->
            <div class="time-range-selector">
              <el-date-picker
                  v-model="startTime"
                  type="datetime"
                  placeholder="开始日期时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="margin-right: 10px;"
              />
              <span style="margin: 0 10px;">至</span>
              <el-date-picker
                  v-model="endTime"
                  type="datetime"
                  placeholder="结束日期时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
              />
            </div>

            <!-- 五级地址联动选择器 -->
            <div class="address-selector">
              <el-select
                  v-model="selectedProvince"
                  placeholder="请选择省份"
                  clearable
                  @change="handleProvinceChange"
              >
                <el-option
                    v-for="province in provinces"
                    :key="province.id"
                    :label="province.name"
                    :value="province.id"
                />
              </el-select>

              <el-select
                  v-model="selectedCity"
                  placeholder="请选择城市"
                  clearable
                  :disabled="!selectedProvince"
                  @change="handleCityChange"
              >
                <el-option
                    v-for="city in cities"
                    :key="city.id"
                    :label="city.name"
                    :value="city.id"
                />
              </el-select>

              <el-select
                  v-model="selectedDistrict"
                  placeholder="请选择区县"
                  clearable
                  :disabled="!selectedCity"
                  @change="handleDistrictChange"
              >
                <el-option
                    v-for="district in districts"
                    :key="district.id"
                    :label="district.name"
                    :value="district.id"
                />
              </el-select>

              <el-select
                  v-model="selectedStreet"
                  placeholder="请选择街道"
                  clearable
                  :disabled="!selectedDistrict"
              >
                <el-option
                    v-for="street in streets"
                    :key="street.id"
                    :label="street.name"
                    :value="street.id"
                />
              </el-select>


            </div>

            <!-- 授权按钮 -->
            <div class="authorization-actions1">
              <el-button
                  class="power"
                  type="primary"
                  @click="handleAreaAuthorization"
              >
                保存
              </el-button>
            </div>
            <div class="power-b">
              <el-button @click="selectBAuthorization" :class="{ active: authorizationType === 'B' }">B授权</el-button>
              <el-button @click="selectCAuthorization" :class="{ checked: authorizationType === 'C' }">C授权</el-button>
            </div>
          </div>
          <div class="right5" style="height:350px">
            <el-row style="margin-left: 30px">
              <el-radio-group v-model="areaProportionSwitch" @change="handleAreaProportionSwitchChange">
                <el-radio value="0" size="large">开启</el-radio>
                <el-radio value="1" size="large">关闭</el-radio>
              </el-radio-group>
            </el-row>
            <el-row
                style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
            >

              <span class="province">省</span>

              <el-input
                  class="rate1"
                  placeholder="请输入省份"
                  v-model="areaProportionForm.lvel1Proportion"
                  :disabled="areaProportionSwitch === '1'"
              />
              <span class="percent">%</span>

              <el-row>
                <span class="province">市</span>

                <el-input
                    class="rate1"
                    placeholder="请输入市"
                    v-model="areaProportionForm.lvel2Proportion"
                    :disabled="areaProportionSwitch === '1'"
                />
                <span class="percent">%</span>

              </el-row>
              <el-row>
                <span class="province">县、区</span>

                <el-input
                    class="rate1"
                    placeholder="请输入县"
                    v-model="areaProportionForm.lvel3Proportion"
                    :disabled="areaProportionSwitch === '1'"
                />
                <span class="percent">%</span>

              </el-row>
              <el-row>
                <span class="province">镇、街</span>

                <el-input
                    class="rate1"
                    placeholder="请输入镇"
                    v-model="areaProportionForm.lvel4Proportion"
                    :disabled="areaProportionSwitch === '1'"
                />
                <span class="percent">%</span>

              </el-row>
            </el-row>
            <el-button type="primary" class="save-address" @click="submitAreaProportion" :disabled="areaProportionSwitch === '1'"
            >保存
            </el-button
            >
          </div>
        </el-row>
      </div>
    </div>
  </div>

</template>
<script lang="ts" setup>
import HomeBg from "../../components/HomeBg.vue";
import {
  computed,
  getCurrentInstance,
  onMounted,
  onUnmounted,
  watch,
  ref,
  nextTick,
} from "vue";
import {
  deleteNewEnterprise,
  getEnterpriseDataSet,
  getAllCooperateEnterprise,
  createNewEnterprise,
  getEnterpriseDataName,
  deleteNewEnterpriseDataName,
  addEnterpriseDataName,
  setCustomConstants,
  getCustomConstants,
  getEnterpriseQuantitySet,
  addEnterpriseQuantity,
  deleteEnterpriseQuantitySet,
  getEnterpriseQuantityName,
  addEnterpriseQuantityName,
  deleteEnterpriseQuantityName,
  setEnterpriseDataSwitch,
  setQuantityDataSwitch,
  setCustomConstantsSwitch,
  saveOrUpdateDailyTradePercentage,
  getDailyTradePercentage,
  saveOrUpdatePartnerEnterpriseAdminData,
  getPartnerEnterpriseAdminData,
  saveOrUpdateBSettings,
  getBSettings,
  saveOrUpdateCSettings,
  getCSettings,
  getCooperateEnterpriseForArea,
  getArea,
  saveAreaAuthorize,
  saveOrUpdateAreaProportion,
  getAreaProportion,
  saveOrUpdateQuantifyCount,
  getQuantifyCount
} from '@/api/systemSet'
import {useRouter} from "vue-router";
import {ElMessage} from 'element-plus';
const enterpriseQuantityData = ref([])
const router = useRouter();
const inputValue1 = ref(null);
const enterpriseInputValue = ref(null);
const inputValue = ref("");
  const switchValue = ref("1")//第二个开关

// 所有企业各ID每日每笔新交易数据的量化数比相关变量
const dailyTradePercentageSwitch = ref("0") // 开关状态
const dailyTradePercentageForm = ref({
  dailyTradePercentage: '',
  ranking1: '',
  ranking1Percentage: '',
  ranking2: '',
  ranking2Percentage: ''
})

// 所有合作企业Admain的各ID每日每笔数据量化数比相关变量
const partnerEnterpriseAdminSwitch = ref("0") // 开关状态
const partnerEnterpriseAdminForm = ref({
  dailyDataPercentage: ''
})

// 合作企业各IDB设置相关变量
const bSettingsSwitch = ref("0") // 开关状态 (0:开启, 1:关闭)
const bSettingsForm = ref({
  quantifyToCredit: '',
  creditToCoupon: ''
})

// 合作企业各IDC设置相关变量
const cSettingsSwitch = ref("0") // 开关状态 (0:开启, 1:关闭)
const cSettingsForm = ref({
  quantifyToCredit: '',
  creditToCoupon: ''
})

// 分量进化设置相关变量
const quantityEvolutionSwitch = ref("0") // 开关状态 (0:开启, 1:关闭)
const quantityEvolutionForm = ref({
  proportion: ''
})

// 所合作企业区域的设置相关变量
const areaSettingsSwitch = ref("0") // 开关状态 (0:开启, 1:关闭)
const selectedEnterprise = ref("") // 选中的企业ID
const enterpriseList = ref([]) // 企业列表
const phoneNumber = ref("") // 手机号
const authorizationType = ref("B") // 授权类型 B或C
const startTime = ref("") // 开始时间
const endTime = ref("") // 结束时间
const selectedProvince = ref("")
const selectedCity = ref("")
const selectedDistrict = ref("")
const selectedStreet = ref("")
const provinces = ref([])
const cities = ref([])
const districts = ref([])
const streets = ref([])

// 初始化开关状态，优先从 localStorage 读取
const customConstantsSwitch = ref(localStorage.getItem('customConstantsSwitch') || "0")
const enterpriseDataSwitch = ref(localStorage.getItem('enterpriseDataSwitch') || "0")
const quantityDataSwitch = ref(localStorage.getItem('quantityDataSwitch') || "0")

// 当开关变化时自动保存到 localStorage
watch(customConstantsSwitch, (newVal) => {
  localStorage.setItem('customConstantsSwitch', newVal)
})

watch(enterpriseDataSwitch, (newVal) => {
  localStorage.setItem('enterpriseDataSwitch', newVal)
})

watch(quantityDataSwitch, (newVal) => {
  localStorage.setItem('quantityDataSwitch', newVal)
})
const radio4 = ref("1");
const radio5 = ref();
const radio6 = ref("1");
const radio7 = ref("1");
const radio8 = ref("1");
const radio9 = ref("1");
const radio10 = ref("1");
const value1 = ref([]);
const value2 = ref([]);
const value3 = ref([]);
const value4 = ref([]);
const message = ref('')
const message1 = ref('')
const message2 = ref('')
const message3 = ref('')
const message4 = ref('')
const message5 = ref('')
const message6 = ref('')
const message7 = ref('')
const message8 = ref('')
const message9 = ref('')
const message10 = ref('')
const message11 = ref('')
const message12 = ref('')
const message13 = ref('')
const message14 = ref('')
const message15 = ref('')
const message16 = ref('')
const displayNumber = ref('')
const tradeAmountTotal = ref(0)
const enteypriseTradeAmountTotal = ref(0)
const resultValue = ref('')
const submit1 = () => {
  if (message.value) {
    // 可以在这里加校验，比如是否为数字等
    displayNumber.value = message.value;
    // ElMessage.success('保存成功');
  } else {
    // ElMessage.warning('请输入数值');
  }
};
const {proxy} = getCurrentInstance()!;
const tableData1 = ref([]);
const tableData = ref([]);
const options = ref([
  {
    value: "Option1",
    province: "广东省",
    city: "揭阳市",
    district: "惠来县",
    street: "周田镇",
  },
  {
    value: "Option2",
    province: "北京市",
    city: "北京市",
    district: "东城区",
    street: "景山街道",
  },
  {
    value: "Option3",
    province: "北京市",
    city: "北京市",
    district: "东城区",
    street: "景山街道",
  },
  {
    value: "Option4",
    province: "北京市",
    city: "北京市",
    district: "东城区",
    street: "景山街道",
  },
  {
    value: "Option5",
    province: "北京市",
    city: "北京市",
    district: "东城区",
    street: "景山街道",
  },
]);
const provinceOptions = ref([
  {value: "gd", label: "广东省"},
  {value: "zj", label: "浙江省"},
  // 其他省份...
]);
// 原始数据
const allOptions = {
  provinces: [
    {value: "gd", label: "广东省"},
    {value: "zj", label: "浙江省"},
  ],
  cities: {
    gd: [
      {value: "gz", label: "广州市"},
      {value: "sz", label: "深圳市"},
    ],
    zj: [
      {value: "hz", label: "杭州市"},
      {value: "nb", label: "宁波市"},
    ],
  },
  districts: {
    gz: [
      {value: "th", label: "天河区"},
      {value: "yx", label: "越秀区"},
    ],
    sz: [
      {value: "ft", label: "福田区"},
      {value: "ns", label: "南山区"},
    ],
  },
  towns: {
    th: [
      {value: "th1", label: "天河南街道"},
      {value: "th2", label: "林和街道"},
    ],
    yx: [
      {value: "yx1", label: "北京街道"},
      {value: "yx2", label: "人民街道"},
    ],
  },
};

const connAll = ref([])
const selectKey = ref(0) // 用于强制刷新下拉框

// 监控 connAll 数据变化
watch(connAll, (newVal) => {
  console.log('connAll 数据变化:', newVal)
  selectKey.value++ // 强制刷新下拉框
}, { deep: true })

// 假设这是你的全局唯一ID生成器
let nextId = tableData1.value.length > 0 ? Math.max(...tableData1.value.map(item => item.id)) + 1 : 1;
// 添加方法
const addToTable1 = async () => {
  if (!inputValue1.value) {
    ElMessage.warning('请选择一个企业');
    return;
  }
  // 根据 name 查找 id
  const selected = connAll.value.find(item => item.name === inputValue1.value);
  const enterpriseId = selected.id;
  try {
    const res = await createNewEnterprise(enterpriseId); // 调用接口，传入 id

    if (res.code === 200) {
      ElMessage.success(res.message);
      inputValue1.value = ''; // 清空输入框
      await getCooperateEnterprise(); // 刷新表格
    } else {
      ElMessage.error(res.message || '操作失败');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error(error);
  }
};
// 加载下拉的企业
const getAllCooperate = async () => {
  try {
    // 获取下拉合作企业的接口
    const res = await getAllCooperateEnterprise();
    console.log('getAllCooperateEnterprise API响应:', res)
    if (res.code === 200 && Array.isArray(res.data?.cooperateEnterprises)) {
      // 清理企业名称的空格并设置数据
      const cleanedData = res.data.cooperateEnterprises.map(item => ({
        ...item,
        name: item.name.trim() // 去除名称前后的空格
      }));

      connAll.value = cleanedData;
      enterpriseAll.value = cleanedData;

      await nextTick()
      console.log('企业数据设置成功:', {
        connAll: connAll.value,
        enterpriseAll: enterpriseAll.value,
        connAllLength: connAll.value.length
      })
    } else {
      console.log('企业数据格式异常:', res)
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
};
const isVisible = ref(true); // 初始设置为 true，即默认显示
const isSelected = ref(false);

// 定义一个方法来切换 isVisible 的值
function toggleDisplay() {
  isVisible.value = true; // 切换 isVisible 的值
}

function closeDisplay() {
  isVisible.value = false;
}

const isFormVisible = ref(true);
const openForm = () => {
  isFormVisible.value = !isFormVisible.value;
};
//删除自定义名称
const handleDelete = (index) => {
  tableData.value.splice(index, 1);
};
//删除企业名称
const handleDelete1 = (id) => {
  var data = {
    enterpriseId: id
  }
  deleteNewEnterprise(data).then(() => {
    getCooperateEnterprise()
    getEnterTranctionData()
  })
};
//统计每日自动更新合计
// const totalItems = computed(() => {
//   getEnterTranctionData()
// });
//点击提交成功
// function submit() {
//   alert("保存成功！！");
//   console.log(message.value);
//
// }

//点击授权成功
function submitPower() {
  alert("授权成功！！");
}

//一旦授权成功不再授权

// 响应式数据
const selectedCommunity = ref("");
const authorizedList = ref([]);

// 区域比例设置相关变量
const areaProportionSwitch = ref("1") // 开关状态 (0:开启, 1:关闭)
const areaProportionForm = ref({
  lvel1Proportion: '',
  lvel2Proportion: '',
  lvel3Proportion: '',
  lvel4Proportion: ''
})


// 计算属性
const canAuthorize = computed(() => {
  // 必须选择时间范围和至少省份
  return startTime.value && endTime.value && selectedProvince.value;
});

// 方法
const handleDistrictChangeOld = () => {
  selectedStreet.value = "";
  selectedCommunity.value = "";
};

const handleAuthorization = () => {
  if (!canAuthorize.value) return;

  // 获取各层级地址名称
  const provinceName =
      provinces.value.find((p) => p.value === selectedProvince.value)?.label ||
      "";
  const cityName =
      cities.value.find((c) => c.value === selectedCity.value)?.label || "";
  const districtName =
      districts.value.find((d) => d.value === selectedDistrict.value)?.label ||
      "";
  const streetName =
      streets.value.find((s) => s.value === selectedStreet.value)?.label || "";

  authorizedList.value.push({
    startTime: timeRange.value[0],
    endTime: timeRange.value[1],
    province: provinceName,
    city: cityName,
    district: districtName,
    street: streetName,
    fullAddress: `${provinceName}${cityName}${districtName}${streetName}`,

  });
  console.log(authorizedList.value);

  // 清空选择
  timeRange.value = [];
  selectedProvince.value = "";
  selectedCity.value = "";
  selectedDistrict.value = "";
  selectedStreet.value = "";
  selectedCommunity.value = "";
};

const handleRevoke = (index) => {
  authorizedList.value.splice(index, 1);
};
// 撤销授权
const revokeAuthorization = (index) => {
  authorizedAddresses.value.splice(index, 1);
};
//保存按钮

const handleButtonClick = (item) => {

  // if(item==="基础设置") router.push("/roleList")
  if (item === "系统设置") router.push("/basicSet");
  if (item === "系统查询") router.push("/systemQuery");
  // if (item === "状态数据") router.push("/systemQuery");
  if (item === "状态数据") router.push("/stateData");
};

const buttonList = ["系统设置", "系统查询", "状态数据"];
// 获取合作企业列表
const getCooperateEnterprise = async () => {
  try {
    const res = await getEnterpriseDataSet();
    console.log('getEnterpriseDataSet API响应:', res)
    if (res?.data?.cooperateEnterprise) {
      // 清理企业名称的空格
      const cleanedData = res.data.cooperateEnterprise.map(item => ({
        ...item,
        name: item.name.trim() // 去除名称前后的空格
      }));
      tableData1.value = cleanedData;
      console.log('已添加企业列表数据:', tableData1.value)
    }
  } catch (error) {
    console.error('获取已添加企业列表失败:', error)
  }
}
//企业开关
  const getEnterpriseDataSwitch = async () => {
    const payload = {
      enterpriseDataSwitch: enterpriseDataSwitch.value,

    };

    try {
      const res = await setEnterpriseDataSwitch(payload);
      if (res.code === 200)   {
        console.log('res',res)
        fetchCustomConstant()
      } else {
        ElMessage.error(res.msg || '保存失败');
      }
    } catch (error) {
      console.error('请求失败:', error);
      ElMessage.error('请求失败，请检查网络或重试');
    }
  };
  //交易数据开关
  const getQuantityDataSwitch = async () => {
    const payload = {
      quantityDataSwitch: quantityDataSwitch.value,

    };

    try {
      const res = await setQuantityDataSwitch(payload);
      if (res.code === 200)   {
        console.log('res',res)
        fetchCustomConstant()
      } else {
        ElMessage.error(res.msg || '保存失败');
      }
    } catch (error) {
      console.error('请求失败:', error);
      ElMessage.error('请求失败，请检查网络或重试');
    }
  }
  //自定义常数开关
  const getCustomConstantsSwitch = async () => {
    const payload = {
      customConstantsSwitch: customConstantsSwitch.value,

    };

    try {
      const res = await setCustomConstantsSwitch(payload);
      if (res.code === 200)   {
        console.log('res',res)
        fetchCustomConstant()
      } else {
        ElMessage.error(res.msg || '保存失败');
      }
    } catch (error) {
      console.error('请求失败:', error);
      ElMessage.error('请求失败，请检查网络或重试');
    }
  }
// 页面加载时获取初始值
const fetchCustomConstant = async () => {
  try {
    const res = await getCustomConstants()

    // 如果接口返回了 data，则从中提取数据
    if (res.code === 200 && res.data !== undefined) {

      // 重点逻辑：只在 localStorage 没有值时才用接口数据
      customConstantsSwitch.value =
          localStorage.getItem('customConstantsSwitch') ?? res.data.customConstantsSwitch ?? "0"

      enterpriseDataSwitch.value =
          localStorage.getItem('enterpriseDataSwitch') ?? res.data.enterpriseDataSwitch ?? "0"

      quantityDataSwitch.value =
          localStorage.getItem('quantityDataSwitch') ?? res.data.quantityDataSwitch ?? "0"

      // 同步更新 localStorage，防止下次刷新丢失
      localStorage.setItem('customConstantsSwitch', customConstantsSwitch.value)
      localStorage.setItem('enterpriseDataSwitch', enterpriseDataSwitch.value)
      localStorage.setItem('quantityDataSwitch', quantityDataSwitch.value)

      // 其他数据处理
      message.value = String(res.data.constants || '')
      resultValue.value = String(res.data.result || '')

    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')

    // 出错时也尝试从 localStorage 恢复
    customConstantsSwitch.value = localStorage.getItem('customConstantsSwitch') || "0"
    enterpriseDataSwitch.value = localStorage.getItem('enterpriseDataSwitch') || "0"
    quantityDataSwitch.value = localStorage.getItem('quantityDataSwitch') || "0"
  }
}
  //查询企业交易数据
  const getEnterTranctionData = async () => {
    try {
      const res = await getEnterpriseDataName();
      if (res.code === 200) {
        if (res?.data?.enterpriseDataName) {
          tableData.value = res.data.enterpriseDataName.map(item => ({
            ...item,
            trade_amount: Number(item.trade_amount) //
          }));
          tradeAmountTotal.value = Number(res.data.tradeAmountTotal);
        }
        //每天自动更新
        if (res.data.tradeAmountTotal !== undefined && res.data.tradeAmountTotal > 0) {
          tradeAmountTotal.value = res.data.tradeAmountTotal;
        }
      }
    } catch (error) {
      console.log(error)
    }
  }
//删除企业交易数据
const handelDeleteEnterpriseDataName = (id) => {
  var data = {
    enterpriseProductDataId: id,
  }
  deleteNewEnterpriseDataName(data).then((res) => {
    getEnterTranctionData()
  })
}
const formData = ref({
  trade_name: '',
  trade_amount: 0,
})
// 添加按钮逻辑
const addToTable = () => {
  const name = formData.value.trade_name.trim();

  if (!name) {
    ElMessage.warning("名称不能为空");
    return;
  }

  let ids = connAll.value.map(item => item.id).join(',');
  let data = {
    "operate" : "add",
    "type" : 0,
    "dataTypeName" : name,
  }
  addEnterpriseDataName(data)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success("添加成功");
          getEnterTranctionData(); // 刷新表格数据
          formData.value.trade_name = ''; // 清空输入框
        } else {
          // 处理非 200 的响应码（如后端自定义错误）
          ElMessage.error(res.message || "添加失败");
        }
      })
      .catch(error => {
        // 捕获网络错误或后端抛出的异常
        console.error('添加失败:', error);

        // 如果后端返回的是特定错误信息
        if (error.response?.data?.message) {
          ElMessage.error(error.response.data.message);
        } else {
          ElMessage.error("数据名称已经存在不能重复添加!");
        }
      })
};



// 点击保存按钮触发的方法
const submitCustomConstants = async () => {
  const inputVal = message.value.trim()

  if (!inputVal) {
    ElMessage.warning('请输入自定义常数值')
    return
  }

  if (isNaN(Number(inputVal))) {
    ElMessage.error('请输入合法的数字')
    return
  }
  try {
    const payload = {
      customConstants: Number(inputVal),

    }
    console.log('发送的数据:', payload)

    const res = await setCustomConstants(payload)
    if (res.code === 200) {
      ElMessage.success('保存成功')
      message.value = inputVal
       fetchCustomConstant()
      // message.value = ''
      // resultValue.value = ''

    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage.error(error?.response?.data?.message || '网络异常，请稍后再试')
  }
}

//各企业系统每日更新总累计量化数添加
const enterpriseAll = ref([])
const enterpriseData = ref([])
//查看各企业系统
const getEnterpriseAllQuantitySet  = async () => {
  try{
    const res = await getEnterpriseQuantitySet()
    console.log(res)
    if(res.code === 200) {
      enterpriseData.value  = res.data.cooperateEnterprise
    }
  }catch(error){
    console.error('初始化数据失败:', error);
  }
}
//添加各企业系统每日更新总累计量化数企业名称
const addToEnterprise = async () => {
  if(!enterpriseInputValue){
    ElMessage.warning("请选择一个企业")
    return
  }
  //根据企业名查找ID
  const selectedEnterprise = enterpriseAll.value.find(item=>item.name === enterpriseInputValue.value)
  const selectedEnterpriseId = selectedEnterprise.id
  try{
    const res = await addEnterpriseQuantity(selectedEnterpriseId)
    if(res.code === 200){
      ElMessage.success(res.message )
    await getEnterpriseAllQuantitySet()
    }else {
      ElMessage.error(res.message || '操作失败');
    }
  }catch(error){
    console.error(error);
    ElMessage.error(error);
    }
}
//删除已添加各企业系统每日更新总累计量化数企业名称
const handelDeleteEnterprise = async (id) => {
  try{
    const data = { enterpriseId: id }
    const res = await deleteEnterpriseQuantitySet(data)
    if(res.code === 200){
      ElMessage.success(res.message )
      await getEnterpriseAllQuantitySet()
    }else{
      ElMessage.error(res.message );
    }
  }catch (error){
    console.error('<UNK>:', error);
  }
}
//查询各企业系统每日更新总累计量化数交易数据
const getAllEnterpriseQuantityName = async () => {
  try {
    const res = await getEnterpriseQuantityName()
    if(res.code === 200){
      console.log(res.data)
      enterpriseQuantityData.value = res.data.enterpriseDataName.map(item=>({
        ...item,
        trade_amount:Number(item.trade_amount)

      }))
      //每天自动更新
      enteypriseTradeAmountTotal.value = res.data.tradeAmountTotal
      if (res.data.tradeAmountTotal !== undefined && res.data.tradeAmountTotal > 0) {
        enteypriseTradeAmountTotal.value = res.data.tradeAmountTotal;
      }
    }
  }catch (error){
    console.log(error)
    ElMessage.error(error);
  }
}
const enterpriseTransctionData  = ref({
  trade_name: '',
  trade_amount: 0,
})
//新增各企业系统每日更新总累计量化数交易数据
const addToEnterpriseTable =  async () => {
const name = enterpriseTransctionData.value.trade_name.trim()
  if(!name){
    ElMessage.warning('名称不能为空');
    return
  }
  let ids = enterpriseAll.value.map(item=>item.id).join(',')
  let data = {
    "enterpriseIds" : ids,
    "tradeName" : name,
  }
  const res = await addEnterpriseQuantityName(data)
  try{
    if(res.code === 200){
      ElMessage.success(res.message )
     await getAllEnterpriseQuantityName()
      enterpriseTransctionData.value.trade_name = ''
    }else{
      ElMessage.error(res.message)
    }
  }catch(error){
    ElMessage.error(error);
    console.log(error)
    // 如果后端返回的是特定错误信息
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message);
    } else {
      ElMessage.error("数据名称已经存在不能重复添加!");
    }
  }
}
//删除各企业系统每日更新总累计量化数交易数据
const handelDeleteEnterpriseQuantityName =(id)=>{
  var data = {enterpriseProductDataId:id}
  deleteEnterpriseQuantityName(data).then((res)=>{
    getAllEnterpriseQuantityName()
  })
}

// 所有企业各ID每日每笔新交易数据的量化数比相关方法
// 开关变化处理
const handleDailyTradePercentageSwitchChange = async () => {
  console.log('开关状态变化:', dailyTradePercentageSwitch.value)

  // 当选择关闭时，自动保存数据
  if (dailyTradePercentageSwitch.value === '1') {
    try {
      const payload = {
        isEnabled: parseInt(dailyTradePercentageSwitch.value),
        dailyTradePercentage: parseFloat(dailyTradePercentageForm.value.dailyTradePercentage) || 0,
        ranking1: parseFloat(dailyTradePercentageForm.value.ranking1) || 0,
        ranking1Percentage: parseFloat(dailyTradePercentageForm.value.ranking1Percentage) || 0,
        ranking2: parseFloat(dailyTradePercentageForm.value.ranking2) || 0,
        ranking2Percentage: parseFloat(dailyTradePercentageForm.value.ranking2Percentage) || 0
      }

      const res = await saveOrUpdateDailyTradePercentage(payload)
      if (res.code === 200) {
        ElMessage.success(res.message || '保存成功')
      } else {
        ElMessage.error(res.message)
      }
    } catch (error) {
      console.error('自动保存失败:', error)
      if (error?.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else if (error?.message) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('保存失败')
      }
    }
  }
}

// 获取初始数据
const fetchDailyTradePercentageData = async () => {
  try {
    const res = await getDailyTradePercentage()
    if (res.code === 200 && res.data) {
      dailyTradePercentageSwitch.value = res.data.isEnabled || "0"
      dailyTradePercentageForm.value = {
        dailyTradePercentage: res.data.dailyTradePercentage || '',
        ranking1: res.data.ranking1 || '',
        ranking1Percentage: res.data.ranking1Percentage || '',
        ranking2: res.data.ranking2 || '',
        ranking2Percentage: res.data.ranking2Percentage || ''
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 保存数据
const submitDailyTradePercentage = async () => {
  try {
    // 验证数据
    const form = dailyTradePercentageForm.value
    if (!form.dailyTradePercentage || !form.ranking1 || !form.ranking1Percentage ||
        !form.ranking2 || !form.ranking2Percentage) {
      ElMessage.warning('请填写完整的数据')
      return
    }

    const payload = {
      isEnabled: parseInt(dailyTradePercentageSwitch.value),
      dailyTradePercentage: parseFloat(form.dailyTradePercentage),
      ranking1: parseFloat(form.ranking1),
      ranking1Percentage: parseFloat(form.ranking1Percentage),
      ranking2: parseFloat(form.ranking2),
      ranking2Percentage: parseFloat(form.ranking2Percentage)
    }

    const res = await saveOrUpdateDailyTradePercentage(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '保存成功')
    } else {
      // 显示后台返回的具体错误信息
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.error('保存失败:', error)
    // 优先显示后台返回的错误信息
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 所有合作企业Admain的各ID每日每笔数据量化数比相关方法
// 开关变化处理
const handlePartnerEnterpriseAdminSwitchChange = async () => {
  console.log('合作企业Admin开关状态变化:', partnerEnterpriseAdminSwitch.value)

  // 当选择关闭时，自动保存数据
  if (partnerEnterpriseAdminSwitch.value === '1') {
    try {
      // 处理数据量化数比值，如果为空或无效则使用0
      const dailyDataPercentageValue = parseFloat(partnerEnterpriseAdminForm.value.dailyDataPercentage)
      const finalDailyDataPercentage = isNaN(dailyDataPercentageValue) ? 0 : dailyDataPercentageValue

      const payload = {
        isEnabled: parseInt(partnerEnterpriseAdminSwitch.value),
        dailyDataPercentage: finalDailyDataPercentage
      }

      const res = await saveOrUpdatePartnerEnterpriseAdminData(payload)
      if (res.code === 200) {
        ElMessage.success(res.message || '保存成功')
      } else {
        ElMessage.error(res.message)
      }
    } catch (error) {
      console.error('自动保存失败:', error)
      if (error?.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else if (error?.message) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('保存失败')
      }
    }
  }
}

// 获取初始数据
const fetchPartnerEnterpriseAdminData = async () => {
  try {
    const res = await getPartnerEnterpriseAdminData()
    if (res.code === 200 && res.data) {
      partnerEnterpriseAdminSwitch.value = res.data.isEnabled || "0"
      partnerEnterpriseAdminForm.value = {
        dailyDataPercentage: res.data.dailyDataPercentage || ''
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 保存数据
const submitPartnerEnterpriseAdminData = async () => {
  try {
    // 验证数据
    const form = partnerEnterpriseAdminForm.value
    if (!form.dailyDataPercentage) {
      ElMessage.warning('请填写数据量化数比')
      return
    }

    // 验证是否为有效数字
    const dailyDataPercentageValue = parseFloat(form.dailyDataPercentage)
    if (isNaN(dailyDataPercentageValue)) {
      ElMessage.warning('请输入有效的数字')
      return
    }

    const payload = {
      isEnabled: parseInt(partnerEnterpriseAdminSwitch.value),
      dailyDataPercentage: dailyDataPercentageValue
    }

    const res = await saveOrUpdatePartnerEnterpriseAdminData(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '保存成功')
    } else {
      // 显示后台返回的具体错误信息
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.error('保存失败:', error)
    // 优先显示后台返回的错误信息
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 合作企业各IDB设置相关方法
// 开关变化处理
const handleBSettingsSwitchChange = async () => {
  console.log('合作企业IDB设置开关状态变化:', bSettingsSwitch.value)

  // 当选择关闭时，自动保存数据
  if (bSettingsSwitch.value === '1') {
    try {
      // 处理数据值，如果为空或无效则使用0
      const quantifyToCreditValue = parseFloat(bSettingsForm.value.quantifyToCredit)
      const creditToCouponValue = parseFloat(bSettingsForm.value.creditToCoupon)

      const payload = {
        isEnabled: parseInt(bSettingsSwitch.value),
        quantifyToCredit: isNaN(quantifyToCreditValue) ? 0 : quantifyToCreditValue,
        creditToCoupon: isNaN(creditToCouponValue) ? 0 : creditToCouponValue
      }

      const res = await saveOrUpdateBSettings(payload)
      if (res.code === 200) {
        ElMessage.success(res.message || '保存成功')
      } else {
        ElMessage.error(res.message)
      }
    } catch (error) {
      console.error('自动保存失败:', error)
      if (error?.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else if (error?.message) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('保存失败')
      }
    }
  }
}

// 获取初始数据
const fetchBSettingsData = async () => {
  try {
    const res = await getBSettings()
    if (res.code === 200 && res.data) {
      bSettingsSwitch.value = res.data.isEnabled || "0"
      bSettingsForm.value = {
        quantifyToCredit: res.data.quantifyToCredit || '',
        creditToCoupon: res.data.creditToCoupon || ''
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 保存数据
const submitBSettings = async () => {
  try {
    // 验证数据
    const form = bSettingsForm.value
    if (!form.quantifyToCredit || !form.creditToCoupon) {
      ElMessage.warning('请填写完整的数据')
      return
    }

    // 验证是否为有效数字
    const quantifyToCreditValue = parseFloat(form.quantifyToCredit)
    const creditToCouponValue = parseFloat(form.creditToCoupon)

    if (isNaN(quantifyToCreditValue)) {
      ElMessage.warning('请输入有效的量化值进化量化数值')
      return
    }

    if (isNaN(creditToCouponValue)) {
      ElMessage.warning('请输入有效的量化进化平台促销券数值')
      return
    }

    const payload = {
      isEnabled: parseInt(bSettingsSwitch.value),
      quantifyToCredit: quantifyToCreditValue,
      creditToCoupon: creditToCouponValue
    }

    const res = await saveOrUpdateBSettings(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '保存成功')
    } else {
      // 显示后台返回的具体错误信息
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.error('保存失败:', error)
    // 优先显示后台返回的错误信息
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 合作企业各IDC设置相关方法
const handleCSettingsSwitchChange = async () => {
  if (cSettingsSwitch.value === '1') {
    try {
      const quantifyToCreditValue = parseFloat(cSettingsForm.value.quantifyToCredit)
      const creditToCouponValue = parseFloat(cSettingsForm.value.creditToCoupon)

      const payload = {
        isEnabled: parseInt(cSettingsSwitch.value),
        quantifyToCredit: isNaN(quantifyToCreditValue) ? 0 : quantifyToCreditValue,
        creditToCoupon: isNaN(creditToCouponValue) ? 0 : creditToCouponValue
      }

      const res = await saveOrUpdateCSettings(payload)
      if (res.code === 200) {
        ElMessage.success(res.message || '保存成功')
      } else {
        ElMessage.error(res.message)
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error('保存失败')
      }
    }
  }
}

const fetchCSettingsData = async () => {
  try {
    const res = await getCSettings()
    if (res.code === 200 && res.data) {
      cSettingsSwitch.value = res.data.isEnabled || "0"
      cSettingsForm.value = {
        quantifyToCredit: res.data.quantifyToCredit || '',
        creditToCoupon: res.data.creditToCoupon || ''
      }
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

const submitCSettings = async () => {
  try {
    const form = cSettingsForm.value
    if (!form.quantifyToCredit || !form.creditToCoupon) {
      ElMessage.warning('请填写完整的数据')
      return
    }

    const quantifyToCreditValue = parseFloat(form.quantifyToCredit)
    const creditToCouponValue = parseFloat(form.creditToCoupon)

    if (isNaN(quantifyToCreditValue) || isNaN(creditToCouponValue)) {
      ElMessage.warning('请输入有效的数字')
      return
    }

    const payload = {
      isEnabled: parseInt(cSettingsSwitch.value),
      quantifyToCredit: quantifyToCreditValue,
      creditToCoupon: creditToCouponValue
    }

    const res = await saveOrUpdateCSettings(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '保存成功')
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 分量进化设置相关方法
const handleQuantityEvolutionSwitchChange = async () => {
  if (quantityEvolutionSwitch.value === '1') {
    try {
      const proportionValue = parseFloat(quantityEvolutionForm.value.proportion)

      const payload = {
        isEnabled: parseInt(quantityEvolutionSwitch.value),
        proportion: isNaN(proportionValue) ? 0 : proportionValue
      }

      const res = await saveOrUpdateQuantifyCount(payload)
      if (res.code === 200) {
        ElMessage.success(res.message || '保存成功')
      } else {
        ElMessage.error(res.message)
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error('保存失败')
      }
    }
  }
}

const fetchQuantityEvolutionData = async () => {
  try {
    const res = await getQuantifyCount()
    if (res.code === 200 && res.data) {
      quantityEvolutionSwitch.value = res.data.is_enabled || "0"
      quantityEvolutionForm.value = {
        proportion: res.data.proportion || ''
      }
    }
  } catch (error) {
    console.error('获取分量进化设置数据失败:', error)
    // 如果获取失败，使用默认值
    quantityEvolutionSwitch.value = "0"
    quantityEvolutionForm.value = {
      proportion: ''
    }
  }
}

const submitQuantityEvolution = async () => {
  try {
    const form = quantityEvolutionForm.value
    if (!form.proportion) {
      ElMessage.warning('请填写每日每ID的分量进化量化数')
      return
    }

    const proportionValue = parseFloat(form.proportion)

    if (isNaN(proportionValue)) {
      ElMessage.warning('请输入有效的数字')
      return
    }

    const payload = {
      isEnabled: parseInt(quantityEvolutionSwitch.value),
      proportion: proportionValue
    }

    const res = await saveOrUpdateQuantifyCount(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '保存成功')
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 所合作企业区域的设置相关方法
// 获取企业列表
const fetchEnterpriseList = async () => {
  try {
    const res = await getCooperateEnterpriseForArea()
    console.log('企业列表API响应:', res)
    if (res.code === 200 && res.data?.cooperateEnterprise) {
      enterpriseList.value = res.data.cooperateEnterprise
      console.log('企业列表数据:', enterpriseList.value)

      // 根据企业状态设置页面开关状态
      // 只要有一个企业是开启状态(onOff: "0")，页面就选择开启
      // 所有企业都是关闭状态(onOff: "1")时，页面才选择关闭
      const hasEnabledEnterprise = enterpriseList.value.some(enterprise => enterprise.onOff === "0")
      areaSettingsSwitch.value = hasEnabledEnterprise ? "0" : "1"
      console.log('页面开关状态设置为:', areaSettingsSwitch.value, '(有开启企业:', hasEnabledEnterprise, ')')
    } else {
      console.log('企业列表数据格式异常:', res)
    }
  } catch (error) {
    console.error('获取企业列表失败:', error)
    ElMessage.error('获取企业列表失败')
  }
}

// 企业选择变化
const handleEnterpriseChange = (enterpriseId) => {
  const enterprise = enterpriseList.value.find(item => item.id === enterpriseId)
  if (enterprise) {
    areaSettingsSwitch.value = enterprise.onOff
  }
}

// 获取地区数据
const fetchAreaData = async (areaId = "") => {
  try {
    const res = await getArea({ areaId })
    if (res.code === 200 && res.data?.areaList) {
      return res.data.areaList
    }
    return []
  } catch (error) {
    ElMessage.error('获取地区数据失败')
    return []
  }
}

// 省份变化
const handleProvinceChange = async () => {
  selectedCity.value = ""
  selectedDistrict.value = ""
  selectedStreet.value = ""
  cities.value = []
  districts.value = []
  streets.value = []

  if (selectedProvince.value) {
    cities.value = await fetchAreaData(selectedProvince.value)
  }
}

// 城市变化
const handleCityChange = async () => {
  selectedDistrict.value = ""
  selectedStreet.value = ""
  districts.value = []
  streets.value = []

  if (selectedCity.value) {
    districts.value = await fetchAreaData(selectedCity.value)
  }
}

// 区县变化
const handleDistrictChange = async () => {
  selectedStreet.value = ""
  streets.value = []

  if (selectedDistrict.value) {
    streets.value = await fetchAreaData(selectedDistrict.value)
  }
}

// B授权选择
const selectBAuthorization = () => {
  authorizationType.value = "B"
}

// C授权选择
const selectCAuthorization = () => {
  authorizationType.value = "C"
}

// 保存区域授权
const handleAreaAuthorization = async () => {
  try {
    /* if (!selectedEnterprise.value) {
        ElMessage.warning('请选择企业')
        return
    }
    if (areaSettingsSwitch.value === "0") {
      if (!phoneNumber.value) {
        ElMessage.warning('请输入手机号')
        return
      }
      if (!startTime.value || !endTime.value) {
        ElMessage.warning('请选择开始时间和结束时间')
        return
      }
      if (!selectedProvince.value) {
        ElMessage.warning('请至少选择省份')
        return
      }
    } */

    // 确定最深层级的地区ID
    let areaId = selectedProvince.value || ""
    if (selectedStreet.value) {
      areaId = selectedStreet.value
    } else if (selectedDistrict.value) {
      areaId = selectedDistrict.value
    } else if (selectedCity.value) {
      areaId = selectedCity.value
    }

    const payload = {
      enterpriseId: selectedEnterprise.value || "",
      areaId: areaId,
      phone: phoneNumber.value || "",
      startTime: startTime.value || "",
      endTime: endTime.value || "",
      onOff: parseInt(areaSettingsSwitch.value),
      type: authorizationType.value
    }

    const res = await saveAreaAuthorize(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '授权成功')
      // 重新获取企业列表数据
      await fetchEnterpriseList()
      // 清空表单
      selectedEnterprise.value = ""
      phoneNumber.value = ""
      startTime.value = ""
      endTime.value = ""
      selectedProvince.value = ""
      selectedCity.value = ""
      selectedDistrict.value = ""
      selectedStreet.value = ""
      cities.value = []
      districts.value = []
      streets.value = []
      authorizationType.value = "B"
      areaSettingsSwitch.value = "0"
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 区域比例设置相关方法
const handleAreaProportionSwitchChange = async () => {
  if (areaProportionSwitch.value === '1') {
    try {
      const form = areaProportionForm.value
      const payload = {
        lvel1Proportion: parseFloat(form.lvel1Proportion) || 0,
        lvel2Proportion: parseFloat(form.lvel2Proportion) || 0,
        lvel3Proportion: parseFloat(form.lvel3Proportion) || 0,
        lvel4Proportion: parseFloat(form.lvel4Proportion) || 0,
        onOff: parseInt(areaProportionSwitch.value)
      }

      const res = await saveOrUpdateAreaProportion(payload)
      if (res.code === 200) {
        ElMessage.success(res.message || '保存成功')
      } else {
        ElMessage.error(res.message)
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error('保存失败')
      }
    }
  }
}

const fetchAreaProportionData = async () => {
  try {
    const res = await getAreaProportion()
    if (res.code === 200 && res.data) {
      areaProportionSwitch.value = res.data.on_off || "1"
      areaProportionForm.value = {
        lvel1Proportion: res.data.lvel1_proportion || '',
        lvel2Proportion: res.data.lvel2_proportion || '',
        lvel3Proportion: res.data.lvel3_proportion || '',
        lvel4Proportion: res.data.lvel4_proportion || ''
      }
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

const submitAreaProportion = async () => {
  try {
    const form = areaProportionForm.value
    if (!form.lvel1Proportion || !form.lvel2Proportion || !form.lvel3Proportion || !form.lvel4Proportion) {
      ElMessage.warning('请填写完整的数据')
      return
    }

    const lvel1Value = parseFloat(form.lvel1Proportion)
    const lvel2Value = parseFloat(form.lvel2Proportion)
    const lvel3Value = parseFloat(form.lvel3Proportion)
    const lvel4Value = parseFloat(form.lvel4Proportion)

    if (isNaN(lvel1Value) || isNaN(lvel2Value) || isNaN(lvel3Value) || isNaN(lvel4Value)) {
      ElMessage.warning('请输入有效的数字')
      return
    }

    const payload = {
      lvel1Proportion: lvel1Value,
      lvel2Proportion: lvel2Value,
      lvel3Proportion: lvel3Value,
      lvel4Proportion: lvel4Value,
      onOff: parseInt(areaProportionSwitch.value)
    }

    const res = await saveOrUpdateAreaProportion(payload)
    if (res.code === 200) {
      ElMessage.success(res.message || '保存成功')
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    if (error?.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('保存失败')
    }
  }
}

onMounted(() => {
  getAllCooperate()
  getCooperateEnterprise()
  getEnterTranctionData()
  getEnterpriseDataSwitch()
  getEnterpriseAllQuantitySet()
  fetchCustomConstant()
  getAllEnterpriseQuantityName()
  fetchDailyTradePercentageData()
  fetchPartnerEnterpriseAdminData()
  fetchBSettingsData()
  fetchCSettingsData()
  fetchQuantityEvolutionData()
  fetchEnterpriseList()
  fetchAreaProportionData()
  // 初始化省份数据
  fetchAreaData().then(data => {
    provinces.value = data
  })

});

</script>
<style lang="scss" scoped>
.active {
  background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
  color: black; /* 确保文字颜色在背景色上清晰可见 */
}

.checked {
  background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
  color: black; /* 确保文字颜色在背景色上清晰可见 */
}

.number {
  margin-top: 20px;
  margin-left: 50px;
  width: 200px;
  height: 30px;
  border: 1px solid #ccc;
}

.ys {
  height: 50px;
  text-align: left;
  margin-left: -80px;
  //  background-color: #9fa0d3;
  border: none;
}

.right-top {
  // border: 1px solid #2a48bf;
  width: 1200px;
  width: 100%;
  height: 100%;
  //margin: 0 auto;
  border-radius: 10px;
  //margin-left: 10%;

}

.right2 {
  height: 300px;
  // background-color: red;
  margin-top: 100px;
  box-shadow: 0px 15px 15px #2a48bf;
  border-radius: 10px;
}

//保存按钮
.save {
  border-radius: 5px;
  margin-left: 450px;
  margin-top: 70px;
}

.save1 {
  border-radius: 5px;
  margin-left: 450px;
  margin-top: 160px;
}
.underline {
  border: none;
  height: 3px; // 线条粗细
  background-color: #000; // 线条颜色
  margin: 10px 0; // 线条与上下内容的间距
  width: 100%; // 横贯整个父容器宽度
}

.save2 {
  border-radius: 5px;
  margin-left: 450px;
  margin-top: 260px;
}

.content {
  width: 100%;
  border-bottom: 1px solid #000;
}

.btn {
  background-color: #fff;
  color: red;
}

.btn-selected {
  background-color: #7dcf80; /* 绿色 */
  color: #fff;
}

.right3 {
  height: 200px;

  span {
    // padding-left: 30px;
  }

  box-shadow: 5px 15px 10px #ccc;
  margin-top: 50px;
  border-radius: 10px;
  box-shadow: 0px 15px 15px #2a48bf;
}

.right4 {
  height: 80px;
  // background-color: red;
  margin-top: 30px;
  border: 1px solid #2a48bf;
  border-radius: 10px;
}

.right5 {
  width: 100%;

  span {
    text-align: center;

  }

  height: 300px;
  box-shadow: 0px 15px 15px #2a48bf;
  margin-top: 20px;
  border-radius: 10px;
}

.right6 {
  height: 380px;

  span {
    // padding-left: 30px;
  }

  box-shadow: 5px 15px 10px #ccc;
  margin-top: 50px;
  border-radius: 10px;
  box-shadow: 0px 15px 15px #2a48bf;
}

.right7 {
  span {
    text-align: center;
    // color: red;
    // padding-left: 30px;
  }

  height: 460px;
  box-shadow: 0px 15px 15px #2a48bf;
  margin-top: 20px;
  border-radius: 10px;
}

//总计
.count {
  height: 30px;
  width: 200px;
  border: 1px solid #ccc;
  margin-top: 15px;
  border-radius: 3px;
  margin-left: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 170px;
}

//量化比
.rate {
  height: 25px;
  width: 200px;
  margin-top: 20px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 370px;
}

.rate1 {
  height: 25px;
  width: 200px;
  margin-top: 20px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 210px;
}

.rate2 {
  height: 25px;
  width: 200px;
  margin-top: 10px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
}

.rate3 {
  height: 25px;
  width: 200px;
  margin-top: 20px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 85px;
}

.rate4 {
  height: 25px;
  width: 200px;
  margin-top: 30px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 180px;
}

.rate5 {
  width: 200px;
  margin-left: 20px;
  height: 30px;
  margin-top: 7px;
}

.percent {
  height: 28px;
  width: 30px;
  margin-top: 18px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
}

.adress {
  display: flex;
}

.province {
  width: 200px;
  margin-left: -40px;
  margin-top: 10px;
}

.save-address {
  border-radius: 5px;
  margin-left: 450px;

  margin-top: 200px;
}

.address-container {
  margin-left: 50px;
  display: flex;
  flex-direction: column;
  // gap: 16px; /* 控制下拉框之间的间距 */
  max-width: 300px; /* 控制整体宽度 */
  flex: 6;
}

.address-item {
  display: flex;
  flex-direction: column;
  gap: 7px; /* 控制标签和下拉框之间的间距 */
}

.address-item p {
  margin: 0;
  font-size: 16px;
  color: #000;
}

.block {
  margin-left: 50px;
  width: 300px;
}

//B授权选中

/* 响应式调整 */
@media (max-width: 768px) {
  .address-container {
    max-width: 100%;
  }

  .el-select {
    width: 50% !important; /* 覆盖内联样式 */
  }
}

.address-authorization-system {
  padding: 20px;
  max-width: 1400px;
  // margin:  auto;
  margin-left: 30px;
}

.address-selector {
  width: 400px;
  margin-bottom: 20px;
}

//授权按钮
.authorization-actions1 {
  position: relative;
}

.power-b {
  position: absolute;
  left: 711px;
  top: 300px;
}

.power {
  position: absolute;
  left: 160px;
  top: -140px;
  width: 200px;
  margin-left: 500px;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;

  .left-buttons {
    position: fixed;
    z-index: 1000;
    width: 200px;
    height: 100%;
    height: 2300px;
    overflow-y: auto;
    background: #fff;
    // border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {

      width: 100%;
      height: 50px;
      background-color: #3a58cf;

      color: #fff;
      font-size: 16px;
      border-radius: 0;
      border: none;
      margin: 0;
      padding: 0;
      display: block;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      transition: background-color 0.3s;

      &:hover {
        background-color: #2a48bf;
      }

      &:first-child {
        border-radius: 0px;
        // border-radius: 8px;
      }

      &:last-child {
        margin-top: 10px;
        border-radius: 0px;
        // border-radius: 8px;
        border-bottom: none;
      }
    }
  }

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  .left-buttons {
    border-right: 1px solid black;
  }

  .left {
    // background-color: #14097a;
    width: 168px;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 4;

    .Text {
      font-size: 40px;
      color: white;
      writing-mode: vertical-rl; /* 如果需要垂直文字 */
      letter-spacing: 10px;
    }
  }

  .el-input__wrapper {
    margin-top: 20px;
  }

  .right {
    margin-left: 107px;
    margin-top: 55px;
    z-index: 4;
    width: calc(100% - 275px); /* 168 + 107 */
    .main-value {
      font-size: 18px;
      font-weight: bold;
      color: #000;
    }

    .evolution-value {
      margin-top: 30px;
      width: 300px;
      text-align: center;
      font-size: 14px;
      // color: #666;
      margin-top: 5px;
    }
  }
}
</style>
