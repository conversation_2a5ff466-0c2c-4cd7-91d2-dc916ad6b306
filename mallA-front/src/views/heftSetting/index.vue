<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import Sidebar from "../../components/Sidebar.vue";
import { getQuantityConfig, saveOrUpdateQuantityConfig } from '@/api/heftSetting'
import { ElMessage } from 'element-plus'
import { Check, Close, Setting, Edit, Plus, Minus, InfoFilled, CirclePlus, TrendCharts } from '@element-plus/icons-vue'

const router = useRouter()
// 表单数据
const form = ref({
  initialAccumulated: '',       // 初始累计获得
  standardComponent: '',        // 分量达标
  dailyVoucher: '',             // 每天新获得
  componentStandard: '',        // 分量指标维持达标
  merchantCount: '',            // 推荐商家数量
  monthlyTechDrainage: '',      // 有技术引流商家数
  merchantUnit: '',             // 每位
  upperLimit: '',               // 上限额度
  accumulatedIncrease: '',      // 累计增加
  newIncrease: '',              // 新增分量
  componentUpperLimit: '',      // 分量上限
  amount: ''                    // 平台补贴金
})

// 表单规则
const rules = reactive({
  initialAccumulated: [{ required: true, message: '请输入初始累计获得值', trigger: 'blur' }],
  standardComponent: [{ required: true, message: '请输入分量达标值', trigger: 'blur' }],
  dailyVoucher: [{ required: true, message: '请输入每天新获得值', trigger: 'blur' }],
})

// 开关状态（onOff）
const radio1 = ref(localStorage.getItem('onOff') || '0')
const switchValue = ref(radio1.value === '0')

// 加载状态
const loading = ref(false)
const saveLoading = ref(false)

// 监听开关变化
const handleSwitchChange = (val) => {
  radio1.value = val ? '0' : '1'
}

const handleButtonClick = (item) => {
  if(item === '引流设置') router.push('/technicalDrainageB')
  if(item === '分量设置') router.push('/heftSetting')
}

const menuItem = ref([
  {label:'引流设置', path:'/technicalDrainageB', icon: 'el-icon-data-line'},
  {label:'分量设置', path:'/heftSetting', icon: 'el-icon-setting'},
])

//获取分量设置
onMounted(async () => {
  loading.value = true
  try {
    const res = await getQuantityConfig()

    if (res.code === 200 && res.data) {
      const data = res.data
      console.log('data',data)
      // 只有当 localStorage 中没有值时才使用接口数据
      radio1.value = localStorage.getItem('onOff') ?? data.onOff ?? '0'
      switchValue.value = radio1.value === '0'

      // 同步更新 localStorage
      localStorage.setItem('onOff', radio1.value)

      // 表单赋值
      form.value = {
        initialAccumulated: data.initialThreshold || '',
        standardComponent: data.dailyThresholdReward || '',
        dailyVoucher: data.dailyThreshold || '',

        componentStandard: data.recommendMerchantsNum  || '',
        merchantCount: data.recommendMerchantsReward || '',

        monthlyTechDrainage: data.monthlyTechThreshold || '',
        merchantUnit: data.monthlyTechRewardGold || '',

        upperLimit: data.monthlyNoTechThreshold || '',
        accumulatedIncrease: data.monthlyNoTechPenaltyGold || '',

        newIncrease: data.dailyExtraQuantityUnit || '',
        componentUpperLimit: data.dailyExtraRewardGold || '',
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
})

const formRef = ref(null)

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    saveLoading.value = true
    const payload = {
      id: 1, // 如果后端需要 id 更新，否则可以不传
      onOff: radio1.value,
      initialThreshold: form.value.initialAccumulated,
      dailyThresholdReward: form.value.standardComponent,
      dailyThreshold: form.value.dailyVoucher,
      recommendMerchantsNum:form.value.componentStandard,
      recommendMerchantsReward: form.value.merchantCount,
      monthlyTechThreshold: form.value.monthlyTechDrainage,
      monthlyTechRewardGold: form.value.merchantUnit,
      monthlyNoTechThreshold: form.value.upperLimit,
      monthlyNoTechPenaltyGold: form.value.accumulatedIncrease,
      dailyExtraQuantityUnit: form.value.newIncrease,
      dailyExtraRewardGold: form.value.componentUpperLimit
    }

    const res = await saveOrUpdateQuantityConfig(payload)

    if (res.code === 200) {
      ElMessage({
        message: '保存成功',
        type: 'success',
        duration: 2000
      })
      localStorage.setItem('onOff', radio1.value) // 同步本地存储
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage.error('表单验证失败或网络错误，请检查输入并重试')
  } finally {
    saveLoading.value = false
  }
}
</script>

<template>
  <div class="heft-setting-container">
    <el-card v-loading="loading" element-loading-text="加载中..." class="page-card">
      <template #header>
        <div class="page-header">
          <div class="header-title">
            <el-icon class="header-icon"><Setting /></el-icon>
            <h2>分量设置</h2>
          </div>
          <div class="header-actions">
            <span class="switch-label">功能状态：</span>
            <el-switch
              v-model="switchValue"
              :active-icon="Check"
              :inactive-icon="Close"
              @change="handleSwitchChange"
              active-text="开启"
              inactive-text="关闭"
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949;"
            />
          </div>
        </div>
      </template>

      <el-form 
        ref="formRef"
        :model="form" 
        :rules="rules"
        label-position="top"
        class="heft-form"
      >
        <!-- 基础设置区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><TrendCharts /></el-icon>
            <span>基础分量设置</span>
          </div>
          
          <div class="section-content">
            <div class="form-card">
              <div class="form-layout">
                <div class="form-column">
                  <div class="form-item-container">
                    <div class="form-label">初始累计获得</div>
                    <el-input-number 
                      v-model="form.initialAccumulated" 
                      :precision="2"
                      :step="0.1"
                      :min="0"
                      controls-position="right"
                      placeholder="请输入数值"
                      class="custom-input"
                    />
                  </div>
                  
                  <div class="form-item-container">
                    <div class="form-label">每天新获得</div>
                    <el-input-number 
                      v-model="form.dailyVoucher"
                      :precision="2"
                      :step="0.1"
                      :min="0"
                      controls-position="right"
                      placeholder="请输入数值"
                      class="custom-input"
                    />
                  </div>
                </div>
                
                <div class="form-column right-column">
                  <div class="form-item-container center-item">
                    <div class="form-label">分量达标</div>
                    <el-input-number 
                      v-model="form.standardComponent"
                      :precision="2"
                      :step="0.1"
                      :min="0"
                      controls-position="right"
                      placeholder="请输入数值"
                      class="custom-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 商家推荐设置区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Edit /></el-icon>
            <span>商家推荐设置</span>
          </div>
          
          <div class="section-content">
            <div class="form-card">
              <!-- 推荐商家行 -->
              <div class="form-flex-row">
                <div class="form-flex-label">每推荐</div>
                <el-input-number 
                  v-model="form.componentStandard"
                  :precision="0"
                  :min="0"
                  :step="1"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">商家、提升上限额度</div>
                <el-input-number 
                  v-model="form.merchantCount"
                  :precision="2"
                  :step="0.1"
                  :min="0"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">平台补贴金</div>
              </div>
              
              <!-- 技术引流行 -->
              <div class="form-flex-row">
                <div class="form-flex-label">每月有技术引流</div>
                <el-input-number 
                  v-model="form.monthlyTechDrainage"
                  :precision="0"
                  :min="0"
                  :step="1"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">商家、提升上限额度</div>
                <el-input-number 
                  v-model="form.merchantUnit"
                  :precision="2"
                  :step="0.1"
                  :min="0"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">平台补贴金</div>
              </div>
              
              <!-- 无效技术引流行 -->
              <div class="form-flex-row">
                <div class="form-flex-label">每月无效技术引流</div>
                <el-input-number 
                  v-model="form.upperLimit"
                  :precision="0"
                  :min="0"
                  :step="1"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">商家、下降额度</div>
                <el-input-number 
                  v-model="form.accumulatedIncrease"
                  :precision="2"
                  :step="0.1"
                  :min="0"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">平台补贴金</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 额外设置区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Plus /></el-icon>
            <span>额外设置</span>
          </div>
          
          <div class="section-content">
            <div class="form-card">
              <div class="form-flex-row">
                <div class="form-flex-label">每天新增</div>
                <el-input-number 
                  v-model="form.newIncrease"
                  :precision="0"
                  :min="0"
                  :step="1"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">分量上限</div>
                <el-input-number 
                  v-model="form.componentUpperLimit"
                  :precision="2"
                  :step="0.1"
                  :min="0"
                  controls-position="right"
                  class="custom-small-input"
                />
                <div class="form-flex-label">平台补贴金。不是就没。</div>
              </div>
            </div>
          </div>
        </div>
      
        <!-- 表单底部 -->
        <div class="form-footer">
          <el-button 
            type="primary" 
            :loading="saveLoading"
            @click="handleSave"
            size="large"
            class="save-button"
          >
            <el-icon><Check /></el-icon>
            <span>保存设置</span>
          </el-button>
        </div>
      </el-form>
      
      <div class="form-tips">
        <el-alert
          title="提示：所有设置在保存后于明日生效"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请确保所有输入的数值准确无误，修改后将影响系统分量计算。</p>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.heft-setting-container {
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  background-color: #f7f8fa;
  box-sizing: border-box;
}

.page-card {
  max-width: 980px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  :deep(.el-card__header) {
    padding: 18px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .header-title {
    display: flex;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }
    
    .header-icon {
      margin-right: 10px;
      font-size: 24px;
      color: #1677ff;
    }
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    
    .switch-label {
      margin-right: 12px;
      font-size: 14px;
      color: #595959;
    }
  }
}

.heft-form {
  margin-top: 16px;
}

.form-section {
  margin-bottom: 28px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding-left: 4px;
    
    .section-icon {
      margin-right: 8px;
      font-size: 18px;
      color: #1677ff;
    }
    
    span {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .section-content {
    padding: 4px 0;
  }
}

/* 表单卡片样式 */
.form-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

/* 新的表单布局 */
.form-layout {
  display: flex;
  flex-wrap: nowrap;
  position: relative;
}

.form-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
  min-width: 300px;
  position: relative;
}

.right-column {
  justify-content: center;
  align-items: flex-start;
  padding-left: 20px;
}

.center-item {
  margin-top: 30px;
}

.form-item-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #595959;
}

.custom-input {
  width: 210px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

/* 弹性布局的表单行 */
.form-flex-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16px;
  padding: 8px 0;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:hover {
    background-color: #fafafa;
    border-radius: 4px;
  }
  
  .form-flex-label {
    font-size: 14px;
    color: #595959;
    margin-right: 8px;
    white-space: nowrap;
  }
  
  .custom-small-input {
    width: 120px;
    margin-right: 8px;
  }
}

.form-footer {
  display: flex;
  justify-content: center;
  margin-top: 36px;
  padding-top: 24px;
  border-top: 1px dashed #f0f0f0;
  
  .save-button {
    padding: 0 36px;
    height: 40px;
    background-color: #1677ff;
    border-color: #1677ff;
    transition: all 0.3s;
    
    &:hover {
      background-color: #4096ff;
      border-color: #4096ff;
    }
    
    .el-icon {
      margin-right: 6px;
    }
  }
}

.form-tips {
  margin-top: 24px;
  
  :deep(.el-alert) {
    border-radius: 6px;
  }
}

:deep(.el-input-number) {
  .el-input-number__decrease, 
  .el-input-number__increase {
    background-color: #fafafa;
    border-color: #d9d9d9;
    
    &:hover {
      color: #1677ff;
    }
  }
  
  &.is-controls-right {
    .el-input__wrapper {
      padding-right: 28px;
    }
  }
  
  .el-input__wrapper {
    background-color: #fff;
    box-shadow: 0 0 0 1px #d9d9d9 inset;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 0 0 1px #1677ff40 inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px #1677ff inset;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .form-layout {
    flex-direction: column;
    gap: 20px;
  }

  .right-column {
    padding-top: 0;
  }
  
  .form-flex-row {
    flex-direction: column;
    align-items: flex-start;
    
    .form-flex-label {
      margin: 8px 0;
    }
    
    .custom-small-input {
      width: 100%;
      max-width: 180px;
      margin-bottom: 8px;
    }
  }
}
</style>