
<template>
  <HomeBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="right-content">
        <!-- 固定标题 - 参考技术引流样式 -->
        <span class="fixed-title">平台协议</span>        
        <div class="main-content">
              <el-row :gutter="24">
                <el-col :span="10">
                    <div class="grid-content">
                      <el-form :model="form">
                          <el-form-item>
                            <el-input 
                              v-model="form.name" 
                              placeholder="自定义协议名称"
                              class="protocol-input"
                              style="width: 200px"
                            />
                            <el-button type="primary" size="small" class="add-btn">添加</el-button>
                          </el-form-item>
                      </el-form>
                    </div>
                </el-col>
                <el-col :span="14">
                  <div class="grid-content-list">
                    <el-row :gutter="40">
                      <el-col :span="10" class="big-col" v-for="(item,index) in dataList" :key="index" >
                          <div class="grid-content">
                            <el-button type="warning" class="ellipsis" @click="handleNoticeClick(item)">
                              <span>{{item.name}}</span>
                            </el-button>
                            <!-- <span @click="removeDomain(item)" class="delete">删除</span> -->
                            <el-icon class="delete"><Delete /></el-icon>
                          </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-col>
              </el-row>
          <!-- <div class="divider"></div> -->
          <div class="upload-container">          
            <div class="text-content" @copy.prevent>
              <el-input
                v-model="form.protocol"
                type="textarea"
                :rows="12"
                placeholder="请输入协议内容"
                class="protocol-textarea"
                :readonly="false"
              />
            </div>
          </div>
          <div>
            <el-button type="primary">保存</el-button>
          </div>
        </div>
      </div>
    </div>
  </HomeBg>
</template>
<script lang="ts" setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref, reactive} from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const dynamicValidateForm = reactive<{
  domains: DomainItem[]
}>({
  domains: [
    {
      key: 1,
      value: '',
    }
  ]
})

interface DomainItem {
  key: number
}
const form = reactive({
  name: '',
  protocol: ''
})
const value = ref([])
const options = [
  {
    value: 'register',
    label: '注册'
  },
  {
    value: 'merchant',
    label: '商家'
  },
  {
    value: 'payment',
    label: '支付'
  }
]

const handleButtonClick = (item) => {
  if(item === '平台协议') router.push('/platformAgreement')
  if(item === '技术引流') router.push('/technicalDrainage')
  if(item === '权限设置') router.push('/permissionSetting')
  if(item === '关系链设置') router.push('/relationshipChainSetting')
  if(item === '分量设置') router.push('/heftSetting')
  if(item === '发票设置') router.push('/')
}

const buttonList = ['平台协议', '技术引流', '权限设置', '关系链设置', '分量设置','发票设置']
const dataList = [
  {
    name:"协议1协议1协议1协议1"
  },
  {
    name:"协议2协议2协议2协议2"
  },
  {
    name:"协议通知3协议通知3协议通知3协议通知3"
  },
  {
    name:"协议通知4协议通知4协议通知4协议通知4"
  },
  {
    name:"协议通知5协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知6协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知7协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知8协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知8协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知8协议通知5协议通知5协议通知5"
  },
    {
    name:"协议通知6协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知7协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知8协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知8协议通知5协议通知5协议通知5"
  },
  {
    name:"协议通知8协议通知5协议通知5协议通知5"
  },
  
]
const handleNoticeClick = (item) => {
    form.protocol = item.name
}
const removeDomain = (item: DomainItem) => {
  console.log(item,'item')
  const index = dataList.domains.indexOf(item)
      console.log(index,'index');

  // if (index !== -1) {
  //   dataList.domains.splice(index, 1)
  // }
}
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  position: relative;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  flex-shrink: 0;
  position: relative;
  z-index: 1;

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.right-content {
  flex: 1;
  padding: 20px 20px 20px 0px;
  position: relative;
}

/* 固定标题样式 - 参考技术引流 */
.fixed-title {
  position: absolute;
  margin-left: 20px;
  top: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  z-index: 2;
}

.main-content {
  margin-left: 225px;
  width: 100%;
  height: 600px;
  margin-top: 60px; /* 给固定标题留出空间 */
  max-width: 800px;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  
}

.cascader-selector {
  width: 200px;
  margin-bottom: 20px;
}
.protocol{
  width: 100%;
  background-color: yellow;
}

.protocol-form {
  width: 50%;
  margin-bottom: 20px;
  border: 1px solid red;
}
.dataList{
  width: 50%;
  background-color: yellowgreen;
}
.protocol-input {
  width: 100%;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 20px 0;
}

.upload-container {
  width: 100%;
  height: calc(100% - 120px);
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  
  span {
    font-size: 16px;
    color: #606266;
  }
}

.add-btn {
  background-color: #3A58CF;
  border: none;
  margin-left: 10px;
  &:hover {
    background-color: #2a48bf;
  }
}

.upload-btn {
  background-color: #3A58CF;
  border: none;
  
  &:hover {
    background-color: #2a48bf;
  }
}

.text-content {
  margin-top: 20px;
  height: calc(100% - 60px);
  
  :deep(.el-textarea__inner) {
    user-select: none;
    resize: none;
    height: 100%;
    min-height: 300px;
  }
}

.protocol-textarea {
  width: 100%;
  height: 100%;
}

.ellipsis span{
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100px;
  display: inline-block; 
}
.grid-content-list{
  border: 1px solid yellowgreen;
  overflow: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
  .grid-content{
    width: 220px;
    // border: 1px solid red;
    margin-bottom: 10px;
  }
}
.big-col{
  margin-right: 10px;
}
.delete{
  vertical-align: middle;
  margin-left: 10px;
  cursor: pointer;
}
</style>