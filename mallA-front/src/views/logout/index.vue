<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '系统通知') router.push('/customerServer')
  if(item === '申请通知') router.push('/')
  if(item === '商品通知') router.push('/productNote')
  if(item === '订单通知') router.push('/ordersNote')
  if(item === '结算通知') router.push('/settlementNote')
  if(item === '退款通知') router.push('/refundNote')
  if(item === '商家入驻通知') router.push('/businessSettledNote')
  if(item === '实名申请') router.push('/realNameReview')
  if(item === '账号注销') router.push('/logout')
}

const buttonList = [
  '全部消息', '系统通知', '申请通知', '商品通知','订单通知',
  '结算通知','退款通知','商家入驻通知','实名申请','账号注销'
]

// 表单数据
const form = ref({
  password: '',
  verificationCode: ''
})

// 发送验证码
const sendCode = () => {
  console.log('发送验证码')
  // 实际项目中这里调用发送验证码API
}

// 提交注销
const submitDelete = () => {
  console.log('提交注销', form.value)
  // 实际项目中这里调用注销账号API
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="rightBox">
        <div class="delete-account-container">
          <h2 class="delete-title">账号注销</h2>
          
          <!-- 警告提示 -->
          <div class="warning-box">
            <h4><i class="el-icon-warning"></i> 重要提示</h4>
            <ul>
              <li>账号注销后将无法恢复</li>
              <li>所有个人数据和历史记录将被永久删除</li>
              <li>未使用的权益将自动失效</li>
            </ul>
          </div>
          
          <!-- 注销表单 -->
          <el-form :model="form" class="delete-form">
            <el-form-item label="登录密码" prop="password">
              <el-input 
                v-model="form.password" 
                type="password" 
                placeholder="请输入当前账号密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="验证码" prop="verificationCode">
              <div class="code-input">
                <el-input 
                  v-model="form.verificationCode" 
                  placeholder="请输入短信验证码"
                />
                <el-button 
                  type="primary" 
                  plain 
                  @click="sendCode"
                  class="send-code-btn"
                >
                  获取验证码
                </el-button>
              </div>
            </el-form-item>
            
            <div class="action-buttons">
              <el-button 
                type="danger" 
                class="confirm-delete"
                @click="submitDelete"
              >
                确认注销
              </el-button>
              <el-button 
                class="cancel-btn"
                @click="router.push('/')"
              >
                取消
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-account-container {
  width: 100%;
  max-width: 600px;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.delete-title {
  text-align: center;
  color: #3A58CF;
  margin-bottom: 30px;
  font-size: 24px;
}

.warning-box {
  background-color: #fff6f6;
  border-left: 4px solid #f56c6c;
  padding: 15px;
  margin-bottom: 30px;
  
  h4 {
    color: #f56c6c;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
    }
  }
  
  ul {
    padding-left: 20px;
    margin: 0;
    color: #666;
    
    li {
      margin-bottom: 5px;
    }
  }
}

.delete-form {
  :deep(.el-form-item__label) {
    font-weight: bold;
    color: #333;
  }
  
  .el-input {
    width: 100%;
  }
}

.code-input {
  display: flex;
  gap: 10px;
  
  .send-code-btn {
    width: 120px;
    flex-shrink: 0;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  
  .confirm-delete {
    width: 200px;
    height: 45px;
    font-size: 16px;
  }
  
  .cancel-btn {
    width: 200px;
    height: 45px;
    font-size: 16px;
    border: 1px solid #dcdfe6;
  }
}
</style>