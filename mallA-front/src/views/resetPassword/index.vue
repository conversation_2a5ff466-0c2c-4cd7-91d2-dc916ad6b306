<script setup>
import Background from '../../components/Background.vue'
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const newPassword = ref('')
const confirmPassword = ref('')
const isMobile = ref(false)
const passwordError = ref('')

// 密码正则：必须包含数字、字母、特殊符号，8-20位
const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*_\-]).{8,20}$/

onMounted(() => {
  checkDevice()
  window.addEventListener('resize', checkDevice)
})

const checkDevice = () => {
  isMobile.value = window.innerWidth <= 768
}

const validatePassword = () => {
  if (!passwordRegex.test(newPassword.value)) {
    passwordError.value = '密码必须包含数字、字母和特殊符号(!@#$%^&*_-)，且为8-20位'
    return false
  }
  passwordError.value = ''
  return true
}

const handleSubmit = () => {
  if (!validatePassword()) {
    ElMessage.error(passwordError.value)
    return
  }
  
  if (newPassword.value !== confirmPassword.value) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  // 密码验证通过，执行修改密码逻辑
  ElMessage.success('密码修改成功')
  // 这里可以添加提交表单的逻辑
}
</script>

<template>
  <Background>
    <div class="reset-password-container" :class="{ 'mobile-view': isMobile }">
      <div class="form-box">
        <h2 class="title">输入新密码</h2>
        
        <el-input
          v-model="newPassword"
          class="password-input"
          type="password"
          placeholder="数字+字母+特殊符号8-20位"
          show-password
          @blur="validatePassword"
        />
        <div v-if="passwordError" class="error-message">{{passwordError}}</div>
        
        <el-input
          v-model="confirmPassword"
          class="password-input"
          type="password"
          placeholder="确认新密码"
          show-password
        />
         <router-link to="/" class="confirm-btn">确认修改</router-link>
        
      </div>
    </div>
  </Background>
</template>

<style lang="scss" scoped>
.reset-password-container {
  position: relative;
  z-index: 10;
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5vh 0;
  box-sizing: border-box;

  &.mobile-view {
    padding: 10vh 0;
    
    .form-box {
      width: 90vw;
      height: auto;
      min-height: 70vh;
      padding: 8vh 5vw;
    }
    
    .password-input,
    .confirm-btn {
      width: 100% !important;
      max-width: 100%;
    }
  }
}

.form-box {
  width: 642px;
  min-height: 824px;
  background-color: #5170ED;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 164px 40px 40px;
  box-sizing: border-box;
  transition: all 0.3s ease;

  @media (max-height: 900px) {
    padding-top: 10vh;
  }
}

.title {
  color: white;
  font-size: clamp(24px, 2vw, 28px);
  margin-bottom: clamp(40px, 8vh, 80px);
  text-align: center;
}

.password-input {
  width: 465px;
  height: 70px;
  margin-bottom: clamp(20px, 4vh, 40px);
  
  :deep(.el-input__wrapper) {
    height: 100%;
    font-size: clamp(16px, 1.8vw, 18px);
    border-radius: 4px;
    background: white;
    
    &:hover, &.is-focus {
      box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
    }
  }
  
  :deep(.el-input__inner) {
    height: 100%;
    font-size: inherit;
  }
  
  @media (max-width: 768px) {
    height: 60px;
  }
}
.error-message {
  width: 465px;
  color: #ff4d4f;
  font-size: 14px;
  min-height: 20px;
  margin-top: -8px; /* 仅微调错误提示位置，不影响输入框间距 */
  margin-bottom: 8px;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}
.confirm-btn {
  width: 465px;
  height: 70px;
  background: #14097A;
  color: white;
  font-size: clamp(18px, 2vw, 20px);
  border-radius: 4px;
  margin-top: clamp(20px, 4vh, 40px);
  border: none;
  transition: all 0.2s;
  display: flex;                  /* 新增 */
  justify-content: center;        /* 新增 - 水平居中 */
  align-items: center;            /* 新增 - 垂直居中 */
  text-decoration: none;          /* 新增 - 移除链接下划线 */
  
  &:hover {
    background: #0e075e;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  @media (max-width: 768px) {
    height: 60px;
    width: 100%;                  /* 移动端宽度调整 */
  }
}
</style>