查询<script setup>
import HomeBg  from '../../components/HomeBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()

// 表格数据
const tableData = ref([
  {
    account: 'user001',
    fans: '87,075',
    idCard: '310*****1990****',
    phone: '138****1234',
    company: '广州中业科技有限公司',
    registerTime: '2022-01-15',
    coupon: '1',
    commission: '55%',
    ip: '************'
  },
  {
    account: 'user002',
    fans: '10,000',
    idCard: '310*****1990****',
    phone: '138****1234',
    company: '广州中南网络科技有限公司',
    registerTime: '2026-01-15',
    coupon: '8',
    commission: '15%',
    ip: '************'
  },
  {
    account: 'user003',
    fans: '999,888,000',
    idCard: '310*****1990****',
    phone: '138****1234',
    company: '腾讯企鹅科技有限公司',
    registerTime: '2001-01-15',
    coupon: '115',
    commission: '25%',
    ip: '***********'
  },
  {
    account: 'user004',
    fans: '85,100,000',
    idCard: '310*****1990****',
    phone: '168****1277',
    company: '字节抖动股份责任制公司',
    registerTime: '2025-01-15',
    coupon: '89',
    commission: '28%',
    ip: '*************'
  },
 
])

const searchForm = ref({
  creditPhone: '',
  name: '',
  level: '',
  startTime: '',
  endTime: ''
})

const showIp = ref(null)

const toggleIp = (index) => {
  showIp.value = showIp.value === index ? null : index
}
</script>

<template>
  <HomeBg>
    <div class="container">
    
      <div class="rightBox">
        <div class="head">
          <span class="search">查询</span>
        </div>
        <div class="main">
          <div class="search-box">
            <div class="search-row">
              <el-input 
                v-model="searchForm.creditPhone"
                class="search-input"
                placeholder="企业信用号/手机号"
              />
              <el-input 
                v-model="searchForm.name"
                class="search-input"
                placeholder="姓名"
              />
              <el-button-group class="level-buttons">
                <el-button 
                  :type="searchForm.level === '1' ? 'primary' : ''"
                  @click="searchForm.level = '1'"
                >
                  一级
                </el-button>
                <el-button 
                  :type="searchForm.level === '2' ? 'primary' : ''"
                  @click="searchForm.level = '2'"
                >
                  二级
                </el-button>
                <el-button 
                  :type="searchForm.level === '3' ? 'primary' : ''"
                  @click="searchForm.level = '3'"
                >
                  三级
                </el-button>
              </el-button-group>
            </div>
            <div class="search-row">
              <el-date-picker
                v-model="searchForm.startTime"
                type="date"
                placeholder="注册时间"
                class="date-picker"
              />
              <span class="to-text">至</span>
              <el-date-picker
                v-model="searchForm.endTime"
                type="date"
                placeholder="结束时间"
                class="date-picker"
              />
              <el-button type="primary" class="search-btn">搜索</el-button>
            </div>
          </div>
          
          <div class="table-info">
            <span>列表、总数 ({{ tableData.length }})</span>
          </div>
          
          <el-table :data="tableData" class="data-table">
            <el-table-column prop="account" label="账号" width="120" />
           <el-table-column prop="company" label="姓名" width="250" />
            <el-table-column prop="idCard" label="身份证" width="300" />
            <el-table-column prop="phone" label="手机号" width="220" />
            <el-table-column prop="registerTime" label="注册时间" width="150" />
            <el-table-column prop="coupon" label="平台补贴券" width="180" />
            <el-table-column label="操作" width="120">
              <template #default="{ row, $index }">
                <el-button 
                  size="small" 
                  @click="toggleIp($index)"
                >
                  {{ showIp === $index ? '隐藏IP' : '查看IP' }}
                </el-button>
                <div v-if="showIp === $index" class="ip-info">
                  最近登录IP: {{ row.ip }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </HomeBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}



.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.head {
  margin-bottom: 20px;
  
  .search {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-box {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.search-input {
  width: 200px;
  margin-right: 15px;
}

.level-buttons {
  margin-right: 15px;
}

.date-picker {
  width: 180px;
}

.to-text {
  margin: 0 10px;
  color: #606266;
}

.search-btn {
  margin-left: 15px;
}

.table-info {
  margin-bottom: 15px;
  font-size: 14px;
  color: #909399;
}

.data-table {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.ip-info {
  margin-top: 5px;
  padding: 5px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}
</style>