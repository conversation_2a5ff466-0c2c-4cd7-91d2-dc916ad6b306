<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref } from 'vue'

const isActive = ref(false) // 控制单选框状态
</script>

<template>
    <!-- <HomeBg> -->
        <div class="container">
            <img class="bg" src="../../images/bigBackground.png" alt="背景图">
            
            <div class="control-panel">
                <!-- 返回上一个版本按钮 -->
                <div class="return-version">
                    返回上一个版本
                </div>
                
                <!-- 启动控制区域 -->
                <div class="activation-box">
                    <span class="activation-text">启动</span>
                    <el-checkbox v-model="isActive" class="activation-checkbox" />
                </div>
            </div>
            
            <!-- 更新按钮保持原样 -->
            <div class="updateBox">
                <el-button class="update">更新</el-button>
            </div>
        </div>
    <!-- </HomeBg> -->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    
    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    /* 控制面板容器 */
    .control-panel {
        position: absolute;
        top: 50px;
        right: 50px;
        display: flex;
        align-items: center;
        gap: 20px;
        z-index: 4;
    }

    /* 返回版本按钮 */
    .return-version {
        color: #3A5BDE;
        font-size: 40px;
        cursor: pointer;
        padding: 10px;
        transition: opacity 0.3s;
        
        &:hover {
            opacity: 0.8;
        }
    }

    /* 启动控制区域 */
    .activation-box {
        width: 168px;
        height: 162px;
        background-color: #FFFFFF;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        padding: 0 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        
        .activation-text {
            font-size: 24px;
            color: #333;
        }
        
        /* 复选框样式 */
        :deep(.el-checkbox.activation-checkbox) {
            .el-checkbox__inner {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                border: 2px solid #3A5BDE;
                
                &::after {
                    border-color: #3A5BDE;
                }
            }
            
            .el-checkbox__label {
                display: none;
            }
        }
    }

    /* 更新按钮保持原样 */
    .updateBox {
        background-color: #fff;
        z-index: 4;
        width: 400px;
        height: 400px;
        margin-left: 318px;
        margin-top: 286px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 15px;
        
        .update {
            width: 200px;
            height: 200px;
            background-color: #3A5BDE;
            font-size: 30px;
            color: #000;
            border-radius: 15px;
        }
    }
}
</style>