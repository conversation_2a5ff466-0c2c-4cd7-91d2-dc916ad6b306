<template>
  <MerchantBg>
    <div class="container">
      <div class="rightBox">
        <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="rules"
            label-width="120px"
            class="demo-ruleForm"
            :size="formSize"
            status-icon
        >       
                <div class="title">法人信息</div>
                <el-form-item label="公司名" prop="name">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="法人名字" prop="region">
                    <el-select v-model="ruleForm.region" placeholder="Activity zone">
                        <el-option label="Zone one" value="shanghai" />
                        <el-option label="Zone two" value="beijing" />
                    </el-select>
                </el-form-item>
                <el-form-item label="手机号" prop="count">
                    <el-select
                        v-model="ruleForm.count"
                        placeholder="Activity count"
                        :options="options"
                    />
                </el-form-item>
                <div class="title">商家信息</div>
                <el-form-item label="店铺名称" prop="name">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="LOGO" prop="region">
                    <el-select v-model="ruleForm.region" placeholder="Activity zone">
                        <el-option label="Zone one" value="shanghai" />
                        <el-option label="Zone two" value="beijing" />
                    </el-select>
                </el-form-item>
                <el-form-item label="地址" prop="count">
                    <el-select
                        v-model="ruleForm.count"
                        placeholder="Activity count"
                        :options="options"
                    />
                </el-form-item>
                <el-form-item label="资质证件" prop="count">
                    <el-select
                        v-model="ruleForm.count"
                        placeholder="Activity count"
                        :options="options"
                    />
                </el-form-item>
                <el-form-item label="行业" prop="resource">
                    <el-checkbox-group v-model="ruleForm.type">
                        <el-checkbox label="Online activities" name="type" />
                        <el-checkbox label="Promotion activities" name="type" />
                        <el-checkbox label="Offline activities" name="type" />
                        <el-checkbox label="Simple brand exposure" name="type" />
                    </el-checkbox-group>
                </el-form-item>
                <div class="title">收款信息</div>
                <el-form-item label="银行账号" prop="name">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="开户名" prop="region">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="开户行" prop="region">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="支付宝账号" prop="region">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="支付宝名字" prop="region">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="微信账号" prop="region">
                    <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm(ruleFormRef)">
                        提交
                    </el-button>
                    <el-button @click="resetForm(ruleFormRef)">Reset</el-button>
                </el-form-item>
             </el-form>
      </div>
    </div>
  </MerchantBg>
</template>
<script setup lang="ts">
import MerchantBg from '../../components/MerchantBg.vue'
import { useRouter } from 'vue-router'
import { ref ,reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
const router = useRouter()
interface RuleForm {
  name: string
  region: string
  count: string
  date1: string
  date2: string
  delivery: boolean
  type: string[]
  resource: string
  desc: string
}
const ruleForm = reactive<RuleForm>({
  name: 'Hello',
  region: '',
  count: '',
  date1: '',
  date2: '',
  delivery: false,
  type: [],
  resource: '',
  desc: '',
})
const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: 'Please input Activity name', trigger: 'blur' },
    { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
  ],
  region: [
    {
      required: true,
      message: 'Please select Activity zone',
      trigger: 'change',
    },
  ],
  count: [
    {
      required: true,
      message: 'Please select Activity count',
      trigger: 'change',
    },
  ],
  date1: [
    {
      type: 'date',
      required: true,
      message: 'Please pick a date',
      trigger: 'change',
    },
  ],
  date2: [
    {
      type: 'date',
      required: true,
      message: 'Please pick a time',
      trigger: 'change',
    },
  ],
  type: [
    {
      type: 'array',
      required: true,
      message: 'Please select at least one activity type',
      trigger: 'change',
    },
  ],
  resource: [
    {
      required: true,
      message: 'Please select activity resource',
      trigger: 'change',
    },
  ],
  desc: [
    { required: true, message: 'Please input activity form', trigger: 'blur' },
  ],
})

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}
.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}
.title{
    font-size: 20px;
    color: #000;
    height: 50px;
    line-height: 50px;
    margin-bottom: 10px;
    border-bottom: 1px solid #000;
}
</style>