<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import request from '../../utils/request';

// 搜索表单
const searchForm = reactive({
  startTime: "",
  endTime: "",
});

// 汇总数据
const summaryData = ref({
  totalDailyData: "0",
  totalRemainder: "0",
  totalRemainderCount: "0",
  totalDailymeasure: "0"
});

// 加载状态
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 5,
  total: 0
});

// API调用函数
const querySystemInfo = async (params: any) => {
  try {
    const response = await request({
      url: '/mall-project/api/querySystemInfo',
      method: 'post',
      data: params
    });
    return response;
  } catch (error) {
    console.error('查询系统信息失败:', error);
    throw error;
  }
};



// 获取数据
const fetchData = async () => {
  if (
    searchForm.startTime &&
    searchForm.endTime &&
    searchForm.startTime > searchForm.endTime
  ) {
    ElMessage.warning("开始日期不能晚于结束日期");
    return;
  }

  loading.value = true;
  try {
    const params = {
      startDate: searchForm.startTime,
      endDate: searchForm.endTime,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    const response = await querySystemInfo(params);

    // response.data直接就是业务数据
    const data = response.data;
    const list = data.list || [];
    const total = data.total || 0;

    tableData.value = list;
    pagination.total = total;

    // 同时更新汇总数据，因为新接口同时返回列表和汇总数据
    if (data && data.summary) {
      const summary = data.summary;
      summaryData.value = {
        totalDailyData: summary.dailyDataAccrueTotal || "0",        // 每日数据累计量
        totalRemainder: summary.dailyRemainderTotal || "0",         // 系统每日余数
        totalDailymeasure: summary.dailyQuantityTotal || "0",       // 每日系统量化数总累计
        totalRemainderCount: summary.dailyMeterageTotal || "0"      // 累计计量数
      };
    }
  } catch (error: any) {
    ElMessage.error(error.message || '查询失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = async () => {
  pagination.pageNum = 1;
  await fetchData();
};

// 重置搜索条件
const resetSearch = async () => {
  Object.assign(searchForm, {
    startTime: "",
    endTime: "",
  });
  pagination.pageNum = 1;
  await fetchData();
};

// 分页大小变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;
  fetchData();
};

// 当前页码变化
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  fetchData();
};

// 初始化
onMounted(async () => {
  await fetchData();
});
</script>

<template>
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>系统查询</h1>
      </div>

      <!-- 统计信息 -->
      <el-card class="summary-card" shadow="hover" style="margin-bottom: 20px;">
        <div class="summary-section">
          <div class="summary-item">
            <span class="summary-label">每日数据累计量:</span>
            <el-input v-model="summaryData.totalDailyData" placeholder="0" style="width: 200px;" disabled />
          </div>
          <div class="summary-item">
            <span class="summary-label">系统每日余数:</span>
            <el-input v-model="summaryData.totalRemainder" placeholder="0" style="width: 200px;" disabled />
          </div>
          <div class="summary-item">
            <span class="summary-label">每日系统量化数总累计:</span>
            <el-input v-model="summaryData.totalDailymeasure" placeholder="0" style="width: 170px;" disabled />
          </div>
          <div class="summary-item">
            <span class="summary-label">累计计量数:</span>
            <el-input v-model="summaryData.totalRemainderCount" placeholder="0" style="width: 200px;" disabled />
          </div>
        </div>
      </el-card>

      <!-- 数据查询与列表 -->
      <el-card class="table-card" shadow="hover">

        <!-- 搜索表单 -->
        <div class="search-form" style="margin-left: 30px;">
          <div class="form-row" style="display: flex;">
            <div class="form-item">
              <span class="form-label">查询日期</span>
              <el-date-picker
                v-model="searchForm.startTime"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>
            <div class="form-actions" style="margin-left: 10px;">
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </div>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table :data="tableData" v-loading="loading" stripe border style="width: 100%">
          <el-table-column prop="updateDate" label="日期" />
          <el-table-column prop="dailyDataAccrue" label="每日数据累计量" />
          <el-table-column prop="dailyRemainder" label="每日余数" />
          <el-table-column prop="dailyQuantityTotal" label="每日量化数总累计" />
          <el-table-column prop="dailyMeterage" label="每日计量数" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  margin: 0;
}

.content-wrapper {
  width: 100%;
}

.page-header {
  width: 100%;
  height: 61px;
  border-bottom: 1px solid #3a58cf;

  h1 {
    line-height: 61px;
    margin-left: 20px;
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border: none;
  background: white;
  box-shadow: none;
  border-radius: 0;

  .el-card__header {
    display: none;
  }

  .el-card__body {
    padding: 0;
  }
}

.search-form {
  height: 100px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  border-bottom: 3px solid #2a48bf;
}

.search-item {
  margin-left: 30px;
  margin-top: 15px;
  display: flex;
  align-items: center;
}

.form-label {
  margin-right: 5px;
  font-weight: bold;
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}

:deep(.el-date-picker) {
  .el-input__wrapper {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}

:deep(.el-button) {
  padding: 8px 16px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;

  &:hover {
    background-color: #66b1ff;
  }
}

.summary-section {
  height: auto;
  min-height: 100px;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  justify-content: flex-start;
  align-items: center;
  padding: 20px 30px;
}

.summary-item {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;

  .summary-label {
    color: #595959;
    white-space: nowrap;
    min-width: 120px;
    font-size: 14px;
  }

  :deep(.el-input) {
    width: 150px;
    flex-shrink: 0;
  }
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.data-table th,
.data-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.data-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.data-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.data-table tr:hover {
  background-color: #f0f0f0;
}

:deep(.el-table) {
  font-size: 14px;

  .el-table__cell {
    padding: 12px 0;
  }
}

.pagination-container {
  right: 20px;
  bottom: 20px;
  background: white;
  padding: 10px;
  border-radius: 4px;
  z-index: 10;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}



:deep(.el-table) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: none;

    th.el-table__cell {
      background: linear-gradient(135deg, #f8faff, #f0f9ff);
      color: #262626;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 2px solid rgba(24, 144, 255, 0.1);
      padding: 8px 12px;
      height: 40px;
    }

    td.el-table__cell {
      border-bottom: 1px solid rgba(0, 0, 0, 0.04);
      transition: all 0.2s ease;
      padding: 8px 12px;
      height: 45px;
    }

    .el-table__row {
      &:hover {
        background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
      }
    }

    .cell {
      padding: 8px 12px;
      font-size: 14px;
      color: #262626;
    }
}

.pagination-container {
  margin-top: 32px;
  padding: 24px;
  display: flex;
  justify-content: center;
  background: linear-gradient(135deg, #f8faff, #f0f9ff);
  border-radius: 12px;

  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: #595959;
      font-weight: 500;
    }

    .el-pager {
      li {
        border-radius: 8px;
        margin: 0 4px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        &.is-active {
          background: linear-gradient(135deg, #1890ff, #40a9ff);
          color: white;
          border: none;
          transform: scale(1.1);
        }

        &:hover:not(.is-active) {
          background: rgba(24, 144, 255, 0.1);
          color: #1890ff;
          border-color: rgba(24, 144, 255, 0.3);
        }
      }
    }

    .btn-prev,
    .btn-next {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      border: 2px solid #d9d9d9;

      &:hover {
        background: rgba(24, 144, 255, 0.1);
        color: #1890ff;
        border-color: #40a9ff;
        transform: translateY(-1px);
      }
    }

    .el-select {
      .el-input__wrapper {
        border-radius: 8px;
        border: 2px solid #d9d9d9;
        transition: all 0.3s ease;

        &:hover {
          border-color: #40a9ff;
        }

        &.is-focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
        }
      }
    }
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 2px solid #e8e8e8;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);

    &:hover {
      border-color: #40a9ff;
      background: rgba(255, 255, 255, 1);
    }

    &.is-focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
      background: rgba(255, 255, 255, 1);
    }
  }

  .el-input__prefix {
    color: #a0a0a0;
  }
}

:deep(.el-date-editor) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 2px solid #e8e8e8;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);

    &:hover {
      border-color: #40a9ff;
      background: rgba(255, 255, 255, 1);
    }

    &.is-focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
      background: rgba(255, 255, 255, 1);
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .content-wrapper {
    padding: 0 16px;
  }

  .summary-section {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .search-form {
    .form-row {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .form-item {
        flex-direction: column;
        align-items: flex-start;

        .form-label {
          margin-bottom: 8px;
          margin-right: 0;
        }

        .el-input, .el-date-picker {
          width: 100%;
        }
      }

      .form-actions {
        margin-left: 0;
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }

  .summary-section {
    grid-template-columns: 1fr;
  }

  .pagination-container {
    :deep(.el-pagination) {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }
    }
  }
}
</style>