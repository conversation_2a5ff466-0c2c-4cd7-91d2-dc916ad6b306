<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'

const router = useRouter()
const showTable = ref(false)
const tableData = ref([
  {
    product: '商品1',
    reviewer: '用户A',
    time: '2023-08-01',
    visible: '显示'
  },
  {
    product: '商品2',
    reviewer: '用户B',
    time: '2023-08-02',
    visible: '显示'
  }
])

const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
  if(item === '商品代销申请') router.push('./productSellApply')
}

const handleDelete = (index) => {
  tableData.value.splice(index, 1)
}

const showComment = () => {
  showTable.value = true
}

const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址',  '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]
</script>

<template>
    <ManageBg>
        <div class="container">
             <div class="left-buttons">
                <el-button 
                    v-for="(item, index) in buttonList" 
                    :key="index"
                    class="data-button"
                    @click="handleButtonClick(item)"
                >
                    {{ item }}
                </el-button>
             </div>
             <div class="content-area">
                <div class="headerBox">
                    <div class="header">
                        <span class="revList">评价列表</span>
                    </div>
                    <div class="main">
                        <div class="title">商品评价列表</div>
                        <div class="search-area">
                            <div class="search-row">
                                <div class="search-item">
                                    <span class="label">商品名称</span>
                                    <input class="input" type="text" placeholder="请输入商品名称">
                                </div>
                                <button class="allComment">全部评价</button>
                            </div>
                            <div class="search-row">
                                <div class="search-item">
                                    <span class="label">开始时间</span>
                                    <input class="date-input" type="date">
                                    <span class="to">至</span>
                                    <span class="label">结束时间</span>
                                    <input class="date-input" type="date">
                                </div>
                            </div>
                            <div class="search-button">
                                <el-button @click="showComment" type="primary" :icon="Search" class="custom-search-btn" />
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bottom" v-show="showTable">
                    <el-table 
                        :data="tableData" 
                        style="width: 100%"
                        :header-cell-style="{ backgroundColor: '#3A58CF', color: 'white' }"
                    >
                        <el-table-column 
                        prop="product" 
                        label="商品信息" 
                        min-width="20%"
                        />
                        <el-table-column 
                        prop="reviewer" 
                        label="评价者" 
                        min-width="20%"
                        />
                        <el-table-column 
                        prop="time" 
                        label="时间" 
                        min-width="20%"
                        />
                        <el-table-column 
                        prop="visible" 
                        label="显示" 
                        min-width="15%"
                        />
                        <el-table-column 
                        label="操作" 
                        min-width="25%"
                        >
                        <template #default="scope">
                            <el-button
                            size="small"
                            type="danger"
                            @click="handleDelete(scope.$index)"
                            >删除</el-button>
                        </template>
                        </el-table-column>
                    </el-table>
                    </div>
             </div>
        </div>
    </ManageBg>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.content-area {
    display: flex;
    flex-direction: column;
    margin-left: 41px;
    width: 1347px;
}

.headerBox {
    margin-top: 40px;
    border: 3px solid #3A58CF;
    border-radius: 8px;
    overflow: hidden;
    
    .header {
        background-color: #3A58CF;
        height: 76px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        .revList {
            font-size: 30px;
            color: white;
            padding: 0 160px;
            cursor: pointer;
        }
    }
    
    .main {
        padding: 30px 40px 20px;
        
        .title {
            font-size: 30px;
            margin-left: 180px;
            color: #000;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .search-area {
            display: flex;
            flex-direction: column;
            gap: 15px;
            
            .search-row {
                display: flex;
                align-items: center;
                gap: 15px;
                
                .search-item {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    
                    .label {
                        font-size: 24px;
                        color: #333;
                        min-width: 80px;
                    }
                    
                    .input, .date-input {
                        width: 480px;
                        height: 48px;
                        border: 1px solid #ccc;
                        border-radius: 4px;
                        padding: 0 15px;
                        font-size: 16px;
                    }
                    
                    .to {
                        margin: 0 10px;
                        font-size: 18px;
                    }
                }
                
                .allComment {
                    width: 160px;
                    height: 48px;
                    margin-left: 264px;
                    background-color: #3A58CF;
                    color: white;
                    font-size: 22px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    
                    &:hover {
                        background-color: #2a48bf;
                    }
                }
            }
            
            .search-button {
                margin-top: 15px;
                margin-left: 1000px;
                
                :deep(.custom-search-btn) {
                    width: 122px;
                    height: 46px;
                    padding: 0 !important;
                    
                    .el-icon {
                        width: 24px;
                        height: 24px;
                        font-size: 24px;
                    }
                    
                    span {
                        display: none;
                    }
                }
            }
        }
    }
}

.bottom {
  width: 1347px;
  margin-top: 20px;
  border: 2px solid #3A58CF;
  border-radius: 8px;
  overflow: hidden;

  :deep(.el-table) {
    font-size: 18px;
    table-layout: fixed; // 启用固定布局

    // 表头样式
    th.el-table__cell {
      background-color: #3A58CF !important;
      color: white;
      font-size: 20px;
      height: 60px;
      text-align: center;
    }

    // 单元格样式
    td.el-table__cell {
      height: 55px;
      text-align: center;
      padding: 12px 0;
    }

    // 列宽自适应
    .el-table__header-wrapper,
    .el-table__body-wrapper {
      width: 100% !important;
    }

    // 操作按钮样式
    .el-button {
      padding: 8px 15px;
      font-size: 16px;
      min-width: 80px;
    }

    // 最后一列右对齐
    .el-table__cell:last-child {
      .cell {
        display: flex;
        justify-content: center;
      }
    }
  }
}
</style>