<template>
  <el-header class="header">
    <div class="header-content">
      <!-- 设置图标 -->
      <div class="header-top">
        <el-icon><Setting /></el-icon>
      </div>

      <!-- 分割线 -->
      <div class="header-line"></div>

      <!-- 用户信息区域 -->
      <div class="user-info">
        <span>欢迎，{{ username }}</span>
        <el-button @click="logout" link type="danger" style="margin-left: 10px;">
          退出登录
        </el-button>
      </div>
    </div>
  </el-header>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Setting } from '@element-plus/icons-vue';

const router = useRouter();
const username = ref('aa');

// 组件挂载时获取用户名
onMounted(() => {
  const storedUsername = localStorage.getItem('username');
  if (storedUsername) {
    username.value = storedUsername;
  } else {
    // 如果没有用户名，说明未登录或登录已过期
    ElMessage.warning('请先登录');
    router.push('/login');
  }
});

// 退出登录逻辑
const logout = () => {
  // 清除本地存储
  localStorage.removeItem('token');
  localStorage.removeItem('username');

  // 提示并跳转登录页
  ElMessage.success('退出成功');
  router.push('/login');
};
</script>

<style scoped>
.header {
  background-color: #ffffff;
  color: #333333;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.header-top {
  margin-right: 10px;
  cursor: pointer;
}

.header-line {
  width: 1px;
  height: 60%;
  background-color: #ccc;
  margin: 0 15px;
}

.user-info {
  display: flex;
  align-items: center;
  font-size: 14px;
}
</style>