<template>
  <HomeBg>
    <div class="page-container">
      <div class="content-wrapper">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>模块管理</h1>
        </div>

        <!-- 模块管理卡片 -->
        <el-card class="table-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>模块管理</span>
              <el-button type="primary" @click="showAddModal">
                <el-icon><Plus /></el-icon>新增模块
              </el-button>
            </div>
          </template>

          <!-- 搜索表单 -->
          <div class="search-form">
            <div class="form-row">
              <div class="form-item">
                <span class="form-label">搜索</span>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索模块名称或代码"
                  clearable
                  @keyup.enter="searchModules"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>
            <div class="form-actions">
              <el-button type="primary" @click="searchModules">
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </div>
          </div>

          <!-- 数据表格 -->
          <el-table :data="tableData" v-loading="loading" stripe border style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="moduleName" label="模块名称" min-width="200">
              <template #default="{ row }">
                <span :style="{ paddingLeft: (row.level - 1) * 20 + 'px' }">
                  {{ row.moduleName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="moduleCode" label="模块代码" width="120" />
            <el-table-column prop="level" label="级别" width="80">
              <template #default="{ row }">
                {{ getLevelText(row.level) }}
              </template>
            </el-table-column>
            <el-table-column prop="moduleUrl" label="URL" min-width="150">
              <template #default="{ row }">
                {{ row.moduleUrl || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="icon" label="图标URL" min-width="150">
              <template #default="{ row }">
                {{ row.icon || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="sortOrder" label="排序" width="80" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="editModule(row.id)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="confirmDelete(row.id)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 模块编辑对话框 -->
        <el-dialog
          v-model="dialogVisible"
          :title="dialogTitle"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form
            ref="moduleFormRef"
            :model="moduleForm"
            :rules="formRules"
            label-width="100px"
          >
            <el-form-item label="模块名称" prop="moduleName">
              <el-input v-model="moduleForm.moduleName" placeholder="请输入模块名称" />
            </el-form-item>

            <el-form-item label="模块代码" prop="moduleCode">
              <el-input v-model="moduleForm.moduleCode" placeholder="模块代码" readonly />
            </el-form-item>

            <el-form-item label="级别" prop="level">
              <el-select v-model="moduleForm.level" placeholder="请选择级别" @change="handleLevelChange">
                <el-option label="一级" :value="1" />
                <el-option label="二级" :value="2" />
                <el-option label="三级" :value="3" />
                <el-option label="四级" :value="4" />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="moduleForm.level > 1"
              label="父模块"
              prop="parentId"
            >
              <el-select v-model="moduleForm.parentId" placeholder="请选择父模块" @change="handleParentIdChange">
                <el-option label="无" :value="0" />
                <el-option
                  v-for="module in filteredParentModules"
                  :key="module.id"
                  :label="`${module.moduleName} (${module.moduleCode})`"
                  :value="module.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="URL">
              <el-input v-model="moduleForm.moduleUrl" placeholder="请输入模块URL" />
            </el-form-item>

            <el-form-item label="图标URL">
              <el-input v-model="moduleForm.icon" placeholder="请输入图标URL" />
            </el-form-item>

            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="moduleForm.sortOrder" :min="0" />
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-select v-model="moduleForm.status" placeholder="请选择状态">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-form>

          <template #footer>
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="saveModule">保存</el-button>
          </template>
        </el-dialog>
      </div>
    </div>
  </HomeBg>
</template>

<script setup lang="ts">
import HomeBg from "../../components/HomeBg.vue";
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { Search, Refresh, Plus } from '@element-plus/icons-vue';
import {
  getModulesList,
  getModuleById,
  createModule,
  updateModule,
  deleteModule,
  getNextModuleCode,
  getNextSortOrder,
  ModuleData
} from "../../api/modules";

// 搜索关键词
const searchKeyword = ref("");

// 加载状态
const loading = ref(false);

// 表格数据
const tableData = ref<ModuleData[]>([]);

// 所有模块数据（用于父模块选择）
const allModules = ref<ModuleData[]>([]);

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增模块");
const moduleFormRef = ref<FormInstance>();

// 模块表单数据
const moduleForm = reactive<ModuleData>({
  id: undefined,
  moduleName: "",
  moduleCode: "",
  level: 1,
  parentId: 0,
  moduleUrl: "",
  icon: "",
  sortOrder: 0,
  status: 1
});

// 表单验证规则
const formRules = {
  moduleName: [
    { required: true, message: '请输入模块名称', trigger: 'blur' }
  ],
  moduleCode: [
    { required: true, message: '请输入模块代码', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择级别', trigger: 'change' }
  ],
  parentId: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (moduleForm.level > 1 && value === 0) {
          callback(new Error(`请为${getLevelText(moduleForm.level)}模块选择父模块`));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 计算属性：过滤后的父模块列表
const filteredParentModules = computed(() => {
  return allModules.value.filter(module => {
    const level = moduleForm.level;
    if (level === 1) return false; // 一级模块不需要父模块
    if (level === 2) return module.level === 1; // 二级模块只能选择一级模块
    if (level === 3) return module.level === 2; // 三级模块只能选择二级模块
    if (level === 4) return module.level === 3; // 四级模块只能选择三级模块
    return false;
  });
});

// 获取级别文本
const getLevelText = (level: number) => {
  const levelMap: { [key: number]: string } = {
    1: '一级',
    2: '二级',
    3: '三级',
    4: '四级'
  };
  return levelMap[level] || '';
};

// 加载模块列表
const loadModules = async () => {
  loading.value = true;
  try {
    const response = await getModulesList();
    if (response.code === 200) {
      allModules.value = response.data;
      // 加载完成后，如果有搜索条件，则应用搜索过滤
      applySearchFilter();
    } else {
      ElMessage.error(response.message || '获取模块列表失败');
    }
  } catch (error) {
    console.error('获取模块列表失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('获取模块列表失败，请稍后重试');
    }
  } finally {
    loading.value = false;
  }
};

// 应用搜索过滤
const applySearchFilter = () => {
  const keyword = searchKeyword.value.trim().toLowerCase();

  if (!keyword) {
    tableData.value = allModules.value;
    return;
  }

  tableData.value = allModules.value.filter(module =>
    module.moduleName.toLowerCase().includes(keyword) ||
    module.moduleCode.toLowerCase().includes(keyword)
  );
};

// 搜索模块
const searchModules = () => {
  applySearchFilter();
};

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = "";
  applySearchFilter();
};

// 显示新增模态框
const showAddModal = () => {
  dialogTitle.value = '新增模块';
  resetModuleForm();
  dialogVisible.value = true;
  // 获取默认值
  handleLevelChange();
};

// 编辑模块
const editModule = async (id: number) => {
  dialogTitle.value = '编辑模块';
  try {
    const response = await getModuleById(id);
    if (response.code === 200) {
      const module = response.data;
      Object.assign(moduleForm, module);
      dialogVisible.value = true;
    } else {
      ElMessage.error(response.message || '获取模块信息失败');
    }
  } catch (error) {
    console.error('获取模块信息失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('获取模块信息失败，请稍后重试');
    }
  }
};

// 重置模块表单
const resetModuleForm = () => {
  Object.assign(moduleForm, {
    id: undefined,
    moduleName: "",
    moduleCode: "",
    level: 1,
    parentId: 0,
    moduleUrl: "",
    icon: "",
    sortOrder: 0,
    status: 1
  });
  moduleFormRef.value?.clearValidate();
};

// 处理级别变更
const handleLevelChange = async () => {
  if (moduleForm.level === 1) {
    moduleForm.parentId = 0;
  }

  // 获取模块代码和排序值
  await fetchNextModuleCode();
  await fetchNextSortOrder();
};

// 处理父模块变更
const handleParentIdChange = async () => {
  // 获取模块代码和排序值
  await fetchNextModuleCode();
  await fetchNextSortOrder();
};

// 获取下一个可用的模块代码
const fetchNextModuleCode = async () => {
  try {
    const response = await getNextModuleCode(
      moduleForm.level,
      moduleForm.parentId,
      moduleForm.id
    );
    if (response.code === 200) {
      moduleForm.moduleCode = response.data;
    } else {
      console.error('获取模块代码失败:', response.message);
      // 显示后端返回的具体错误信息
      ElMessage.error(response.message || '获取模块代码失败');
      moduleForm.moduleCode = '';
    }
  } catch (error) {
    console.error('获取模块代码失败:', error);
    // 使用后台返回的错误信息
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('获取模块代码失败，请稍后重试');
    }
    moduleForm.moduleCode = '';
  }
};

// 获取下一个可用的排序值
const fetchNextSortOrder = async () => {
  try {
    const response = await getNextSortOrder(moduleForm.level, moduleForm.parentId);
    if (response.code === 200) {
      moduleForm.sortOrder = response.data;
    } else {
      console.error('获取排序值失败:', response.message);
      // 显示后端返回的具体错误信息
      ElMessage.error(response.message || '获取排序值失败');
    }
  } catch (error) {
    console.error('获取排序值失败:', error);
    // 使用后台返回的错误信息
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('获取排序值失败，请稍后重试');
    }
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  resetModuleForm();
};

// 保存模块
const saveModule = async () => {
  if (!moduleFormRef.value) return;

  try {
    await moduleFormRef.value.validate();

    // 根据级别确定父模块ID
    let parentId = moduleForm.parentId;
    if (moduleForm.level === 1) {
      parentId = 0;
    } else if (parentId === 0) {
      ElMessage.error(`请为${getLevelText(moduleForm.level)}模块选择父模块`);
      return;
    }

    const data = {
      ...moduleForm,
      parentId
    };

    const response = moduleForm.id
      ? await updateModule(data)
      : await createModule(data);

    if (response.code === 200) {
      ElMessage.success(moduleForm.id ? '更新成功' : '创建成功');
      closeDialog();
      loadModules();
    } else {
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存模块失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('保存失败，请稍后重试');
    }
  }
};

// 确认删除
const confirmDelete = (id: number) => {
  ElMessageBox.confirm('确定要删除这个模块吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteModuleById(id);
  }).catch(() => {
    // 用户取消删除
  });
};

// 删除模块
const deleteModuleById = async (id: number) => {
  try {
    const response = await deleteModule(id);
    if (response.code === 200) {
      ElMessage.success('删除成功');
      loadModules();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除模块失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('删除失败，请稍后重试');
    }
  }
};

// 组件挂载时获取数据
onMounted(() => {
  loadModules();
});
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 24px;

  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border-radius: 8px;
  margin-bottom: 24px;
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-card__body {
    padding: 24px;
  }
}

.table-card {
  .search-form {
    margin-bottom: 24px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      gap: 24px;
    }
    
    .form-item {
      display: flex;
      align-items: center;
      
      .form-label {
        width: 80px;
        font-size: 14px;
        color: #262626;
        margin-right: 12px;
      }
      
      .el-input, .el-date-picker {
        width: 240px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 16px;
      
      .el-button .el-icon {
        margin-right: 4px;
      }
    }
  }

  // 移除汇总相关样式，模块管理不需要

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    
    th.el-table__cell {
      background-color: #fafafa;
      color: #262626;
      font-weight: 500;
    }
    
    .cell {
      padding: 12px 16px;
    }
  }
}

// 移除分页相关样式，模块管理不需要分页

:deep(.el-input .el-input__prefix) {
  color: #a0a0a0;
}

// 模块管理特有样式
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #262626;
  }

  .el-select,
  .el-input-number {
    width: 100%;
  }
}

:deep(.el-table) {
  .el-tag {
    font-weight: 500;
  }

  .el-button + .el-button {
    margin-left: 8px;
  }
}
</style>