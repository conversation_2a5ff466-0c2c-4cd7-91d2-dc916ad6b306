<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块管理</title>
    <!-- 确保jQuery先加载 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- 更换layer的CDN源 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script>
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        body {
            font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            margin-top: 0;
            font-size: 32px;
            color: #333;
            font-weight: 600;
        }
        .tools {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 20px;
        }
        .btn-add {
            padding: 10px 20px;
            background-color: #0052D9;
            color: #fff;
            font-weight: bold;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: background-color 0.3s, transform 0.2s;
        }
        .btn-add:hover {
            background-color: #003f9a;
            transform: translateY(-1px);
        }
        .search-bar {
            display: flex;
            margin-bottom: 20px;
        }
        .search-bar input {
            flex: 1;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        .search-bar button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: #fff;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .search-bar button:hover {
            background-color: #096dd9;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            overflow: hidden;
        }
        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .table th {
            background-color: #fafafa;
            color: #595959;
            font-weight: 600;
        }
        .table tr:hover {
            background-color: #fafafa;
        }
        .btn-edit {
            padding: 6px 12px;
            background-color: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: background-color 0.3s;
            margin-right: 8px;
        }
        .btn-edit:hover {
            background-color: #096dd9;
        }
        .btn-delete {
            padding: 6px 12px;
            background-color: #ff4d4f;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: background-color 0.3s;
        }
        .btn-delete:hover {
            background-color: #d9363e;
        }
        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            padding: 30px;
            width: 50%;
            max-width: 600px;
            margin: 80px auto;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            position: relative;
        }
        .close {
            font-size: 26px;
            color: #999;
            cursor: pointer;
        }
        .form-control {
            padding: 8px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            outline: none;
        }
        .form-control:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        /* URL and icon inputs full width */
        #moduleUrl, #icon {
            width: 100%;
            box-sizing: border-box;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .btn-group {
            margin-top: 20px;
            text-align: right;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        .btn-secondary {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .status-enabled {
            color: #4CAF50;
            font-weight: bold;
        }
        .status-disabled {
            color: #F44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>模块管理</h1>
        <div class="tools">
            <button onclick="showAddModal()" class="btn-add">新增模块</button>
        </div>
        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="搜索模块名称或代码">
            <button onclick="searchModules()">搜索</button>
        </div>
        <table id="modulesTable" class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>模块名称</th>
                    <th>模块代码</th>
                    <th>级别</th>
                    <th>URL</th>
                    <th>图标URL</th>
                    <th>排序</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 模块列表 -->
            </tbody>
        </table>
    </div>

    <!-- 模块编辑模态框 -->
    <div id="moduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">新增模块</h2>
            <form id="moduleForm">
                <input type="hidden" id="moduleId">
                
                <div class="form-group">
                    <label>模块名称：</label>
                    <input type="text" id="moduleName" class="form-control" placeholder="请输入模块名称">
                </div>
                
                <div class="form-group">
                    <label>模块代码：</label>
                    <input type="text" id="moduleCode" class="form-control" readonly>
                </div>
                
                <div class="form-group">
                    <label>级别：</label>
                    <select id="level" class="form-control">
                        <option value="1">一级</option>
                        <option value="2">二级</option>
                        <option value="3">三级</option>
                        <option value="4">四级</option>
                    </select>
                </div>
                
                <div class="form-group" id="parentModuleGroup">
                    <label>父模块：</label>
                    <select id="parentId" class="form-control">
                        <option value="0">无</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>URL：</label>
                    <input type="text" id="moduleUrl" class="form-control" placeholder="请输入模块URL">
                </div>
                
                <div class="form-group">
                    <label>图标URL：</label>
                    <input type="text" id="icon" class="form-control" placeholder="请输入图标URL">
                </div>
                
                <div class="form-group">
                    <label>排序：</label>
                    <input type="number" id="sortOrder" class="form-control" value="0">
                </div>
                
                <div class="form-group">
                    <label>状态：</label>
                    <select id="status" class="form-control">
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                
                <div class="btn-group">
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 检查layer是否已加载
        function showMessage(msg, isSuccess) {
            if (typeof layer !== 'undefined') {
                layer.msg(msg);
            } else {
                // 如果layer未加载，使用alert作为备选
                alert(msg);
            }
        }
        
        // 页面加载完成后执行
        $(document).ready(function() {
            loadModules();
            loadParentModules();
            
            // 表单提交事件
            $('#moduleForm').on('submit', function(e) {
                e.preventDefault();
                saveModule();
            });

            // 级别选择变更事件
            $('#level').on('change', function() {
                handleLevelChange();
            });

            // 父模块选择变更事件
            $('#parentId').on('change', function() {
                handleParentIdChange();
            });

            // 点击模态框外部时不关闭，移除原有事件
            $(window).off('click');
            
            // ESC键关闭模态框
            $(document).keydown(function(e) {
                if (e.keyCode === 27) { // ESC键
                    closeModal();
                }
            });
        });

        // 从后端获取下一个可用的排序值
        function fetchNextSortOrder() {
            const level = parseInt($('#level').val());
            const parentId = parseInt($('#parentId').val());
            $.get(`/modules/nextSortOrder?level=${level}&parentId=${parentId}`, function(response) {
                if (response.code === 200) {
                    $('#sortOrder').val(response.data);
                } else {
                    console.error('获取排序值失败:', response.message);
                }
            });
        }
        
        // 处理级别变更
        function handleLevelChange() {
            const level = parseInt($('#level').val());
            const parentGroup = $('#parentModuleGroup');
            const parentSelect = $('#parentId');
            
            if (level === 1) { // 一级模块
                parentSelect.val('0'); // 确保一级模块的父模块ID为0
                parentGroup.hide();
            } else {
                parentGroup.show();
                
                // 根据级别过滤可选的父模块
                parentSelect.find('option').each(function() {
                    const optionLevel = parseInt($(this).data('level'));
                    const optionValue = parseInt($(this).val());
                    
                    // 显示符合条件的选项
                    if (optionValue === 0 || // 始终显示"无"选项
                        (level === 2 && optionLevel === 1) || // 二级模块只能选择一级模块
                        (level === 3 && optionLevel === 2) || // 三级模块只能选择二级模块
                        (level === 4 && optionLevel === 3)) { // 四级模块只能选择三级模块
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
                
                // 如果当前选中的父模块不符合条件，重置为"无"
                const currentParentLevel = parseInt(parentSelect.find('option:selected').data('level'));
                if (currentParentLevel && 
                    !((level === 2 && currentParentLevel === 1) ||
                      (level === 3 && currentParentLevel === 2) ||
                      (level === 4 && currentParentLevel === 3))) {
                    parentSelect.val('0');
                }
            }
            
            // 根据新级别或新父模块重新获取模块代码和排序
            fetchNextModuleCode();
            fetchNextSortOrder();
        }

        // 处理父模块变更
        function handleParentIdChange() {
            // 根据新父模块重新获取模块代码和排序
            fetchNextModuleCode();
            fetchNextSortOrder();
        }

        // 从后端获取下一个可用的模块代码
        function fetchNextModuleCode() {
            const level = parseInt($('#level').val());
            const parentId = parseInt($('#parentId').val());
            const moduleId = $('#moduleId').val();

            // 调用后端API获取可用的模块代码
            $.get(`/modules/nextCode?level=${level}&parentId=${parentId}&moduleId=${moduleId}`, function(response) {
                if (response.code === 200) {
                    $('#moduleCode').val(response.data);
                } else {
                    console.error('获取模块代码失败:', response.message);
                    $('#moduleCode').val('');
                    showMessage('获取模块代码失败');
                }
            });
        }

        // 加载父模块选项
        function loadParentModules() {
            $.get('/modules/list', function(response) {
                if (response.code === 200) {
                    const modules = response.data;
                    const parentSelect = $('#parentId');
                    
                    // 保留"无"选项
                    parentSelect.html('<option value="0">无</option>');
                    
                    // 按级别添加其他选项
                    modules.forEach(function(module) {
                        parentSelect.append(`<option value="${module.id}" data-level="${module.level}">${module.moduleName} (${module.moduleCode})</option>`);
                    });
                    
                    // 如果当前是编辑模式，需要重新处理级别变更
                    if ($('#moduleId').val()) {
                        handleLevelChange();
                    }
                }
            });
        }

        // 加载模块列表
        function loadModules() {
            $.get('/modules/list', function(response) {
                if (response.code === 200) {
                    const modulesList = response.data;
                    const tableBody = $('#modulesTable tbody');
                    tableBody.empty();
                    
                    modulesList.forEach(module => {
                        const levelIndent = '&nbsp;'.repeat((module.level - 1) * 4);
                        const levelText = module.level === 1 ? '一级' : 
                                          module.level === 2 ? '二级' : 
                                          module.level === 3 ? '三级' : '四级';
                        const statusClass = module.status === 1 ? 'status-enabled' : 'status-disabled';
                        const statusText = module.status === 1 ? '启用' : '禁用';
                        
                        let tr = `<tr>
                            <td>${module.id}</td>
                            <td>${levelIndent}${module.moduleName}</td>
                            <td>${module.moduleCode}</td>
                            <td>${levelText}</td>
                            <td>${module.moduleUrl || '-'}</td>
                            <td>${module.icon || '-'}</td>
                            <td>${module.sortOrder}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>
                                <button onclick="editModule(${module.id})" class="btn-edit">编辑</button>
                                <button onclick="confirmDelete(${module.id})" class="btn-delete">删除</button>
                            </td>
                        </tr>`;
                        tableBody.append(tr);
                    });
                } else {
                    showMessage('获取模块列表失败');
                }
            });
        }
        
        // 搜索模块
        function searchModules() {
            const keyword = $('#searchInput').val().trim().toLowerCase();
            
            if (!keyword) {
                loadModules();
                return;
            }
            
            $.get('/modules/list', function(response) {
                if (response.code === 200) {
                    const filteredModules = response.data.filter(module => 
                        module.moduleName.toLowerCase().includes(keyword) || 
                        module.moduleCode.toLowerCase().includes(keyword)
                    );
                    
                    const tableBody = $('#modulesTable tbody');
                    tableBody.empty();
                    
                    if (filteredModules.length === 0) {
                        tableBody.append('<tr><td colspan="9" style="text-align:center">没有找到匹配的模块</td></tr>');
                        return;
                    }
                    
                    filteredModules.forEach(module => {
                        const levelIndent = '&nbsp;'.repeat((module.level - 1) * 4);
                        const levelText = module.level === 1 ? '一级' : 
                                         module.level === 2 ? '二级' : 
                                         module.level === 3 ? '三级' : '四级';
                        const statusClass = module.status === 1 ? 'status-enabled' : 'status-disabled';
                        const statusText = module.status === 1 ? '启用' : '禁用';
                        
                        let tr = `<tr>
                            <td>${module.id}</td>
                            <td>${levelIndent}${module.moduleName}</td>
                            <td>${module.moduleCode}</td>
                            <td>${levelText}</td>
                            <td>${module.moduleUrl || '-'}</td>
                            <td>${module.icon || '-'}</td>
                            <td>${module.sortOrder}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>
                                <button onclick="editModule(${module.id})" class="btn-edit">编辑</button>
                                <button onclick="confirmDelete(${module.id})" class="btn-delete">删除</button>
                            </td>
                        </tr>`;
                        tableBody.append(tr);
                    });
                }
            });
        }

        // 显示新增模态框
        function showAddModal() {
            $('#modalTitle').text('新增模块');
            $('#moduleForm')[0].reset();
            $('#moduleId').val('');
            $('#level').val('1');
            $('#moduleCode').val(''); // 清空模块代码
            $('#sortOrder').val(''); // 清空排序
            handleLevelChange(); // 处理级别变更，过滤父模块选项并获取默认值
            $('#moduleModal').show();
        }

        // 显示编辑模态框
        function editModule(id) {
            $('#modalTitle').text('编辑模块');
            
            // 先加载父模块选项，确保在设置模块值之前父模块选项已经准备好
            $.get('/modules/list', function(parentResponse) {
                if (parentResponse.code === 200) {
                    const modules = parentResponse.data;
                    const parentSelect = $('#parentId');
                    
                    // 保留"无"选项
                    parentSelect.html('<option value="0">无</option>');
                    
                    // 按级别添加其他选项
                    modules.forEach(function(module) {
                        parentSelect.append(`<option value="${module.id}" data-level="${module.level}">${module.moduleName} (${module.moduleCode})</option>`);
                    });
                    
                    // 加载完父模块选项后，再获取要编辑的模块信息
                    $.get(`/modules/${id}`, function(response) {
                        if (response.code === 200) {
                            const module = response.data;
                            $('#moduleId').val(module.id);
                            $('#moduleName').val(module.moduleName);
                            
                            // 存储原始的级别和父模块ID，用于检测变更
                            $('#moduleModal').data('originalLevel', module.level);
                            $('#moduleModal').data('originalParentId', module.parentId);
                            
                            $('#level').val(module.level);
                            // 先设置级别，再处理父模块的显示/隐藏
                            if (module.level === 1) {
                                $('#parentModuleGroup').hide();
                                $('#parentId').val(0);
                            } else {
                                $('#parentModuleGroup').show();
                                // 过滤父模块选项
                                parentSelect.find('option').each(function() {
                                    const optionLevel = parseInt($(this).data('level'));
                                    const optionValue = parseInt($(this).val());
                                    
                                    if (optionValue === 0 || // 始终显示"无"选项
                                        (module.level === 2 && optionLevel === 1) || // 二级模块只能选择一级模块
                                        (module.level === 3 && optionLevel === 2) || // 三级模块只能选择二级模块
                                        (module.level === 4 && optionLevel === 3)) { // 四级模块只能选择三级模块
                                        $(this).show();
                                    } else {
                                        $(this).hide();
                                    }
                                });
                                
                                // 设置父模块
                                $('#parentId').val(module.parentId);
                            }
                            
                            $('#moduleCode').val(module.moduleCode); // 设置已有的模块代码
                            $('#moduleUrl').val(module.moduleUrl);
                            $('#icon').val(module.icon);
                            $('#sortOrder').val(module.sortOrder);
                            $('#status').val(module.status);
                            $('#moduleModal').show();
                        } else {
                            showMessage('获取模块信息失败');
                        }
                    });
                } else {
                    showMessage('获取父模块列表失败');
                }
            });
        }

        // 关闭模态框
        function closeModal() {
            $('#moduleModal').hide();
            // 重置表单，清空编辑状态
            $('#moduleForm')[0].reset();
            $('#moduleId').val('');
        }

        // 保存模块
        function saveModule() {
            // 获取表单数据
            const moduleId = $('#moduleId').val();
            const moduleName = $('#moduleName').val();
            const moduleCode = $('#moduleCode').val();
            const level = parseInt($('#level').val());
            const moduleUrl = $('#moduleUrl').val();
            const icon = $('#icon').val();
            const sortOrder = $('#sortOrder').val();
            const status = $('#status').val();
            
            // 根据级别确定父模块ID
            let parentId = parseInt($('#parentId').val());
            if (level === 1) {
                // 一级模块的父模块ID必须为0
                parentId = 0;
            } else if (parentId === 0) {
                // 非一级模块必须有父模块
                showMessage(`请为${level}级模块选择父模块`);
                return;
            }
            
            // 验证表单
            if (!moduleName) {
                showMessage('请输入模块名称');
                return;
            }
            
            if (!moduleCode) {
                showMessage('请输入模块代码');
                return;
            }
            
            // 构建数据对象
            const data = {
                moduleName: moduleName,
                moduleCode: moduleCode,
                level: level,
                parentId: parentId,
                moduleUrl: moduleUrl,
                icon: icon,
                sortOrder: parseInt(sortOrder),
                status: parseInt(status)
            };
            
            if (moduleId) {
                data.id = parseInt(moduleId);
            }
            
            // 发送请求
            $.ajax({
                url: moduleId ? '/modules/update' : '/modules/create',
                type: moduleId ? 'PUT' : 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 200) {
                        showMessage(moduleId ? '更新成功' : '创建成功');
                        closeModal();
                        loadModules(); // 重新加载模块列表
                        loadParentModules(); // 重新加载父模块选项
                    } else {
                        showMessage(response.message);
                    }
                },
                error: function() {
                    showMessage('请求失败，请稍后重试');
                }
            });
        }

        // 确认删除
        function confirmDelete(id) {
            if (typeof layer !== 'undefined' && layer.confirm) {
                layer.confirm('确定要删除这个模块吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    deleteModule(id);
                });
            } else {
                if (confirm('确定要删除这个模块吗？')) {
                    deleteModule(id);
                }
            }
        }

        // 删除模块
        function deleteModule(id) {
            $.ajax({
                url: '/modules/' + id,
                type: 'DELETE',
                success: function(response) {
                    if (response.code === 200) {
                        showMessage('删除成功');
                        loadModules(); // 重新加载模块列表
                        loadParentModules(); // 重新加载父模块选项
                    } else {
                        showMessage(response.message);
                    }
                },
                error: function() {
                    showMessage('请求失败，请稍后重试');
                }
            });
        }
    </script>
</body>
</html> 