/* 全局样式 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header h2 {
    margin: 0;
    color: #333;
}

.btn-add {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-add:hover {
    background-color: #45a049;
}

/* 搜索栏样式 */
.search-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.search-bar input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-bar button {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.search-bar button:hover {
    background-color: #1976D2;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

tr:hover {
    background-color: #f5f5f5;
}

/* 按钮样式 */
button {
    padding: 6px 12px;
    margin: 0 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

button:first-child {
    background-color: #2196F3;
    color: white;
}

button:last-child {
    background-color: #f44336;
    color: white;
}

button:hover {
    opacity: 0.8;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    width: 50%;
    max-width: 600px;
    border-radius: 5px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #2196F3;
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 代码生成按钮组 */
.code-input-group {
    display: flex;
    gap: 10px;
}

.code-input-group input {
    flex: 1;
}

.btn-generate {
    background-color: #607D8B;
    color: white;
    white-space: nowrap;
}

.btn-generate:hover {
    background-color: #455A64;
}

/* 图标选择按钮组 */
.icon-input-group {
    display: flex;
    gap: 10px;
}

.icon-input-group input {
    flex: 1;
}

.btn-icon {
    background-color: #9C27B0;
    color: white;
    white-space: nowrap;
}

.btn-icon:hover {
    background-color: #7B1FA2;
}

/* 表单按钮组 */
.form-buttons {
    margin-top: 20px;
    text-align: right;
}

.form-buttons button {
    margin-left: 10px;
}

.form-buttons button[type="submit"] {
    background-color: #4CAF50;
    color: white;
}

.form-buttons button[type="button"] {
    background-color: #9e9e9e;
    color: white;
}

/* 图标选择器样式 */
.icon-selector {
    padding: 20px;
}

.icon-selector .icon-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.icon-selector .icon-item {
    padding: 10px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.icon-selector .icon-item:hover {
    background-color: #f5f5f5;
    border-color: #2196F3;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .modal-content {
        width: 90%;
        margin: 5% auto;
    }
    
    .code-input-group,
    .icon-input-group {
        flex-direction: column;
    }
    
    .btn-generate,
    .btn-icon {
        width: 100%;
    }
} 