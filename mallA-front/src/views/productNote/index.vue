<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '系统通知') router.push('/customerServer')
  if(item === '申请通知') router.push('/')
  if(item === '商品通知') router.push('/productNote')
  if(item === '订单通知') router.push('/ordersNote')
  if(item === '结算通知') router.push('/settlementNote')
  if(item === '退款通知') router.push('/refundNote')
  if(item === '商家入驻通知') router.push('/businessSettledNote')
  if(item === '实名申请') router.push('/realNameReview')
  if(item === '账号注销') router.push('/logout')

}

const buttonList = [
  '全部消息', '系统通知', '申请通知', '商品通知','订单通知',
  '结算通知','退款通知','商家入驻通知','实名申请','账号注销'
]

// 表单数据
const form = ref({
  startTime: '',
  endTime: '',
  status: ''
})

// 表格数据
const tableData = ref([
  {
    id: '1001',
    product: '商品A',
    price: '¥199',
    orderNumber: 'ORD20231101001',
    time: '2023-11-01 14:30',
    status: '已上架'
  },
  {
    id: '1002',
    product: '商品B',
    price: '¥299',
    orderNumber: 'ORD20231102002',
    time: '2023-11-02 09:15',
    status: '已下架'
  },
  {
    id: '1003',
    product: '商品C',
    price: '¥399',
    orderNumber: 'ORD20231103003',
    time: '2023-11-03 16:45',
    status: '已上架'
  }
])

const handleSearch = () => {
  console.log('搜索条件:', form.value)
  // 这里应该调用API获取筛选后的数据
}

// 上架/下架操作
const toggleProductStatus = (row) => {
  const newStatus = row.status === '已上架' ? '已下架' : '已上架'
  // 在实际应用中，这里应该调用API更新状态
  row.status = newStatus
  ElMessage.success(`操作成功，商品已${newStatus}`)
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      <div class="rightBox">
        <div class="head">
          <el-form :model="form" class="filter-form">
            <!-- 第一行：时间范围 -->
            <div class="form-row">
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="form.startTime"
                  type="datetime"
                  placeholder="选择开始时间"
                />
              </el-form-item>
              <span class="time-separator">至</span>
              <el-form-item label="结束时间">
                <el-date-picker
                  v-model="form.endTime"
                  type="datetime"
                  placeholder="选择结束时间"
                />
              </el-form-item>
            </div>
            
            <!-- 第二行：状态筛选 -->
            <div class="form-row">
              <el-form-item label="状态">
                <el-select v-model="form.status" placeholder="请选择状态">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="已上架" value="on"></el-option>
                  <el-option label="已下架" value="off"></el-option>
                </el-select>
              </el-form-item>
              <el-button 
                type="primary" 
                :icon="Search"
                @click="handleSearch"
              >
                搜索
              </el-button>
            </div>
          </el-form>
        </div>
        
        <div class="main">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="id" label="手机号" width="150" />
            <el-table-column prop="product" label="商品" width="150" />
            <el-table-column prop="price" label="价格" width="150"/>
            <el-table-column prop="orderNumber" label="商品信息" width="200"/>
            <el-table-column prop="time" label="时间" width="220" />
            <el-table-column prop="status" label="状态" width="150">
              <template #default="{ row }">
                <el-tag :type="row.status === '已上架' ? 'success' : 'danger'">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button
                  size="small"
                  :type="row.status === '已上架' ? 'danger' : 'success'"
                  @click="toggleProductStatus(row)"
                >
                  {{ row.status === '已上架' ? '下架' : '上架' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.rightBox {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  
  .head {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #3A58CF;
    
    .filter-form {
      .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        .el-form-item {
          margin-right: 20px;
          margin-bottom: 0;
        }
        
        .time-separator {
          margin: 0 10px;
          color: #666;
        }
      }
    }
  }
  
  .main {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    overflow: hidden;
    
    :deep(.el-table) {
      height: 100%;
      
      th {
        background-color: #f5f7fa;
        font-weight: bold;
      }
      
      .el-table__cell {
        padding: 12px 0;
      }
    }
  }
}
</style>