<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ref, watch } from 'vue'

const value = ref('')
const router = useRouter()
const selectAll = ref(false)

const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址', '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]

const tableData = ref([
  {
    username: '12345156',
    brand: '寿喜烧',
    selected: true
  },
  {
    username: '6778321',
    brand: '章鱼小丸子',
    selected: false
  },
   {
    username: '956315',
    brand: '霸王茶几',
    selected: false
  },
  {
    username: '654321',
    brand: '奈雪',
    selected: false
  },
   {
    username: '413415412156',
    brand: '喜茶',
    selected: false
  },
  {
    username: '654321',
    brand: '测试品牌',
    selected: false
  }
])

const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
  if(item === '商品代销申请') router.push('./productSellApply')
}

const handleSelectAll = (val) => {
  tableData.value.forEach(item => {
    item.selected = val
  })
}

// 监听全选状态
watch(selectAll, (newVal) => {
  handleSelectAll(newVal)
})
</script>

<template>
    <ManageBg>
        <div class="container">
             <div class="left-buttons">
             <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
             </div>
             
             <div class="right-content">
                <div class="header">
                    <div class="input-group">
                        <input class="brandName" type="text" placeholder="输入品牌名称">
                        <input class="username" type="number" placeholder="输入账号">
                    </div>
                    <div class="search-box">
                        <el-icon class="search-icon"><Search /></el-icon>
                        <input type="text" placeholder="搜索" class="search-input">
                    </div>
                </div>
                
                <div class="batch-buttons">
                    <button class="open">批量开启</button>
                    <button class="close">批量关闭</button>
                </div>
                       <div class="table-container">
                    <el-table 
                        :data="tableData" 
                        style="width: 100%" 
                        height="340"
                        header-row-class-name="custom-header"
                        row-class-name="custom-row"
                    >
                        <el-table-column width="150">
                            <template #header>
                                <el-checkbox class="select-all-checkbox">全选</el-checkbox>
                            </template>
                            <template #default="{ row }">
                                <el-checkbox v-model="row.selected" class="row-checkbox">
                                    选择
                                </el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column prop="username" label="账号" width="200" />
                        <el-table-column prop="brand" label="品牌" width="200" />
                        <el-table-column label="操作">
                            <template #default>
                                <el-button link type="primary" size="small">编辑</el-button>
                                <el-button link type="danger" size="small">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
             </div>
        </div>
    </ManageBg>    
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
    flex: 1;
    padding: 33px 49px;
    display: flex;
    flex-direction: column;
}

.header {
    background: linear-gradient(to bottom, #3A58CF, #83A4EB);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    height: 195px;
    padding: 30px;
    box-sizing: border-box;
    border-radius: 8px;

    .input-group {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .brandName, .username {
            width: 579px;
            height: 46px;
            background-color: #fff;
            font-size: 20px;
            padding: 0 15px;
            border: none;
            border-radius: 4px;
            outline: none;
         
            &::placeholder {
                color: #808080;
            }
        }
        .username {
            -moz-appearance: textfield;
            
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }
        }
    }

    .search-box {
        width: 309px;
        height: 46px;
        background-color: #fff;
        border-radius: 23px;
        display: flex;
        align-items: center;
        padding: 0 15px;
        box-sizing: border-box;

        .search-icon {
            font-size: 20px;
            color: #3A58CF;
            margin-left: 5px;
            margin-right: 10px;
        }

        .search-input {
            flex: 1;
            height: 100%;
            border: none;
            outline: none;
            font-size: 16px;
            background: transparent;
            
            &::placeholder {
                color: #808080;
            }
        }
    }
}

.batch-buttons {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    
    .open, .close {
        width: 235px;
        height: 46px;
        background-color: #fff;
        font-size: 24px;
        color: #3A58CF;
        border: 1px solid #3A58CF;
        border-radius: 35px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
            background-color: #f0f4ff;
        }
    }
}
.table-container {
    margin-top: 20px;
    padding: 0 20px;
    
    :deep(.custom-header) {
        th {
            background-color: #83A4EB !important;
            color: #000;
            font-weight: bold;
            font-size: 16px;
            
            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }
                .el-checkbox__label {
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }
    
    :deep(.custom-row) {
        td {
            background-color: #D2E0FB;
            
            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }
                .el-checkbox__label {
                    font-size: 14px;
                }
            }
        }
        
        &:hover td {
            background-color: #b8cdf9 !important;
        }
    }
    
    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;
        
        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>