<script setup>
import HomeBg from '../../components/HomeBg.vue'
import {ref} from 'vue'
import {useRouter} from 'vue-router'
const router = useRouter();
const handleToNormalManager = () => {
    router.push({
      path:'/normalManager'
    })
}
const form = ref({
  StoreName: '',
  password: '',
  password2: '',
  phoneNumber:'',
  legalName:'',
  creditNum:'',

})
</script>


<template>
    <HomeBg>
        <div class="container">
            <img class="bg" src="../../images/bigBackground.png" alt="背景图">
      <div class="form-container">
      
        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.StoreName" 
            placeholder="店铺名称" 
          />
          
        </div>
        
        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.password" 
            placeholder="密码" 
          />
          
        </div>
        
        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.password2" 
            placeholder="确认密码"
            type="password"
            show-password
          />
        
        </div>
        
        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.phoneNumber" 
            placeholder="手机号" 
          />
        </div>

        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.creditNum" 
            placeholder="企业信用号" 
          />
        </div>

           <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.legalName" 
            placeholder="法人名字" 
          />
        </div>
        <div class="save-button-container">
          <el-button class="save-button" @click="handleToNormalManager">保存</el-button>
        </div>
      </div>
        </div>
       
    </HomeBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  .form-container {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    width: 100%;
    max-width: 1200px;

    .input-group {
      display: flex;
      align-items: center;
      margin-bottom: 61px;
      width: 100%;
      margin-left: 399px;

      .form-input {
        width: 805px;
        
        :deep(.el-input__wrapper) {
          height: 53px;
          font-size: 16px;
        }
      }

      .reserved-text {
        margin-left: 55px;
        color: #FF8D1A;
        font-size: 16px;
        white-space: nowrap;
      }
    }
.save-button-container {
      width: 805px;
      margin: 0 auto;
      text-align: center;
      
      .save-button {
        width: 120px;
        height: 40px;
        background-color: #14097A;
        color: white;
        border: none;
        font-size: 16px;
        border-radius: 4px;
        
        &:hover {
          background-color: #1a0da0;
        }
        
        &:active {
          background-color: #0f0657;
        }
      }
    }
  }
}
</style>