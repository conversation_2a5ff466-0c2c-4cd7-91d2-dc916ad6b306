<script lang="ts" setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref, reactive } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  accountId: '',
  phone: '',
  settlementMethod: '',
  startTime: '',
  endTime: '',
  searchText: ''
})

// 表格数据
const tableData = ref([
  {
    id:'**********',
    time: '2025-11-4 16:30',
    account: 'user001',
    accountNum:"444444444444444444444",
    companyName: '游戏公司',
    name: '李四',
    todayVoucher: '8200',
    total: '500.00'
  },
  // 可以添加更多数据...
])

// 状态按钮
const statusButtons = [
  { label: '全部记录', value: 'all' },
  { label: '待审核', value: 'pending' },
  { label: '待打款', value: 'waiting' },
  { label: '已打款', value: 'paid' },
  { label: '已驳回', value: 'rejected' }
]

const activeStatus = ref('all')

const onExport = () => {
  console.log('导出Excel')
}

const handleSearch = () => {
  console.log('搜索操作')
}

const handleShelf = (row: any) => {
  console.log('上架操作:', row)
}

const handleUnshelf = (row: any) => {
  console.log('下架操作:', row)
}

const router = useRouter()
const buttonList = [
  '设置', '平台补贴券明细', '平台促销券明细', '抵扣金明细', '货款明细',
  '已核销平台补贴券明细', '结算统计', '结算记录',
  '广告收益', '结算审核', '量化值进化量明细', '量化进化量明细'
]

const handleButtonClick = (item) => {
  if (item === '设置') router.push('./financeSetting')
  if (item === '平台补贴券明细') router.push('./platformVoucherDetail')
  if (item === '平台促销券明细') router.push('./platformCouponDetail')
  if (item === '抵扣金明细') router.push('./commissionDetail')
  if (item === '货款明细') router.push('./loanDetail')
  if (item === '已核销平台补贴券明细') router.push('./verifyVoucherDetail')
  if (item === '结算统计') router.push('./settlementCount')
  if (item === '结算记录') router.push('./settlementRecord')
  if (item === '广告收益') router.push('./')
  if (item === '结算审核') router.push('./settlementReview')
  if (item === '量化值进化量明细') router.push('./quantifyEvolutionDetail')
  if (item === '量化进化量明细') router.push('./creditEvolutionDetail')
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <div class="filter-container">
          <!-- 状态按钮行 -->
          <div class="status-buttons">
            <el-button
              v-for="button in statusButtons"
              :key="button.value"
              :type="activeStatus === button.value ? 'primary' : ''"
              @click="activeStatus = button.value"
            >
              {{ button.label }}
            </el-button>
          </div>
          
          <!-- 表单筛选区域 -->
          <el-form :model="form" class="filter-form">
            <!-- 账号ID行 -->
            <div class="form-row">
              <span class="form-label">账号ID</span>
              <el-form-item>
                <el-input v-model="form.accountId" placeholder="企业号" />
              </el-form-item>
            </div>
            
            <!-- 手机号行 -->
            <div class="form-row">
              <span class="form-label">手机号</span>
              <el-form-item>
                <el-input v-model="form.phone" />
              </el-form-item>
            </div>
            
            <!-- 结算方式行 -->
            <div class="form-row">
              <span class="form-label">结算方式</span>
              <el-form-item>
                <el-input v-model="form.settlementMethod" />
              </el-form-item>
            </div>
            
            <!-- 结算时间行 -->
            <div class="form-row">
              <span class="form-label">结算时间</span>
              <el-form-item>
                <el-date-picker
                  v-model="form.startTime"
                  type="datetime"
                  placeholder="开始时间"
                />
              </el-form-item>
              <span class="time-separator">至</span>
              <el-form-item>
                <el-date-picker
                  v-model="form.endTime"
                  type="datetime"
                  placeholder="结束时间"
                />
              </el-form-item>
            </div>
            
            <!-- 操作按钮行 -->
            <div class="action-buttons">
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                <span>搜索</span>
              </el-button>
              <el-button type="warning" @click="onExport">
                导出
              </el-button>
            </div>
          </el-form>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-container">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="id" label="账号ID" width="100" />
            <el-table-column prop="time" label="结算时间" width="100" />
            <el-table-column prop="account" label="结算单号" width="100" />
            <el-table-column prop="accountNum" label="结算卡号" width="200" />
            <el-table-column label="公司名称/姓名" width="300">
              <template #default="{row}">
                <div>{{ row.companyName }}</div>
                <div>{{ row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="todayVoucher" label="结算方式" width="200" />
            <el-table-column prop="total" label="货款/抵扣金" width="200" />
            <el-table-column label="操作" width="180">
              <template #default="{row}">
                <el-button class="shelf-btn" @click="handleShelf(row)">
                  上架
                </el-button>
                <el-button class="unshelf-btn" @click="handleUnshelf(row)">
                  下架
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>    
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  
  .status-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    
    .el-button {
      flex: 1;
    }
  }
  
  .filter-form {
    .form-row {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      
      .form-label {
        width: 80px;
        text-align: right;
        margin-right: 10px;
        font-size: 14px;
        color: #606266;
      }
      
      .el-form-item {
        margin-bottom: 0;
        flex: 1;
      }
      .el-form-item1 {
        margin-bottom: 0;

      }
      
      .time-separator {
        margin: 0 10px;
        color: #606266;
      }
    }
    
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 20px;
      
      .el-button {
        width: 120px;
      }
    }
  }
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  flex: 1;
  
  :deep(.el-table) {
    font-size: 14px;
    
    th {
      background-color: #f5f7fa;
      color: #333;
      font-weight: bold;
    }
    
    .cell {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}

:deep(.shelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  background-color: pink;
  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.unshelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  margin-left: 0px;
  background-color: cyan;
  &:hover {
    transform: translateY(-1px);
  }
}
</style>