<template>
  <div class="page-container">
    <div class="content-wrapper">

      <!-- 数据名称管理 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>数据名称管理</span>
          </div>
        </template>
        <div class="input-section">
          <el-input
            v-model="inputValue"
            placeholder="请输入数据名称"
            clearable
            style="width: 300px; margin-right: 10px;"
          />
          <el-button type="primary" @click="addItem">添加</el-button>
        </div>
        <el-scrollbar height="150px" style="margin-top: 20px;">
          <el-table :data="tradeDataList" border>
            <el-table-column prop="dataName" label="交易名称" />
            <el-table-column prop="updateTime" label="添加时间" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </el-card>

      <!-- 交易数据参数设置 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>交易数据参数设置</span>
          </div>
        </template>
        <div class="setting-item">
          <el-radio-group v-model="radio1">
            <el-radio value="0" size="large">开启</el-radio>
            <el-radio value="1" size="large">关闭</el-radio>
          </el-radio-group>
        </div>
        <div class="setting-row">
          
          <div class="setting-item">
            <span class="setting-label">每笔交易数据</span>
            <el-input
              class="setting-input"
              v-model="message2"
              placeholder="请输入百分比"
            >
              <template #append>%</template>
            </el-input>
          </div>
          <div class="setting-item">
            <el-button type="primary" @click="submit">保存</el-button>
          </div>
        </div>
      </el-card>

      <!-- 数据查询与列表 -->
      <el-card class="table-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>数据查询与列表</span>
          </div>
        </template>

        <!-- 搜索表单 -->
        <div class="search-form">
          <div class="form-row">
            <div class="form-item">
              <span class="form-label">手机号</span>
              <el-input v-model="searchPhone" placeholder="请输入手机号" clearable>
                <template #prefix>
                  <el-icon><Phone /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="form-item">
              <span class="form-label">查询日期</span>
              <el-date-picker v-model="startDate" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="validateDateRange" />
            </div>
          </div>
          <div class="form-actions">
            <el-button type="primary" @click="filterData"><el-icon><Search /></el-icon>搜索</el-button>
            <el-button @click="resetSearch"><el-icon><Refresh /></el-icon>重置</el-button>
            <el-button type="success" @click="exportToExcel"><el-icon><Download /></el-icon>导出</el-button>
          </div>
        </div>

        <!-- 汇总信息 -->
        <div class="summary-section">
          <div class="summary-item">
            <span class="summary-label">今日总交易量:</span>
            <el-input
              :model-value="summaryData.sumTradeAmount || '0.00'"
              readonly
              class="summary-input"
            />
          </div>
          <div class="summary-item">
            <span class="summary-label">今日总合计数:</span>
            <el-input
              :model-value="summaryData.sumTotalCount || '0.00'"
              readonly
              class="summary-input"
            />
          </div>
        </div>
        
        <!-- 数据表格 -->
        <el-table :data="tableData" stripe border style="width: 100%">
          <el-table-column prop="updateTime" label="时间" width="180" />
          <el-table-column prop="phone" label="电话号码" width="150" />
          <el-table-column prop="enterpriseName" label="公司名称" />
          <el-table-column prop="tradeName" label="交易名称" />
          <el-table-column prop="tradeAmount" label="今日交易记录" />
          <el-table-column prop="totalCount" label="今日合计数" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @current-change="handlePageChange"
            @size-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { Search, Refresh, Download, Phone } from '@element-plus/icons-vue';
import { 
  queryZNHTradeDataPages, 
  zNHTradeDataExport, 
  tradeDataSet, 
  getTradeDataSet, 
  deleteTradeDataSet, 
  tradeDataParameterSet, 
  getTradeDataParameterSet 
} from "../../api/transactionData/index";
import { getErrorMessage } from "../../utils/errorHandler";

const radio1 = computed({
  get: () => tradeDataParameter.value.onOff,
  set: (value) => {
    tradeDataParameter.value.onOff = value;
  }
});

const router = useRouter();
const inputValue = ref("");

const tableData = ref([]);
const summaryData = ref({
  sumTradeAmount: 0,
  sumTotalCount: 0
});

const tradeDataList = ref([]);
const tradeDataParameter = ref({
  id: '',
  enterpriseId: '1',
  perTradeAmount: '',
  onOff: '0',
  updateTime: ''
});

const message2 = computed({
  get: () => tradeDataParameter.value.perTradeAmount,
  set: (value) => {
    tradeDataParameter.value.perTradeAmount = value;
  }
});

const fetchTradeDataParameter = async () => {
  try {
    const response = await getTradeDataParameterSet();
    if (response.code === 200 && response.data) {
      tradeDataParameter.value = { ...response.data, onOff: response.data.onOff || '0' };
    }
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '获取交易数据参数失败'));
  }
};

const submit = async () => {
  if (!tradeDataParameter.value.perTradeAmount || tradeDataParameter.value.perTradeAmount.trim() === '') {
    ElMessage.warning('请输入每笔交易数据百分比');
    return;
  }
  try {
    await tradeDataParameterSet(tradeDataParameter.value);
    ElMessage.success('保存成功');
    fetchTradeDataParameter();
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '保存失败'));
  }
};

const searchPhone = ref('');
const startDate = ref('');
const endDate = ref('');

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const fetchTradeDataList = async () => {
  try {
    const response = await getTradeDataSet({ enterpriseId: '1' });
    if (response.code === 200) {
      tradeDataList.value = response.data?.tradeDataSet || [];
    }
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '获取交易名称列表失败'));
  }
};

const addItem = async () => {
  if (!inputValue.value.trim()) {
    ElMessage.warning("请输入数据名称");
    return;
  }
  try {
    await tradeDataSet({ enterpriseId: '1', tradeName: inputValue.value.trim() });
    ElMessage.success("添加成功");
    inputValue.value = "";
    fetchTradeDataList();
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '添加失败'));
  }
};

const handleDelete = async (index, row) => {
  try {
    await deleteTradeDataSet({ id: row.id });
    ElMessage.success("删除成功");
    fetchTradeDataList();
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '删除失败'));
  }
};

const fetchTradeData = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      phone: searchPhone.value || undefined,
      startTime: startDate.value || undefined,
      endTime: endDate.value || undefined
    };
    const response = await queryZNHTradeDataPages(params);
    if (response.code === 200) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
      summaryData.value = response.data.summary || { sumTradeAmount: 0, sumTotalCount: 0 };
    } else {
      ElMessage.error(response.message || '获取数据失败');
    }
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '获取数据失败'));
  }
};

const validateDateRange = () => {
  if (startDate.value && endDate.value && new Date(endDate.value) < new Date(startDate.value)) {
    ElMessage.error('结束日期不能小于开始日期');
    return false;
  }
  return true;
};

const filterData = () => {
  if (!validateDateRange()) return;
  currentPage.value = 1;
  fetchTradeData();
};

const resetSearch = () => {
  searchPhone.value = '';
  startDate.value = '';
  endDate.value = '';
  currentPage.value = 1;
  fetchTradeData();
};

const handlePageChange = () => {
  fetchTradeData();
};

const exportToExcel = async () => {
  if (!validateDateRange()) return;
  try {
    const response = await zNHTradeDataExport({
      phone: searchPhone.value,
      startTime: startDate.value,
      endTime: endDate.value
    });
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = `交易数据_${new Date().getTime()}.xlsx`;
    link.click();
  } catch (error) {
    ElMessage.error(getErrorMessage(error, '导出失败'));
  }
};

onMounted(() => {
  fetchTradeData();
  fetchTradeDataList();
  fetchTradeDataParameter();
});
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  height: 24px;
  
  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border-radius: 8px;
  margin-bottom: 24px;
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-card__body {
    padding: 24px;
  }
}

.setting-card {
  .input-section {
    display: flex;
    align-items: center;
  }
  .setting-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 24px;
    margin-top: 24px;
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .setting-label {
    margin-right: 12px;
    font-size: 14px;
    color: #262626;
  }
  
  .setting-input {
    width: 220px;
  }
}

.table-card {
  .search-form {
    margin-bottom: 24px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      gap: 24px;
    }
    
    .form-item {
      display: flex;
      align-items: center;
      
      .form-label {
        width: 80px;
        font-size: 14px;
        color: #262626;
        margin-right: 12px;
      }
      
      .el-input, .el-date-picker {
        width: 240px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 16px;
      
      .el-button .el-icon {
        margin-right: 4px;
      }
    }
  }

  .summary-section {
    display: flex;
    gap: 60px;
    margin-bottom: 24px;
    font-size: 14px;
  }

  .summary-item {
    display: flex;
    align-items: center;

    .summary-label {
      color: #595959;
      margin-right: 12px;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 500;
    }

    .summary-input {
      width: 180px;

      :deep(.el-input__wrapper) {
        background-color: #f5f5f5;
        box-shadow: 0 0 0 1px #e0e0e0 inset;
        border-radius: 4px;
      }

      :deep(.el-input__inner) {
        color: #1890ff;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    
    th.el-table__cell {
      background-color: #fafafa;
      color: #262626;
      font-weight: 500;
    }
    
    .cell {
      padding: 12px 16px;
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-start;
}

:deep(.el-input .el-input__prefix) {
  color: #a0a0a0;
}
</style>