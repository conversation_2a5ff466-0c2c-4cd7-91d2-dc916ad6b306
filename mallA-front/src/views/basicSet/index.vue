<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Left from "@/components/Left.vue";
import {Session} from "@/utils/storage.js";

const route = useRoute()


const menuList = ref([])
// const menuList = ref([
//   {
//     label: '系统设置',
//     path : '/basicSet/systemSet',
//   },
//   {
//     label: '系统查询',
//     path : '/basicSet/systemQery',
//   },
//   {
//     label: '状态数据',
//     path : '/basicSet/stateData',
//   },
// ])

// 当前激活菜单项
const openMenus = computed(() => {
  if (route.path.startsWith('/basicSet')) {
    return ['/basicSet']
  }
  return []
})

onMounted(() => {
  const route = useRoute();
  nextTick(() => {
    const _menuList = Session.get("menuList");
    for (let i = 0; i < _menuList.length; i++) {
      const item = _menuList[i];
      if (item.id == route.query.id){
        console.log(item.children)
        menuList.value = item.children
      }
    }
  })
})

// 菜单按钮点击跳转
const handleButtonClick = (item) => {
  if (item === "系统设置") router.push("/basicSet/systemSet")
  if (item === "系统查询") router.push("/basicSet/systemQery")
  if (item === "状态数据") router.push("/basicSet/stateData")
}

onMounted(() => {
  const route = useRoute();
  nextTick(() => {
    const _menuList = Session.get("menuList");
    for (let i = 0; i < _menuList.length; i++) {
      const item = _menuList[i];
      if (item.id == route.query.id){
        menuList.value = item.children
      }
    }
  })
})

</script>

<template>
  <HomeBg>
    <el-container style="height: 100vh;">

      <Left :list="menuList"></Left>

      <!-- 左侧菜单 -->
<!--      <el-aside width="180px" class="sub-menu">-->
<!--        <el-menu-->
<!--          :default-active="$route.path"-->
<!--          router-->
<!--          background-color="#fff"-->
<!--          text-color="#666"-->
<!--          active-text-color="#3a5bde"-->
<!--          :default-openeds="openMenus"-->
<!--          class="aside-menu"-->
<!--        >-->
<!--          <el-submenu index="/basicSet">-->
<!--            <template  #title>基础设置</template>-->
<!--            <el-menu-item index="/basicSet/systemSet">系统设置</el-menu-item>-->
<!--            <el-menu-item index="/basicSet/systemQuery">系统查询</el-menu-item>-->
<!--            <el-menu-item index="/basicSet/stateData">状态数据</el-menu-item>-->
<!--          </el-submenu>-->
<!--        </el-menu>-->
<!--      </el-aside>-->

<!--      &lt;!&ndash; 主内容区域 &ndash;&gt;-->
<!--      <el-main class="main-content">-->
<!--        <router-view />-->
<!--      </el-main>-->

    </el-container>
  </HomeBg>
</template>

<style lang="scss" scoped>

/* 去除二级菜单默认边框 */
:deep(.el-submenu .el-menu) {
  border-right: none !important;
  background-color: transparent !important;
}

:deep(.el-submenu__title) {
  background-color: #fff !important;
}

/* 侧边栏菜单样式 */
.sub-menu {
  height: 100%;
  border-right: 1px solid #000;
  background-color: #fff;
}

.el-menu-item.is-active {
  background-color: #2c3e50 !important;
  color: #fff !important;
}

.aside-menu {
  border-right: none;
  position: fixed;
  width: 179px;
}

/* 主体内容样式 */
.main-content {
  padding: 20px;
  background-color: #f9f9f9;
}

/* 强制让 el-menu-item 内容居中 */
:deep(.el-menu-item) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  font-size: 16px;
}

/* 如果只想对基础系统的子菜单生效，可以加更具体的类名或结构选择器 */
</style>