<script lang="ts" setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref, reactive } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  accountId: '',
  paymentNumber: '',
  amount: '',
  status: '',
  startTime: '',
  endTime: '',
  searchText: ''
})

// 表格数据
const tableData = ref([
  {
    id: '10001',
    paymentNumber: 'PY202511041630',
    amount: '500.00',
    status: '待审核',
    time: '2025-11-4 16:30',
    companyName: '游戏公司',
    name: '李四'
  },
  // 可以添加更多数据...
])

// 状态按钮
const statusButtons = [
  { label: '全部记录', value: 'all' },
  { label: '待审核', value: 'pending' },
  { label: '待打款', value: 'waiting' },
  { label: '已打款', value: 'paid' },
  { label: '已驳回', value: 'rejected' }
]

const activeStatus = ref('all')

const onExport = () => {
  console.log('导出Excel')
}

const handleSearch = () => {
  console.log('搜索操作')
}

const handleApprove = (row: any) => {
  console.log('通过审核:', row)
}

const handleReject = (row: any) => {
  console.log('不通过审核:', row)
}

const router = useRouter()
const buttonList = [
  '设置', '平台补贴券明细', '平台促销券明细', '抵扣金明细', '货款明细',
  '已核销平台补贴券明细', '结算统计', '结算记录',
  '广告收益', '结算审核', '量化值进化量明细', '量化进化量明细'
]

const handleButtonClick = (item) => {
  if (item === '设置') router.push('./financeSetting')
  if (item === '平台补贴券明细') router.push('./platformVoucherDetail')
  if (item === '平台促销券明细') router.push('./platformCouponDetail')
  if (item === '抵扣金明细') router.push('./commissionDetail')
  if (item === '货款明细') router.push('./loanDetail')
  if (item === '已核销平台补贴券明细') router.push('./verifyVoucherDetail')
  if (item === '结算统计') router.push('./settlementCount')
  if (item === '结算记录') router.push('./settlementRecord')
  if (item === '广告收益') router.push('./')
  if (item === '结算审核') router.push('./settlementReview')
  if (item === '量化值进化量明细') router.push('./quantifyEvolutionDetail')
  if (item === '量化进化量明细') router.push('./creditEvolutionDetail')
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <div class="filter-container">
          <div class="filter-header">
            <span class="filter-title">结算审核</span>
            <el-button type="warning" @click="onExport" class="export-btn">
              导出
            </el-button>
          </div>
          
        
              
            
        </div>
        
        <!-- 表格区域 -->
        <div class="table-container">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="id" label="ID账号" width="250" />
            <el-table-column prop="paymentNumber" label="支付单号" width="300" />
            <el-table-column prop="amount" label="金额" width="150" />
            <el-table-column prop="status" label="状态" width="150" />
            <el-table-column prop="time" label="时间" width="260" />
            <el-table-column label="操作" width="250">
              <template #default="{row}">
                <el-button 
                  type="success" 
                 
                  class="pass"
                  @click="handleApprove(row)"
                >
                  通过审核
                </el-button>
                <el-button 
                  type="danger" 
                 
                  class="noPass"
                  @click="handleReject(row)"
                >
                  不通过审核
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>    
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  
  .filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .filter-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
    }
    
    .export-btn {
      background-color: #FF8D1A;
    }
  }
  
  .filter-form {
    .form-row {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      
      .el-form-item {
        margin-right: 20px;
        margin-bottom: 0;
        
        :deep(.el-form-item__label) {
          padding-bottom: 0;
          font-weight: normal;
        }
      }
      
      .time-separator {
        margin: 0 10px;
        color: #666;
      }
      
      .search-btn {
        margin-left: 20px;
      }
    }
  }
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  flex: 1;
  
  :deep(.el-table) {
    font-size: 14px;
    
    th {
      background-color: #f5f7fa;
      color: #333;
      font-weight: bold;
    }
    
    .cell {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .el-button + .el-button {
      margin-left: 0px;
    }
  }
}
.pass{
    background-color: pink;
    border:none;
    color: #000;

}
.noPass{
    background-color: cyan;
    border:none;
     color: #000;
}
</style>