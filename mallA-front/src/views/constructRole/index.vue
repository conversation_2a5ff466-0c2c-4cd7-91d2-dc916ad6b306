<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref } from 'vue'
import {useRouter} from 'vue-router'
const router = useRouter();
const handleToNormalManager = () => {
    router.push({
      path:'/roleList'
    })
}
const form = ref({
  name: '',
  phone: '',
  password: '',
  position: '',
  id:'',
  auth: '',
  loginOption: 'normal' 
})
const props = { multiple: true }

const options = [
    {
      value: 1,
      label: '公司'
    },
    {
      value: 2,
      label: '统计',
      children: [
          {
            value: 1,
            label: '数据'
          },
          {
            value: 2,
            label: '订单排行'
          },
          {
            value: 3,
            label: '商品链接'
          },
          {
            value: 4,
            label: '平台广告'
          },
          {
            value: 5,
            label: '店铺广告'
          },
          {
            value: 6,
            label: '开通权限'
          },
          {
            value: 7,
            label: '开通权限'
          },
          {
            value:8,
            label:'量化率'
          },
          {
            value:9,
            label:'平台促销券'
          },
          {
            value:10,
            label:'交易统计'
          },
          {
            value:11,
            label:'贷款统计'
          },
          {
            value:12,
            label:'数据'
          },
          {
            value:13,
            label:'补贴券'
          },
          {
            value:14,
            label:'退单排行'
          },
          {
            value:15,
            label:'交易数据'
          },
          {
            value:16,
            label:'抵扣金数据'
          }
      ]
  }
]
</script>

<template>
  <HomeBg>
    <div class="container">
      <img class="bg" src="../../images/bigBackground.png" alt="背景图">
      <div class="form-container">
        <!-- 每个输入框组 -->
        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.name" 
            placeholder="角色名称" 
          />
          <span class="reserved-text">显示保留</span>
        </div>
          
        <div class="input-group">
          <el-input 
            class="form-input" 
            v-model="form.password" 
            placeholder="备注"
            type="password"
            show-password
          />
          <span class="reserved-text">显示保留</span>
        </div>

        <!-- 负责授权特殊布局 -->
       <div class="input-group">
          <el-cascader size="large" placeholder="负责授权" :options="options" :props="props" clearable />
       </div>
             
        <!-- 保存按钮 -->
        <div class="save-button-container">
          <el-button class="save-button" @click="handleToNormalManager">保存</el-button>
        </div>
      </div>
    </div>
  </HomeBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  .form-container {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 1200px;

    .input-group {
      display: flex;
      align-items: center;
      margin-bottom: 61px;
      width: 100%;
      margin-left: 399px;

      .form-input {
        width: 805px;
        
        :deep(.el-input__wrapper) {
          height: 53px;
          font-size: 16px;
        }
      }

      .reserved-text {
        margin-left: 55px;
        color: #FF8D1A;
        font-size: 16px;
        white-space: nowrap;
      }
    }

    // 负责授权特殊布局
    
    // 单选框容器
    .radio-container {
      width: 100%;
      margin-top: 20px;
      margin-bottom: 40px;

      .radio-group {
        width: 805px;
        margin: 0 auto;
        
        :deep(.el-radio-group) {
          display: flex;
          justify-content: space-between;
          width: 100%;
        }

        .radio-item {
          display: flex;
          align-items: center;
          
          :deep(.el-radio) {
            margin-right: 8px;
            font-size: 16px;
            
            .el-radio__input {
              .el-radio__inner {
                border-radius: 2px; // 方形单选框
                width: 24px;
                height: 24px;
              }
            }
          }
        }
      }
    }

    // 保存按钮
    .save-button-container {
      width: 805px;
      margin: 0 auto;
      text-align: center;
      
      .save-button {
        width: 120px;
        height: 40px;
        background-color: #14097A;
        color: white;
        border: none;
        font-size: 16px;
        border-radius: 4px;
        
        &:hover {
          background-color: #1a0da0;
        }
        
        &:active {
          background-color: #0f0657;
        }
      }
    }
  }
}
</style>