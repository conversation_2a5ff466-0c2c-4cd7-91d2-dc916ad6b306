<template>
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>功能数据</h1>
      </div>

      <!-- 统计卡片 -->
      <el-card class="stats-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>数据统计</span>
          </div>
        </template>
        <div class="stats-row">
          <div class="stats-item">
            <div class="stats-title">今日总功能数值</div>
            <el-input class="stats-value" v-model="summaryData.todayTotalFunctionDatas" placeholder="0" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">今日总量化值</div>
            <el-input class="stats-value" v-model="summaryData.todayTotalQuantify" placeholder="0" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">今日总补贴金</div>
            <el-input class="stats-value" v-model="summaryData.todayTotalSubsidy" placeholder="0" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">今日总累计功能数值</div>
            <el-input class="stats-value" v-model="summaryData.todayAllTotalFunctionDatas" placeholder="0" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">今日总累计量化值</div>
            <el-input class="stats-value" v-model="summaryData.todayAllTotalQuantify" placeholder="0" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">今日总累计补贴金</div>
            <el-input class="stats-value" v-model="summaryData.todayAllTotalSubsidy" placeholder="0" disabled />
          </div>
        </div>
      </el-card>

      <!-- 数据查询与列表 -->
      <el-card class="table-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>数据查询与列表</span>
          </div>
        </template>

        <!-- 搜索表单 -->
        <div class="search-form">
          <div class="form-row">
            <div class="form-item">
              <span class="form-label">手机号</span>
              <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable>
                <template #prefix>
                  <el-icon><Phone /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="form-item">
              <span class="form-label">查询日期</span>
              <el-date-picker v-model="searchForm.startDate" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </div>
            <!--<div class="form-item">
              <span class="form-label">结束日期</span>
              <el-date-picker v-model="searchForm.endDate" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </div> -->
          </div>
          <div class="form-actions">
            <el-button type="primary" @click="handleSearch"><el-icon><Search /></el-icon>搜索</el-button>
            <el-button @click="resetSearch"><el-icon><Refresh /></el-icon>重置</el-button>
            <el-button type="success" @click="exportToExcel"><el-icon><Download /></el-icon>导出</el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table :data="tableData" stripe border style="width: 100%" v-loading="loading">
          <el-table-column prop="updateDate" label="日期" />
          <el-table-column prop="phone" label="手机号" />
          <el-table-column prop="value" label="功能数值" />
          <el-table-column prop="quantifyValue" label="量化值" />
          <el-table-column prop="subsidyFunds" label="补贴金" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Search, Refresh, Download, Phone } from '@element-plus/icons-vue';
import request from '../../utils/request';

// 数据状态
const loading = ref(false);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);

// 统计数据
const summaryData = reactive({
  todayTotalFunctionDatas: "0",
  todayTotalQuantify: "0",
  todayTotalSubsidy: "0",
  todayAllTotalFunctionDatas: "0",
  todayAllTotalQuantify: "0",
  todayAllTotalSubsidy: "0"
});

// 搜索表单
const searchForm = reactive({
  phone: "",
  startDate: "",
  endDate: ""
});

// 查询功能数据
const queryFunctionDatas = async () => {
  loading.value = true;
  try {
    const params = {
      phone: searchForm.phone,
      startDate: searchForm.startDate,
      endDate: searchForm.endDate,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    const response = await request({
      url: '/mall-project/api/queryFunctionDatas',
      method: 'post',
      data: params
    });

    // response 直接就是业务数据
    const data = response.data;
    tableData.value = data.list || [];
    total.value = data.total || 0;

    // 更新统计数据
    if (data.summary) {
      summaryData.todayTotalFunctionDatas = data.summary.todayTotalFunctionDatas || "0";
      summaryData.todayTotalQuantify = data.summary.todayTotalQuantify || "0";
      summaryData.todayTotalSubsidy = data.summary.todayTotalSubsidy || "0";
      summaryData.todayAllTotalFunctionDatas = data.summary.todayAllTotalFunctionDatas || "0";
      summaryData.todayAllTotalQuantify = data.summary.todayAllTotalQuantify || "0";
      summaryData.todayAllTotalSubsidy = data.summary.todayAllTotalSubsidy || "0";
    }
  } catch (error: any) {
    console.error('查询功能数据失败:', error);
    // 当code为500且message为"暂无数据"时，清空表格数据但不显示错误提示
    if (error.code === 500 && error.message === '暂无数据') {
      tableData.value = [];
      total.value = 0;
      // 重置统计数据
      summaryData.todayTotalFunctionDatas = "0";
      summaryData.todayTotalQuantify = "0";
      summaryData.todayTotalSubsidy = "0";
      summaryData.todayAllTotalFunctionDatas = "0";
      summaryData.todayAllTotalQuantify = "0";
      summaryData.todayAllTotalSubsidy = "0";
    } else {
      ElMessage.error(error.message || '查询失败，请稍后重试');
    }
  } finally {
    loading.value = false;
  }
};

// 搜索功能
const handleSearch = () => {
  currentPage.value = 1;
  queryFunctionDatas();
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.phone = "";
  searchForm.startDate = "";
  searchForm.endDate = "";
  currentPage.value = 1;
  queryFunctionDatas();
};

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
  queryFunctionDatas();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  queryFunctionDatas();
};

// 导出Excel
const exportToExcel = async () => {
  try {
    const params = {
      startDate: searchForm.startDate || "",
      endDate: searchForm.endDate || "",
      phone: searchForm.phone
    };

    const response = await request({
      url: '/mall-project/api/exportFunctionDatasExcel',
      method: 'post',
      data: params,
      responseType: 'blob'
    });

    // 创建blob对象并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `功能数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error("导出数据失败:", error);
    // 使用后台返回的错误信息
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("导出失败，请稍后重试");
    }
  }
};

// 页面加载时查询数据
onMounted(() => {
  queryFunctionDatas();
});
</script>
<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  height: 24px;

  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border-radius: 8px;
  margin-bottom: 24px;
  border: none;

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-card__body {
    padding: 24px;
  }
}

.stats-card {
  .stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
  }

  .stats-item {
    flex: 0 0 calc(33.333% - 16px);
    width: calc(33.333% - 16px);
    display: flex;
    flex-direction: column;
    align-items: center;

    .stats-title {
      font-size: 14px;
      color: #595959;
      margin-bottom: 12px;
    }

    .stats-value {
      width: 100%;
    }
  }
}

.table-card {
  .search-form {
    margin-bottom: 24px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      gap: 24px;
    }

    .form-item {
      display: flex;
      align-items: center;

      .form-label {
        width: 80px;
        font-size: 14px;
        color: #262626;
        margin-right: 12px;
      }

      .el-input, .el-date-picker {
        width: 240px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 16px;

      .el-button .el-icon {
        margin-right: 4px;
      }
    }
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;

    th.el-table__cell {
      background-color: #fafafa;
      color: #262626;
      font-weight: 500;
    }

    .cell {
      padding: 12px 16px;
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-start;
}

:deep(.el-input .el-input__prefix) {
  color: #a0a0a0;
}
</style>