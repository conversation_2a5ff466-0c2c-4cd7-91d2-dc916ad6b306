<template>
  <div class="page-container">
    <div class="content-wrapper">

      <!-- 统计卡片 -->
      <el-card class="stats-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>核销统计</span>
          </div>
        </template>
        <div class="stats-row">
          <div class="stats-item">
            <div class="stats-title">今日核销补贴金</div>
            <el-input class="stats-value" v-model="todayWriteOff" placeholder="0.00" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">累计已核销补贴金</div>
            <el-input class="stats-value" v-model="totalWriteOff" placeholder="0.00" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">累计未核销</div>
            <el-input class="stats-value" v-model="totalUnWriteOff" placeholder="0.00" disabled />
          </div>
        </div>
        <div class="stats-row">
          <div class="stats-item">
            <div class="stats-title">今日已核销促销金</div>
            <el-input class="stats-value" v-model="todayPromotionUsed" placeholder="0.00" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">总累计使用金额</div>
            <el-input class="stats-value" v-model="totalPromotionUsed" placeholder="0.00" disabled />
          </div>
          <div class="stats-item">
            <div class="stats-title">累计核销金</div>
            <el-input class="stats-value" v-model="totalWriteOffGold" placeholder="0.00" disabled />
          </div>
        </div>
      </el-card>

      <!-- 数据查询与列表 -->
      <el-card class="table-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>数据查询与列表</span>
          </div>
        </template>
        
        <!-- 搜索表单 -->
        <div class="search-form">
          <div class="form-row">
            <div class="form-item">
              <span class="form-label">手机号</span>
              <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable>
                <template #prefix>
                  <el-icon><Phone /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="form-item">
              <span class="form-label">查询日期</span>
              <el-date-picker v-model="searchForm.startDate" type="date" placeholder="查询日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </div>
            <!--<div class="form-item">
              <span class="form-label">结束日期</span>
              <el-date-picker v-model="searchForm.endDate" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </div> -->
            <div class="form-item" style="width: 300px;">
              <span class="form-label" style="width: 150px;">排除等于0字段</span>
              <!--下拉选择-->
              <el-select v-model="isGreaterThanZero" placeholder="请选择">
                <el-option label="今日核销补贴金" value="1" />
                <el-option label="累计核销补贴金" value="2" />
                <el-option label="未核销" value="3" />
                <el-option label="已核销促销金" value="4" />
                <el-option label="累计使用金额" value="5" />
                <el-option label="核销值" value="6" />
              </el-select>
            </div>
          </div>
          <div class="form-actions">
            <el-button type="primary" @click="fetchData"><el-icon><Search /></el-icon>搜索</el-button>
            <el-button @click="resetSearch"><el-icon><Refresh /></el-icon>重置</el-button>
            <el-button type="success" @click="exportToExcel"><el-icon><Download /></el-icon>导出</el-button>
          </div>
        </div>
        
        <!-- 数据表格 -->
        <el-table :data="tableData" stripe border style="width: 100%" v-loading="loading">
          <el-table-column prop="updateDate" label="日期" />
          <el-table-column prop="phone" label="手机号" />
          <el-table-column prop="writeOffSubsidy" label="今日核销补贴金" />
          <el-table-column prop="writeOffSubsidyTotal" label="累计核销补贴金" />
          <el-table-column prop="unWriteOffSubsidy" label="未核销" />
          <el-table-column prop="promotionUsed" label="已核销促销金" />
          <el-table-column prop="totalPromotionUsed" label="累计使用金额" />
          <el-table-column prop="writeOffGold" label="核销值" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Search, Refresh, Download, Phone } from '@element-plus/icons-vue';
import { queryWriteOffData } from "../../api/writeData";
import { Session } from "../../utils/storage";
import request from "../../utils/request";

// 统计数据
const todayWriteOff = ref("0.00");
const totalWriteOff = ref("0.00");
const totalUnWriteOff = ref("0.00");
const todayPromotionUsed = ref("0.00");
const totalPromotionUsed = ref("0.00");
const totalWriteOffGold = ref("0.00");

// 搜索表单
const searchForm = reactive({
  phone: "",
  startDate: "",
  endDate: ""
});

// 排除等于0字段的下拉选择
const isGreaterThanZero = ref("");

// 加载状态
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 5,
  total: 0
});

// 获取核销数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      phone: searchForm.phone,
      startDate: searchForm.startDate || "",
      endDate: searchForm.endDate || "",
      isGreaterThanZero: isGreaterThanZero.value || undefined, // 排除等于0字段参数
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    const res = await queryWriteOffData(params);

    // res.data直接就是业务数据
    const data = res.data;
    if (data && data.list) {
      tableData.value = data.list;
      pagination.total = data.total;

      // 同时更新统计数据，因为新接口同时返回统计信息
      if (data.summary) {
        const summary = data.summary;
        todayWriteOff.value = summary.todayTotalWriteOffSubsidy || "0.00";     // 今日核销补贴金
        totalWriteOff.value = summary.totalWriteOffSubsidy || "0.00";          // 累计已核销补贴金
        totalUnWriteOff.value = summary.totalUnWriteOffSubsidy || "0.00";      // 累计未核销
        todayPromotionUsed.value = summary.todayPromotionUsed || "0.00";  // 今日已核销促销金
        totalPromotionUsed.value = summary.totalPromotionUsed || "0.00";       // 总累计使用金额
        totalWriteOffGold.value = summary.totalWriteOffGold || "0.00";        // 累计核销金
      }
    } else {
      ElMessage.error("查询失败");
    }
  } catch (error) {
    console.error("获取核销数据失败:", error);
    // 使用后台返回的错误信息
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("获取数据失败，请稍后重试");
    }
  } finally {
    loading.value = false;
  }
};

// 分页大小变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;
  fetchData();
};

// 当前页码变化
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  fetchData();
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.phone = "";
  searchForm.startDate = "";
  searchForm.endDate = "";
  isGreaterThanZero.value = ""; // 重置排除等于0字段选择
  pagination.pageNum = 1;
  fetchData();
};

// 导出数据
const exportToExcel = async () => {
  try {
    const params = {
      phone: searchForm.phone,
      startDate: searchForm.startDate || "",
      endDate: searchForm.endDate || ""
    };

    const response = await request({
      url: '/mall-project/api/exportWriteOffDataExcel',
      method: 'post',
      data: params,
      responseType: 'blob'
    });

    // 创建blob对象并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `核销数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    ElMessage.success('导出成功');
  } catch (error: any) {
    console.error("导出数据失败:", error);
    ElMessage.error(error?.response?.data?.message || error?.message || "导出失败");
  }
};

// 组件挂载时获取数据
onMounted(() => {
  // 获取数据和统计数据
  fetchData();
});


</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  height: 24px;
  
  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border-radius: 8px;
  margin-bottom: 24px;
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-card__body {
    padding: 24px;
  }
}

.stats-card {
  .stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
  }
  
  .stats-item {
    flex: 1;
    min-width: 240px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .stats-title {
      font-size: 14px;
      color: #595959;
      margin-bottom: 12px;
    }
    
    .stats-value {
      width: 100%;
    }
  }
}

.table-card {
  .search-form {
    margin-bottom: 24px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      gap: 24px;
    }
    
    .form-item {
      display: flex;
      align-items: center;
      
      .form-label {
        width: 80px;
        font-size: 14px;
        color: #262626;
        margin-right: 12px;
      }
      
      .el-input, .el-date-picker {
        width: 240px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 16px;
      
      .el-button .el-icon {
        margin-right: 4px;
      }
    }
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    
    th.el-table__cell {
      background-color: #fafafa;
      color: #262626;
      font-weight: 500;
    }
    
    .cell {
      padding: 12px 16px;
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-start;
}

:deep(.el-input .el-input__prefix) {
  color: #a0a0a0;
}
</style>