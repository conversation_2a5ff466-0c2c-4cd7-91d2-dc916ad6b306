<script setup>
import ManageBg from '../../components/ManageBg.vue'
import {useRouter} from 'vue-router'
const router = useRouter()
const handleToShippingDetail = () =>{
    router.push('/shippingDetail')
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="shipment-form">
        <h1 class="title">发货</h1>
        <div class="form-group">
          <input 
            class="input-field" 
            type="text" 
            placeholder="输入快递公司名称"
          >
        </div>
        <div class="form-group">
          <input 
            class="input-field" 
            type="text" 
            placeholder="输入快递单号"
          >
        </div>
        <button @click="handleToShippingDetail" class="submit-button">提交发货</button>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(
    to bottom,
    rgb(42, 145, 219) 0%,
    rgba(42, 145, 219, 0.7) 50%,
    rgba(42, 145, 219, 0.3) 100%
  );
}

.shipment-form {
  width: 100%;
  max-width: 1100px;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
 
 
 
  
  .title {
    font-size: 48px;
    color: #000;
    margin-bottom: 60px;
    font-weight: 600;
    
  }
  
  .form-group {
    width: 100%;
    margin-bottom: 40px;
    background-color: #fff;
    .input-field {
      width: 100%;
      height: 70px;
      padding: 0 25px;
      font-size: 24px;
      
      border-radius: 8px;
      transition: border-color 0.3s;
      
      &:focus {
        border-color: #14097A;
        outline: none;
      }
      
      &::placeholder {
        color: #aaa;
      }
    }
  }
  
  .submit-button {
    width: 465px;
    height: 80px;
    background-color: #14097A;
    color: white;
    font-size: 28px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #0d0657;
    }
    
    &:active {
      transform: translateY(2px);
    }
  }
}
</style>