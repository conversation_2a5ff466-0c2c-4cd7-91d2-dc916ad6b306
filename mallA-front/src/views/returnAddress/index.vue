<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import {ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
}

const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址', '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]

// 表格数据
const tableData = ref([
  {
    account: '*************',
    address: '广东省深圳市南山区科技园',
    receiver: '张先生',
    phone: '***********',
  },
  {
    account: '*************',
    address: '北京市朝阳区建国路',
    receiver: '李女士',
    phone: '***********',
  }
])
const handleToAddReturnAddress = ()=>{
  router.push('/addReturnAddress')
}
const handleDelete = (index) => {
  tableData.value.splice(index, 1)
}

const handleEdit = (index) => {
  
  console.log('编辑第', index, '条数据')
}
</script>

<template>
    <ManageBg>
        <div class="container">
            <div class="left-buttons">
                <el-button 
                    v-for="(item, index) in buttonList" 
                    :key="index"
                    class="data-button"
                    @click="handleButtonClick(item)"
                >
                    {{ item }}
                </el-button>
            </div>
            <div class="content-area">
                <div class="header">
                    <span class="title">退货地址</span>
                    <el-button @click="handleToAddReturnAddress" type="primary" :icon="Plus" circle />
                </div>
                <div class="main">
                    <div class="table-container">
                        <div class="table-title">退货地址列表</div>
                        <el-table :data="tableData" style="width: 100%">
                            <el-table-column prop="account" label="账号" width="200" />
                            <el-table-column prop="address" label="地址" width="400" />
                            <el-table-column prop="receiver" label="收件人" width="150" />
                            <el-table-column prop="phone" label="电话" width="200" />
                            <el-table-column label="操作" width="200">
                                <template #default="scope">
                                    <el-button
                                        size="small"
                                        type="danger"
                                        @click="handleDelete(scope.$index)"
                                    >删除</el-button>
                                    <el-button
                                        size="small"
                                        @click="handleEdit(scope.$index)"
                                    >返回修改</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </ManageBg>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.content-area {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    align-items: center;
    margin-left: 1180px;
    margin-bottom: 20px;
    
    .title {
        font-size: 24px;
        font-weight: bold;
        margin-right: 15px;
    }
    
    .el-button {
        width: 36px;
        height: 36px;
    }
}

.main {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.table-container {
    width: 100%;
    height: 100%;
    
    .table-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #3A58CF;
        color: white;
        border-radius: 4px;
    }
}

:deep(.el-table) {
    th {
        background-color: #f5f7fa !important;
        color: #333;
        font-weight: bold;
    }
    
    .el-button {
        margin-right: 10px;
        &:last-child {
            margin-right: 0;
        }
    }
}
</style>