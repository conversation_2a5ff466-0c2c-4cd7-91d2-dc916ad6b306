<template>
  <MerchantBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>
      
      <div class="right-content">
        <div class="filter-section">
          <div class="title">
            <span>订单筛选</span>
          </div>
          
          <div class="filter-form">
            <el-form :model="filterForm">
              <div class="form-row">
              <el-input 
                v-model="filterForm.userPhone"
                placeholder="用户手机号"
                class="filter-input"
              />
              <el-input 
                v-model="filterForm.sender"
                placeholder="发件人"
                class="filter-input"
              />
              <el-input 
                v-model="filterForm.receiverPhone"
                placeholder="收件手机号"
                class="filter-input"
              />
            </div>
            
            <div class="form-row">
              <el-input 
                v-model="filterForm.shippingAddress"
                placeholder="发货地"
                class="filter-input"
              />
              <el-input 
                v-model="filterForm.trackingNumber"
                placeholder="快递号"
                class="filter-input"
              />
              <el-input 
                v-model="filterForm.productName"
                placeholder="商品名称"
                class="filter-input"
              />
            </div>
            
            <div class="form-row">
              <el-input 
                v-model="filterForm.orderNumber"
                placeholder="订单号"
                class="filter-input"
              />
              <el-date-picker
                v-model="filterForm.shippingTime"
                type="datetime"
                placeholder="发货时间"
                class="filter-input"
              />
              <el-button 
                type="primary" 
                class="search-btn"
              >
                查询
              </el-button>
            </div>
            </el-form>
            
          </div>
        </div>
        
        <el-table :data="tableData" class="order-table">
          <el-table-column 
            prop="orderNumber" 
            label="手机号" 
            width="180"
            header-class-name="table-header"
          />
          <el-table-column 
            prop="shippingAddress" 
            label="发货地" 
            width="150"
            header-class-name="table-header"
          />
          <el-table-column 
            prop="orderNumber" 
            label="订单号" 
            width="200"
            header-class-name="table-header"
          />
          <el-table-column 
            prop="receiver" 
            label="收件人" 
            width="200"
            header-class-name="table-header"
          />
          <el-table-column 
            prop="trackingNumber" 
            label="快递号" 
            width="200"
            header-class-name="table-header"
          />
          <el-table-column 
            prop="shippingTime" 
            label="发货时间" 
            width="200"
            header-class-name="table-header"
          />
          <el-table-column 
            prop="receiverPhone" 
            label="收件手机号" 
            width="200"
            header-class-name="table-header"
          />
          
         
        </el-table>
      </div>
    </div>
  </MerchantBg>
</template>
<script setup>
import MerchantBg from '../../components/MerchantBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '全部订单') router.push('/merchantOrder')
  if(item === '待支付订单') router.push('/merchantNotPayOrders')
}

const buttonList = [
  '全部订单', '待支付订单'
]

// 筛选表单数据
const filterForm = ref({
  userPhone: '',
  sender: '',
  receiverPhone: '',
  shippingAddress: '',
  trackingNumber: '',
  productName: '',
  orderNumber: '',
  shippingTime: ''
})

// 表格数据
const tableData = ref([
  {
    orderNumber: '20230001',
    shippingAddress: '广东省广州市',
    receiver: '张三',
    orderNumber:'13213124142',
    trackingNumber: 'SF123456789',
    shippingTime: '2023-05-10 14:30',
    receiverPhone: '138****1234',
    productName: '智能手机 X1',
    status: '已发货'
  },
  {
    orderNumber: '20232321',
    shippingAddress: '广东省深圳市',
    receiver: '李四',
    orderNumber:'142555774125',
    trackingNumber: 'SF12313213789',
    shippingTime: '2025-15-10 14:30',
    receiverPhone: '138****1411',
  }
])
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.right-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.filter-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.title {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.filter-form {
  .form-row {
    display: flex;
    margin-bottom: 15px;
    gap: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.filter-input {
  flex: 1;
}

.search-btn {
  width: 120px;
}

.order-table {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  
  :deep(.el-table__header) {
    .table-header {
      background-color: #3A58CF;
      color: white;
    }
  }
  
  :deep(.el-table__cell) {
    padding: 12px 0;
  }
}
</style>