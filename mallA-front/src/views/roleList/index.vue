<template>
  <HomeBg>
    <div class="role-management">
      <div class="background-container">
        <img
          class="background-image"
          src="../../images/bigBackground.png"
          alt="背景图"
        />
        <h1>平台管理</h1>
        <div class="main-content">
          <!-- 搜索和操作栏 -->
          <div class="action-bar">
            <el-input
              v-model="searchQuery"
              placeholder="输入手机号/姓名搜索"
              clearable
              style="width: 300px; margin-right: 20px"
              @clear="handleSearchClear"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="showCreateDialog">
              创建职员
            </el-button>
          </div>

          <!-- 创建角色对话框 -->
          <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑职员' : '创建职员'" width="500px" :destroy-on-close="true">
            <el-form
              :model="roleForm"
              :rules="rules"
              ref="roleFormRef"
              label-width="80px"
            >
              <el-form-item label="职员名" prop="name">
                <el-input
                  v-model="roleForm.employeeName"
                  placeholder="请输入职员名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="roleForm.phone"
                  placeholder="请输入手机号"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="!isEdit" label="密码" prop="password">
                <el-input
                  v-model="roleForm.password"
                  type="password"
                  placeholder="请输入密码"
                ></el-input>
              </el-form-item>
              <!-- 或者 -->
              <el-form-item v-else label="密码" prop="password">
                <el-input
                  v-model="roleForm.password"
                  type="password"
                  placeholder="如需更改密码，请输入新密码"
                
                ></el-input>
</el-form-item>
              <!-- <el-form-item label="工号" prop="id">
                <el-input
                  v-model="roleForm.id"
                  type="password"
                  placeholder="请输入工号"
                ></el-input>
              </el-form-item> -->
              <el-form-item label="岗位" prop="position">
                  <el-select v-model="roleForm.positionId" placeholder="请选择岗位">
                    <el-option
                          v-for="item in positions"
                          :key="item.id"
                          :label="item.position_name"
                          :value="item.id"
                          
                        />
                  </el-select>
              </el-form-item>
              <div class="m-4">
                <el-form-item label="负责授权" prop="modulesIds">
                <el-cascader
                  v-model="roleForm.modulesIds"
                  :options="modulesOptions"
                  :props="{ multiple: true, emitPath:true }"
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
              </div>
             
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitRoleForm" 
                  >确定</el-button
                >
              </span>
            </template>
          </el-dialog>
          <!-- 角色卡片列表 -->
        <!-- 角色列表 -->
        <div class="role-cards">
            <el-card
              v-for="(role,index) in filteredRoles"
              :key="role.phone"
              class="role-card"
              shadow="hover"
            >
              <template #header>
                <div class="card-header">
                  <span>名字：{{ role.employee_name }}</span>
                  <div class="card-actions">
                    <el-button
                      size="small"
                      type="text"
                      @click="handleEdit(role)"
                      >编辑</el-button
                    >
                    <el-button
                      size="small"
                      type="text"
                      @click="handleDelete(role.id,index)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </template>
              <div class="card-content">
                <div class="info-item">
                  <span class="label">手机号：</span>
                  <span class="value">{{ role.phone }}</span>
                </div>
                  <div class="info-item">
                  <span class="label">工号</span>
                  <span class="value">{{ role.employee_no }}</span>
                </div>
                <div class="info-item">
                  <span class="label">岗位：</span>
                  <!-- <span class="value" v-for="(item,index) in positions" :key="index">{{
                    item.position_name
                  }}</span> -->
                  <span class="value">{{ role.position_name }}</span>
                    <!-- <el-select v-model="selectedPosition" placeholder="请选择岗位">
                    <el-option
                          v-for="item in positions"
                          :key="item.id"
                          :label="item.position_name"
                          :value="item.id"
                        />
                  </el-select> -->
                </div>
                <div class="info-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ role.create_time }}</span>
                </div>
                 
              </div>

<!--                  <span>限制登入</span> -->
<!--                  -->
<!--                  <input type="checkbox" @click="handleLimit( )" />-->
<!--                  <el-button text @click="open(index)">点开</el-button>-->

                <div class="card-bottomm">
                  <span>限制登入</span>
                  <el-switch
                      v-model="role.login_limit"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      @change="handleLimit(role)"
                  />
                  <el-button text @click="open(index)">点开</el-button>
                </div>

            </el-card>
          </div>  

        </div>
      </div>
      <!-- 空状态提示 -->
      <el-empty v-if="filteredRoles.length === 0" description="暂无角色数据" />
    </div>
  </HomeBg>
</template>

<script setup>
import HomeBg from "../../components/HomeBg.vue";
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import {
  createRole,
  getPositions,
  getModules,
  searchByNameOrPhone,
  userDelect,
  userUpdate,
  userEdit,
  userEditModules,
  userLimit
} from "@/api/roleList";


// 搜索查询条件
const searchQuery = ref("");
// 搜索查询条件
const t = ref("");
const isEdit = ref(false);
const currentEditId = ref(null);
const total = ref(100); // 总条数
// 切换页码
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
  // getsearchByNameOrPhone(); // 重新搜索
};

// 切换每页条数
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 回到第一页
  // getsearchByNameOrPhone();
};
const handelLimit=()=>{
  console.log('111');
  
}
//获取后端数据

// 岗位选项
const positionOptions = [
  { value: "admin", label: "管理员" },
  { value: "developer", label: "开发人员" },
  { value: "operator", label: "运营人员" },
  { value: "tester", label: "测试人员" },
];
const open = (index) => {
  console.log(filteredRoles.value[index]);
  t.value = filteredRoles.value[index];
};
// 获取岗位标签
const getPositionLabel = (value) => {
  const position = positionOptions.find((item) => item.value === value);
  return position ? position.label : "未知岗位";
};
//授权
const props = { multiple: true };

const options = ref([]);
// 角色表单数据
const roleForm = reactive({
  name: "",
  phone: "",
  password: "",
  id: "",
  menus: "",
  position: "",
  employeeId: "",
  modulesIds: [],
  employeeName: "",
});

// 表单验证规则
const rules = ref({
  password: [
    {
      required: false, // 默认不强制
      message: "请输入密码",
      trigger: "blur",
    },
    {
      min: 6,
      max: 20,
      message: "密码长度在6到20个字符",
      trigger: "blur",
    },
  ],
  employeeName: [{ required: true, message: "请输入职员名", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" },
  ],
});
const updatePasswordValidation = () => {
  const isRequired = !isEdit.value; // 创建时必填，编辑时非必填
  rules.value.password[0].required = isRequired;
  rules.value.password[0].message = isRequired ? "密码不能为空" : "";
};
// 对话框显示状态
const dialogVisible = ref(false);
const roleFormRef = ref(null);
const positions = ref([]);
const modules = ref([]);
// 角色列表数据
const roleList = ref([]);

const filteredRoles = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();

  if (!query) {
    return roleList.value;
  }

  return roleList.value.filter((role) => {
    const name = role.employee_name?.toLowerCase() || '';
    const phone = role.phone?.toLowerCase() || '';

    return name.includes(query) || phone.includes(query);
  });
})
//获取所有员工信息
const getsearchByNameOrPhone = async (data) => {
  console.log(data);


  try {
    const res = await searchByNameOrPhone(data);
    console.log("接口返回:", res);

    if (res.code === 200 && Array.isArray(res.data)) {
      roleList.value = res.data;
    } else if(res.code === 403) {
      alert("已被登录，请重新登录")
      router.push('/');
    }else{
      roleList.value = [];
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};
//获取所有职位
const getPositionsAll = async () => {
  const res = await getPositions();
  positions.value = res.data.positions;
};
function buildTree(data, parentId) {
  return data
    .filter((item) => item.parent_id == parentId)
    .map((item) => ({
      value: String(item.id), // 强制转成字符串
      label: item.module_name,
      children: buildTree(data, item.id),
    }));
}
//获取模块
const modulesOptions = ref([]);
const getModulesAll = async () => {
  try {
    const res = await getModules();
    const modulesData = res.data?.modules;
    // console.log("modulesData",modulesData)
    console.log(res.data.modules[0].id);

    if (Array.isArray(modulesData)) {
      modulesOptions.value = buildTree(modulesData, 0);
      // console.log("options.value",options.value)
    } else {
      console.error("res.data.modules 不是数组", modulesData);
      modulesOptions.value = []; // 防止后续出错
    }
  } catch (err) {
    console.error("获取模块失败:", err);
  }
};
// 清空搜索
const handleSearchClear = () => {
  searchQuery.value = "";
};

// 显示创建对话框
const showCreateDialog = () => {
  isEdit.value = false;
  resetRoleForm(); // 先重置表单
  // roleForm.value = { ...defaultRoleForm };
  updatePasswordValidation(); // 更新密码规则
  dialogVisible.value = true;
  console.log(
    "resetRoleForm -> roleForm:",
    JSON.parse(JSON.stringify(roleForm))
  );

  if (roleFormRef.value) roleFormRef.value.resetFields();
};

// 重置表单
const resetRoleForm = () => {
  Object.assign(roleForm, {
    ...defaultRoleForm,
    modulesIds: [...defaultRoleForm.modulesIds], // 确保数组是新引用
  });
};
const defaultRoleForm = {
  employeeName: "",
  phone: "",
  password: "",
  positionId: "",
  modulesIds: [],
  employeeId: "",
};
// 定义去重函数
function getUniqueModuleIds(modulesIds) {
  if (!modulesIds) return [];

  let array;

  if (typeof modulesIds === "string") {
    array = modulesIds
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean);
  } else if (Array.isArray(modulesIds)) {
    array = modulesIds.map((s) => String(s).trim()).filter(Boolean);
  } else {
    console.warn("不支持的 modulesIds 类型:", modulesIds);
    return [];
  }

  return [...new Set(array)];
}
const submitRoleForm = async () => {
  try {
    console.log(roleForm.modulesIds,"roleForm.modulesIds")
    const set = new Set;
    // ✅ 正确去重：使用 new Set 去除重复模块 ID
    for (let i = 0; i < roleForm.modulesIds.length; i++) {
      let t = roleForm.modulesIds[i]
      if(Array.isArray(t)){
        for (let j = 0; j < roleForm.modulesIds[i].length; j++) {
          set.add(roleForm.modulesIds[i][j]);
        }
      }else{
        set.add(roleForm.modulesIds[i]);
      }
    }
    const uniqueModuleIds = [...set]
    console.log("uniqueModuleIds",uniqueModuleIds)

    // 构建 payload
    const payload = {
      employeeId: roleForm.employeeId,
      employeeName: roleForm.employeeName,
      phone: roleForm.phone,
      positionId: roleForm.positionId,
      modulesIds: uniqueModuleIds.join(","), // ✅ 使用去重后的数组生成字符串
      password: roleForm.password?.trim() || undefined,
    };

    console.log("提交给后端的权限ID:", payload.modulesIds);

    let res;
    if (isEdit.value) {
      res = await userUpdate(payload); // 编辑更新
    } else {
      res = await createRole(payload); // 新增创建
    }

    if (res.code === 200) {
      dialogVisible.value = false;
      ElMessage.success(isEdit.value ? "更新成功" : "创建成功");

      // 刷新用户列表
      await getsearchByNameOrPhone({});
      resetRoleForm(); // 可选：重置表单
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  } catch (error) {
    console.error("请求出错:", error);
    ElMessage.error("提交失败，请检查输入或网络");
  }
};
function isLeafNode(id, options) {
  const find = (nodes) => {
    for (const node of nodes) {
      if (String(node.value) === String(id)) {
        return !node.children || node.children.length === 0;
      }
      if (node.children && node.children.length > 0) {
        const result = find(node.children);
        if (result !== undefined) return result;
      }
    }
    return false;
  };
  return find(options);
}
//编辑获取模块信息
const getUserEditModules = async (id) => {
  try {
    const res = await userEditModules(id);
    if (res.code === 200 && Array.isArray(res.data?.modules)) {
      const selectedModules = res.data.modules;
      modulesOptions.value = buildMinimalTree(selectedModules);
    } else {
      modulesOptions.value = [];
    }
    return res;
  } catch (error) {
    console.error("获取模块失败:", error);
    return { code: 500, data: null };
  }
};
const handleEdit = async (row) => {
  isEdit.value = true;
  currentEditId.value = row.id;

  try {
    // 第一步：获取用户基本信息
    const res = await userEdit(row.id);
    if (res.code !== 200) {
      ElMessage.error("获取用户信息失败");
      return;
    }

    roleForm.employeeId = res.data.id;
    roleForm.employeeName = res.data.employee_name || "";
    roleForm.phone = res.data.phone || "";
    roleForm.positionId = Number(res.data.position_id) || "";
    roleForm.password = "";

    // 第二步：先加载 modulesOptions（全局模块树）
    if (!modulesOptions.value.length) {
      await getUserEditModules(); // 确保 modulesOptions 已加载
    }

    // 第三步：获取该用户的权限模块
    const moduleRes = await userEditModules(row.id); // 返回的是 { total: 20, modules: [...] }
    console.log(moduleRes);
    
    if (moduleRes.code === 200 && Array.isArray(moduleRes.data?.modules)) {
      roleForm.modulesIds = moduleRes.data.modules.map((item) => String(item.id));
      console.log('赋值后的 modulesIds:', roleForm.modulesIds);
    } else {
      roleForm.modulesIds = [];
    }
    // console.log('当前 cascader options:', modulesOptions.value);
    // console.log('当前 cascader value:', roleForm.modulesIds);
    // console.log('第一个选项',modulesOptions.value[0])
    dialogVisible.value = true;
    updatePasswordValidation();

  } catch (error) {
    console.error("获取用户详情失败:", error);
    ElMessage.error(`获取用户失败：${error.message}`);
  }
};
//限制登入
const handleLimit = async (role) => {
  console.log('当前 role:', role);
  const loginLimit = role.login_limit ? 1 : 0;

  // 假设你的后端需要 employeeId 和 positionId
  const { id: employeeId } = role || {};

  if (!employeeId) {
    ElMessage.warning('无法操作：employeeId 不存在');
    return;
  }

  try {
    const res = await userLimit(employeeId, loginLimit);
    ElMessage.success(res.message);

  } catch (error) {
    ElMessage.error('权限更新失败');
    console.error('请求失败:', error);
  }
};
// 删除角色
const handleDelete = async (employeeId, index) => {
  // console.log("传给后端的 employeeId:", employeeId);
  // 确认删除
  try {
    await ElMessageBox.confirm("确认要删除该职员吗？", "提示", {
      type: "warning",
    });
    const res = await userDelect(employeeId);
    // console.log(res);
    if (res.code === 200) {

      ElMessage.success("删除成功");
      roleList.value.splice(index, 1);
    } else {
      ElMessage.error(res.message || "删除失败");
    }
  } catch (err) {
    console.log("删除失败：", err);
  }
};
onMounted(() => {
  getPositionsAll();
  getModulesAll();
  getsearchByNameOrPhone({});

});
</script>

<style scoped>
.role-management {
  padding: 20px;
}
h1 {
  font-size: 30px;
  font-weight: 300px;
  color: #000;
}
.action-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}
.main-content {
  position: relative;
  z-index: 2; /* 确保内容在背景图上方 */
  padding: 20px 0;
}
.role-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.role-card {
  transition: all 0.3s;
}

.role-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #888;
  min-width: 80px;
}

.value {
  flex: 1;
  word-break: break-all;
}
.dirty {
  margin-left: 100px;
}
</style>