<template>
  <div class="role-management">
    <!-- 搜索和操作栏 -->
    <div class="action-bar">
      <el-input
        v-model="searchQuery"
        placeholder="输入手机号搜索"
        clearable
        style="width: 300px; margin-right: 20px"
        @clear="handleSearchClear"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="showCreateDialog">创建角色</el-button>
    </div>

    <!-- 创建角色对话框 -->
    <el-dialog v-model="dialogVisible" title="创建新角色" width="500px">
      <el-form :model="roleForm" :rules="rules" ref="roleFormRef" label-width="80px">
        <el-form-item label="角色名" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="roleForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="roleForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="岗位" prop="position">
          <el-select v-model="roleForm.position" placeholder="请选择岗位" style="width: 100%">
            <el-option
              v-for="item in positionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRoleForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 角色卡片列表 -->
    <div class="role-cards">
      <el-card
        v-for="role in filteredRoles"
        :key="role.phone"
        class="role-card"
        shadow="hover"
      >
        <template #header>
          <div class="card-header">
            <span>{{ role.name }}</span>
            <div class="card-actions">
              <el-button
                size="small"
                type="text"
                @click="handleEdit(role)"
              >编辑</el-button>
              <el-button
                size="small"
                type="text"
                @click="handleDelete(role)"
              >删除</el-button>
            </div>
          </div>
        </template>
        <div class="card-content">
          <div class="info-item">
            <span class="label">手机号：</span>
            <span class="value">{{ role.phone }}</span>
          </div>
          <div class="info-item">
            <span class="label">岗位：</span>
            <span class="value">{{ getPositionLabel(role.position) }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ role.createTime }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态提示 -->
    <el-empty v-if="filteredRoles.length === 0" description="暂无角色数据" />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import {useRouter} from 'vue-router'

// 搜索查询条件
const searchQuery = ref('')

// 岗位选项
const positionOptions = [
  { value: 'admin', label: '管理员' },
  { value: 'developer', label: '开发人员' },
  { value: 'operator', label: '运营人员' },
  { value: 'tester', label: '测试人员' }
]

// 获取岗位标签
const getPositionLabel = (value) => {
  const position = positionOptions.find(item => item.value === value)
  return position ? position.label : '未知岗位'
}

// 角色表单数据
const roleForm = reactive({
  name: '',
  phone: '',
  password: '',
  position: ''
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 16, message: '长度在 6 到 16 个字符', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择岗位', trigger: 'change' }
  ]
})

// 对话框显示状态
const dialogVisible = ref(false)
const roleFormRef = ref(null)

// 角色列表数据
const roleList = ref([
  {
    name: '超级管理员',
    phone: '13800138000',
    position: 'admin',
    createTime: '2023-05-01 10:00:00'
  },
  {
    name: '开发主管',
    phone: '13800138001',
    position: 'developer',
    createTime: '2023-05-02 11:00:00'
  }
])

// 过滤后的角色列表
const filteredRoles = computed(() => {
  if (!searchQuery.value) {
    return roleList.value
  }
  return roleList.value.filter(role => 
    role.phone.includes(searchQuery.value)
  )
})

// 清空搜索
const handleSearchClear = () => {
  searchQuery.value = ''
}

// 显示创建对话框
const showCreateDialog = () => {
  resetRoleForm()
  dialogVisible.value = true
}

// 重置表单
const resetRoleForm = () => {
  roleForm.name = ''
  roleForm.phone = ''
  roleForm.password = ''
  roleForm.position = ''
}

// 提交角色表单
const submitRoleForm = () => {
  roleFormRef.value.validate((valid) => {
    if (valid) {
      // 创建角色
      const newRole = {
        name: roleForm.name,
        phone: roleForm.phone,
        position: roleForm.position,
        createTime: new Date().toLocaleString()
      }
      
      roleList.value.push(newRole)
      dialogVisible.value = false
      ElMessage.success('角色创建成功')
    } else {
      ElMessage.error('请填写完整正确的表单信息')
      return false
    }
  })
}

// 编辑角色
const handleEdit = (row) => {
  // 这里可以打开编辑对话框
  console.log('编辑角色:', row)
  ElMessage.info('编辑功能待实现')
}

// 删除角色
const handleDelete = (row) => {
  // 确认删除
  ElMessageBox.confirm('确认删除该角色吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    roleList.value = roleList.value.filter(item => item.phone !== row.phone)
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.action-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.role-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.role-card {
  transition: all 0.3s;
}

.role-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #888;
  min-width: 80px;
}

.value {
  flex: 1;
  word-break: break-all;
}
</style>