<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Left from '../../components/Left.vue'
import {Session} from "@/utils/storage.js";
const route = useRoute()
const menuList = ref([])
const menuList1 = ref([
  {
    moduleName: '系统数据',
    moduleUrl : '/technicalDrainage/settingSet',
  },
  {
    moduleName: '系统更新',
    moduleUrl : '/technicalDrainage/systemSetting',
  }
])

// 当前激活菜单项
const openMenus = computed(() => {
  if (route.path.startsWith('/technicalDrainage')) {
    return ['/technicalDrainage']
  }
  return []
})

// 菜单按钮点击跳转
const handleButtonClick = (item) => {
  if (item === "数据") router.push("/technicalDrainage/settingSet")
  if (item === "系统更新") router.push("/technicalDrainage/systemSetting")
}
onMounted(() => {
  const route = useRoute();
  nextTick(() => {
    const _menuList = Session.get("menuList");
    for (let i = 0; i < _menuList.length; i++) {
      const item = _menuList[i];
      if (item.id == route.query.id){
        menuList.value = item.children
      }
    }
  })
})

</script>

<template>
  <HomeBg>
    <el-container style="height: 100vh;">
      <Left :list="menuList"></Left>
    </el-container>
  </HomeBg>
</template>

<style lang="scss" scoped>

/* 去除二级菜单默认边框 */
:deep(.el-submenu .el-menu) {
  border-right: none !important;
  background-color: transparent !important;
}

:deep(.el-submenu__title) {
  background-color: #fff !important;
}

/* 侧边栏菜单样式 */
.sub-menu {
  position: fixed;
  height: 100%;
  border-right: 1px solid #000;
  background-color: #fff;
}

.el-menu-item.is-active {
  background-color: #2c3e50 !important;
  color: #fff !important;
}

.aside-menu {
  border-right: none;
}

/* 主体内容样式 */
.main-content {
  position: fixed;
  padding: 20px;
  background-color: #f9f9f9;
}

/* 强制让 el-menu-item 内容居中 */
:deep(.el-menu-item) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  font-size: 16px;
}

/* 如果只想对基础系统的子菜单生效，可以加更具体的类名或结构选择器 */
</style>