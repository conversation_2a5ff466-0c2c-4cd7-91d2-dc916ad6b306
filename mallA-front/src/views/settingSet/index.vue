<script setup>
import HomeBg from '../../components/HomeBg.vue'
import AsideBar from '../../components/AsideBar.vue'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import request from '../../utils/request'
import { ElMessage } from 'element-plus'

const form = ref({
  sysQuantityValue: '0.00',    // 系统量化值累计
  sysCredit: '0.00',           // 系统量化累计
  sysPromotionGold: '0.00',    // 系统促销金累计
  sysQuantityNum: '0.00',      // 系统量化数累计
  sysSubsidyFunds: '0.00',     // 系统补贴金累计
  sysVerifiedSubsidy: '0.00',  // 系统已核销补贴金累计
  sysWeight: '0.00'            // 系统分量累计
})

const loading = ref(false)

// 获取系统数据
const fetchSystemData = async () => {
  loading.value = true
  try {
    const response = await request({
      url: '/mall-project/querySystemDatas',
      method: 'get'
    })

    if (response && response.code === 200) {
      // 成功获取数据，更新form
      const data = response.data
      form.value = {
        sysQuantityValue: data.sysQuantityValue || '0.00',
        sysCredit: data.sysCredit || '0.00',
        sysPromotionGold: data.sysPromotionGold || '0.00',
        sysQuantityNum: data.sysQuantityNum || '0.00',
        sysSubsidyFunds: data.sysSubsidyFunds || '0.00',
        sysVerifiedSubsidy: data.sysVerifiedSubsidy || '0.00',
        sysWeight: data.sysWeight || '0.00'
      }
    } else if (response && response.code === 500) {
      // 无数据情况
      ElMessage.warning(response.message || '暂无数据！')
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (error) {
    console.error('获取系统数据失败:', error)
    // 优先使用后台返回的错误信息
    if (error.response && error.response.data && error.response.data.message) {
      ElMessage.error(error.response.data.message)
    } else if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('网络错误，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchSystemData()
})

const router = useRouter()
const handleButtonClick = (item) => {
  // if(item==="基础设置") router.push("/roleList")
  if (item === "数据") router.push("/technicalDrainage");
  if (item === "系统更新") router.push("/systemSetting");
  if (item === "状态数据") router.push("/stateData");
};

const buttonList=ref([
  {label:'数据',path:'/technicalDrainage'},
  {label:'系统更新',path:'/systemSetting'},
  {label:'状态数据',path:'/stateData'},
])
// const handleSubmit = () => {
//   form.value.agreeProtocol = true // 自动勾选协议
  
//   if (!form.value.agreeProtocol) {
//     alert('请先阅读并同意服务协议')
//     return
//   }
//   console.log('提交表单:', form.value)
// }
// const router = useRouter()
// const handleButtonClick = (item) => {
//   if(item === '平台协议') router.push('/platformAgreement')
//   if(item === '技术引流') router.push('/technicalDrainage')
//   if(item === '权限设置') router.push('/permissionSetting')
//   if(item === '关系链设置') router.push('/relationshipChainSetting')
//   if(item === '分量设置') router.push('/heftSetting')
//   if(item === '发票设置') router.push('/')
// }

// const buttonList = [
//   '平台协议', '技术引流', '权限设置', '关系链设置', '分量设置','发票设置'
// ]
</script>

<template>

    <el-container>

      <!-- 右侧二级菜单 -->

      <!-- 主内容区域 -->



             <el-main class="main-content">
        <router-view />

    <div class="rightBox">
    <div class="main">
      <!-- 使用 el-row + el-col 实现每行3个 -->
      <el-row :gutter="20">
        <!-- 系统量化值累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统量化值累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysQuantityValue" placeholder="0.00" disabled />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>

        <!-- 系统量化累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统量化累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysCredit" placeholder="0.00" disabled/>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>

        <!-- 系统促销金累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统促销金累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysPromotionGold" placeholder="0.00" disabled/>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>

        <!-- 系统量化数累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统量化数累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysQuantityNum" placeholder="0.00" disabled/>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>

        <!-- 系统补贴金累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统补贴金累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysSubsidyFunds" placeholder="0.00" disabled/>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>

        <!-- 系统已核销补贴金累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统已核销补贴金累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysVerifiedSubsidy" placeholder="0.00" disabled/>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>

        <!-- 系统分量累计 -->
        <el-col :span="8">
          <div class="form-row">
            <div class="input-group">
              <span class="label">系统分量累计</span>
              <el-form :model="form" class="inline-form">
                <el-form-item>
                  <el-input v-model="form.sysWeight" placeholder="0.00" disabled/>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
      </el-main>
    </el-container>

</template>
<style lang="scss" scoped>
.container {
  html,
body {
  height: 100vh;
  margin: 0;
  padding: 0;
      color:#fff;

}
.active {
  background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
  color: black; /* 确保文字颜色在背景色上清晰可见 */
}
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }
  .sidebar1,
  .sidebar {
    width: 100%;
    height: auto;
  }
}
.sidebar {
  --active-bg: #2c3e50;
  --active-color: #ffffff;
  --hover-bg: #e9ecef;

  width: 240px;
  background: #f8f9fa;
  height: 100vh;
  border-right: 1px solid #dee2e6;

  .sidebar2 {
    list-style: none;
    padding: 20px 0;
    margin: 0;

    li {
      position: relative;
      transition: background-color 0.3s ease;

      a {
        display: block;
        // padding: 12px 24px;
        color: #2c3e50;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: var(--hover-bg);
      }

      &.active1  {
        background-color: var(--active-bg);
        border-left: 4px solid #3498db;

        a {
          color: var(--active-color);
          font-weight: 500;
        }
      }
    }
  }
}
.layout-container {
  height: 100vh;
  
  display: flex;
  // min-height: 100vh;
  position: fixed;
  z-index: 1000;
}

.main-content {
//   flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
  // position: relative;
  width: 100%;
  height: 100vh;
 display:flex;
  box-sizing: border-box;
 flex-wrap: wrap;

 .left-buttons {
  position: fixed;
  z-index: 1000;
    border: 1px solid #000;
    width: 200px;
    height: 100%;
    // overflow-y: auto;
    background: #fff;
    // border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
   .sidebar2 {
    list-style: none;
    padding: 20px 0;
    margin: 0;

    li {
      position: relative;
      transition: background-color 0.3s ease;

      a {
        display: block;
        // padding: 12px 24px;
        color: #2c3e50;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: var(--hover-bg);
      }

      &.active1  {
        background-color: var(--active-bg);
        border-left: 4px solid #3498db;

        a {
          color: var(--active-color);
          font-weight: 500;
        }
      }
    }
  }
    :deep(.el-button.data-button) {
     
      width: 100%;
      height: 50px;
      background-color: #3a58cf;
   
      color: #fff;
      font-size: 16px;
      border-radius: 0;
      border: none;
      margin: 0;
      padding: 0;
      display: block;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      transition: background-color 0.3s;

      &:hover {
        background-color: #2a48bf;
      }

      &:first-child {
        border-radius: 0px;
        // border-radius: 8px;
        margin-bottom: 10px;
      }

      &:last-child {
        margin-top: 10px;
        border-radius: 0px;
        // border-radius: 8px;
        border-bottom: none;
      }
    }
  }
}

.rightBox{
    display: flex;
    justify-content: center;
    align-items: center;    
    
}
.fixed-title {
  margin-left: 20px;
  margin-top: 20px;
  width: 150px;
  // height: 500px;
  margin-right: 10px;
  left: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  border-right: solid 1px rgb(12, 12, 12);
}

.main {
//   margin-top: 100px;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  margin-left: 0%;
}

.form-row {
  margin: 20px;
  padding: 20px;
  background-color: #3A58CF;
  color: #fff;
  border-radius: 10px;
  text-align: center;
}

.input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.label {
  margin-bottom: 10px;
  font-weight: bold;
}

/* 隐藏数字输入框箭头 */
.custom-number-input {
  :deep(input[type="number"]::-webkit-outer-spin-button),
  :deep(input[type="number"]::-webkit-inner-spin-button) {
    -webkit-appearance: none;
    margin: 0;
  }
  :deep(input[type="number"]) {
    -moz-appearance: textfield;
  }
}

.reduce-method {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 15px;
  
  .method-label {
    font-size: 16px;
    color: #333;
  }
  
  .square-radio {
    :deep(.el-radio__inner) {
      border-radius: 4px;
    }
  }
}

.submit-area {
  margin-top: 40px;
  text-align: center;
  
  .submit-btn {
    width: 300px;
    height: 80px;
    padding: 10px;
    
    .btn-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .protocol-agree {
        display: flex;
        align-items: center;
        justify-content: center;
        
        :deep(.el-checkbox__label) {
          color: #666;
          font-size: 12px;
        }
        
        .protocol-link {
          color: #3A58CF;
          text-decoration: none;
          margin-left: 5px;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>