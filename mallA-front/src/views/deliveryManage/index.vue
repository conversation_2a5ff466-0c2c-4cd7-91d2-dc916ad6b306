<script setup>
import ManageBg from '../../components/ManageBg.vue'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 按钮列表数据
const buttonList = [
  '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
  '评论管理', '退货地址',  '商品链接', '商品链接生成',
  '商品链接导入', '商品代销申请'
]

// 配送数据表格
const deliveryData = ref([
  {
    account: 100001,
    company: '顺丰速运',
    unit: '件',
    status: 'active'
  },
  {
    account: 100002,
    company: '中通快递',
    unit: 'kg',
    status: 'active'
  },
  {
    account: 100003,
    company: '圆通速递',
    unit: '件',
    status: 'inactive'
  }
])

// 按钮点击处理
const handleButtonClick = (item) => {
  if(item === '商品列表') router.push('/productList')
  if(item === '发布商品') router.push('/commodity')
  if(item === '商品分类') router.push('/productCategory')
  if(item === '品牌管理') router.push('/brandManage')
  if(item === '配送管理') router.push('./deliveryManage')
  if(item === '评论管理') router.push('./commentManage')
  if(item === '退货地址') router.push('./returnAddress')
  if(item === '商品链接') router.push('./productLink')
  if(item === '商品链接生成') router.push('./buildProductLink')
  if(item === '商品链接导入') router.push('./productLinkImport')
  if(item === '商品代销申请') router.push('./productSellApply')
}


// 新增配送
const handleAddDelivery = () => {
  ElMessage.info('新增配送功能待实现')
}

// 返回配送方式
const handleReturnMethod = () => {
  router.push('/deliveryMethods')
}

// 修改配送信息
const handleEdit = (row) => {
  ElMessageBox.prompt('修改快递公司名称', '提示', {
    inputValue: row.company,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    row.company = value
    ElMessage.success('修改成功')
  })
}

// 删除配送
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定删除 ${row.company} 的配送信息?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deliveryData.value = deliveryData.value.filter(item => item.account !== row.account)
    ElMessage.success('删除成功')
  })
}
</script>

<template>
  <ManageBg>
    <div class="container">
      <div class="left-buttons">
        <el-button 
          v-for="(item, index) in buttonList" 
          :key="index"
          class="data-button"
          @click="handleButtonClick(item)"
        >
          {{ item }}
        </el-button>
      </div>

      <div class="right-content">
        <div class="header">
          <span class="deliveryList">配送列表</span>
          <el-button class="newDelivery" @click="handleAddDelivery">
            新增配送+
          </el-button>
        </div>
        
        <div class="table-container">
          <el-table 
            :data="deliveryData" 
            style="width: 1291px"
            height="691"
            border
          >
            <el-table-column 
              prop="account" 
              label="账号" 
              width="200"
              sortable
            >
              <template #default="{ row }">
                {{ Number(row.account) }} <!-- 确保显示为数字 -->
              </template>
            </el-table-column>
            
            <el-table-column 
              prop="company" 
              label="快递公司名称" 
              width="300"
            />
            
            <el-table-column 
              prop="unit" 
              label="单位" 
              width="200"
            />
            
            <el-table-column 
              label="操作" 
              width="400"
            >
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  size="small"
                  @click="handleReturnMethod(row)"
                >
                  返回配送方式
                </el-button>
                <el-button 
                  type="warning" 
                  size="small"
                  @click="handleEdit(row)"
                >
                  修改
                </el-button>
                <el-button 
                  type="danger" 
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ManageBg>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  height: 100vh;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}

.right-content {
  flex: 1;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .deliveryList {
    font-size: 24px;
    font-weight: bold;
    color: #000;
  }

  .newDelivery {
    width: 150px;
    height: 40px;
    background-color: #3A58CF;
    color: white;
    border: none;
    font-size: 16px;
    
    &:hover {
      background-color: #2a48bf;
    }
  }
}

.table-container {
  margin-top: 20px;
  
  :deep(.el-table) {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    
    th {
      background-color: #f5f7fa;
      font-weight: bold;
    }
    
    .el-button {
      margin: 0 5px;
    }
  }
}
</style>