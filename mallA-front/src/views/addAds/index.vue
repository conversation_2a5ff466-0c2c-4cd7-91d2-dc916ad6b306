<script setup>
import { Plus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import {useRouter} from 'vue-router'
const router = useRouter()
const formData = ref({
  sort: '',
  title: '',
  image: '',
  payment: '',
  website: ''
})

const handleSubmit = () => {
    router.push('/ad')
}
</script>

<template>
  <div class="container">
    <!-- 固定在左上角的标题 -->
    <div class="page-title">
      <span>添加广告</span>
    </div>
    
    <!-- 表单内容区域 -->
    <div class="main">
      <div class="form-wrapper">
        <!-- 排序行 -->
        <div class="form-row">
          <span class="label">排序</span>
          <el-input 
            v-model="formData.sort" 
            placeholder="请输入排序数字" 
            class="form-input"
          />
        </div>
        
        <!-- 标题行 -->
        <div class="form-row">
          <span class="label">标题</span>
          <el-input 
            v-model="formData.title" 
            placeholder="请输入广告标题" 
            class="form-input"
          />
        </div>
        
        <!-- 图片行 -->
        <div class="form-row">
          <span class="label">图片</span>
          <el-upload
            action="#"
            :auto-upload="false"
            :on-change="(file) => formData.image = file.name"
          >
            <el-icon :size="28" class="add-icon">
              <Plus />
            </el-icon>
          </el-upload>
        </div>
        
        <!-- 支付行 -->
        <div class="form-row">
          <span class="label">支付</span>
          <el-input 
            v-model="formData.payment" 
            placeholder="请输入支付方式" 
            class="form-input"
          />
        </div>
        
        <!-- 商家官网链接行 -->
        <div class="form-row">
          <span class="label">商家官网链接</span>
          <el-input 
            v-model="formData.website" 
            placeholder="请输入官网链接" 
            class="form-input"
          />
        </div>
        
        <!-- 提交按钮 -->
        <div class="form-row submit-row">
          <el-button 
            type="primary" 
            class="submit-btn"
            @click="handleSubmit"
          >
            提交
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  min-height: 100vh;
  padding: 30px;
  background-color: #f5f7fa;
}

.page-title {
  position: absolute;
  top: 30px;
  left: 30px;
  
  span {
    font-size: 28px;
    font-weight: bold;
    color: #333;
  }
}

.main {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 100px);
  margin-top: 80px;
}

.form-wrapper {
  width: 100%;
  max-width: 700px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  
  .label {
    width: 150px;
    text-align: right;
    padding-right: 25px;
    font-size: 18px;
    color: #606266;
    flex-shrink: 0;
  }
  
  .form-input {
    flex: 1;
    min-width: 300px;
    
    :deep(.el-input__inner) {
      height: 45px;
      font-size: 16px;
    }
  }
  
  .add-icon {
    color: #409eff;
    cursor: pointer;
    margin-left: 10px;
    
    &:hover {
      color: #66b1ff;
    }
  }
  
  &.submit-row {
    justify-content: center;
    margin-top: 40px;
    margin-bottom: 0;
  }
}

.submit-btn {
  width: 200px;
  height: 50px;
  font-size: 18px;
  border-radius: 8px;
}
</style>