<template>
  <div class="container">
    <!-- 大背景（全屏） -->
    <Background>
      <!-- 首页背景（固定尺寸） -->
      <div class="home-background-container">
        <img src="../../images/LoginBackground.png" class="home-background" />
      </div>

      <!-- 系统登录盒子（右侧） -->
      <div class="login-box">
        <h2 class="login-title">系统登录</h2>
        <el-form :model="ruleForm" ref="ruleFormRef" :rules="rules" @keyup.enter="onSignIn">
              <el-form-item prop="username">
                  <div class="input-group">
                      <input type="text" placeholder="请输入手机号" v-model="ruleForm.username" />
                  </div>
              </el-form-item>
              <el-form-item prop="password">
                  <div class="input-group">
                      <input type="password" placeholder="请输入密码" v-model="ruleForm.password"/>
                  </div>
              </el-form-item>
              <el-form-item prop="code">
                  <div class="captcha-group">
                      <div class="captcha-input">
                        <input type="text" placeholder="验证码" v-model="ruleForm.randomStr"/>
                      </div>
                      <div class="captcha-code">
                        <img :src="imgSrc" url="">
                      </div>
                      <a href="#" class="refresh-captcha" @click="getVerifyCode()">看不清？</a>
                  </div>
              </el-form-item>
              <!-- @click="onSignIn" -->
              <!-- /<div class="login-btn" @click="submitForm(ruleFormRef)">下一步</div> -->
              <div class="login-btn" :class="{ 'loading': isLoading }" @click="onSignIn" :disabled="isLoading">
                <div class="btn-content">
                  <div class="loading-spinner" v-if="isLoading">
                    <div class="spinner-ring"></div>
                  </div>
                  <span class="btn-text">{{ isLoading ? '登录中...' : '登录' }}</span>
                </div>
                <div class="ripple-effect"></div>
              </div>
              <div class="remember-forgot">
                <label class="remember-me">
                  记住账号 <input type="checkbox" class="square-checkbox" v-model="rememberPassword"/>
                </label>
                <router-link to="/forgotPassword" class="forgot-pwd"
                  >忘记密码？</router-link>
              </div>
        </el-form>
      </div>
    </Background>
  </div>
</template>

<script setup lang="ts">
    import axios from "axios";
    import {ref,reactive,onMounted} from "vue";
    import { ElMessage, ElMessageBox } from "element-plus";
    import Background from "../../components/Background.vue";
    import {useUserInfoLogin} from "../../stores/login";
    import {useUserInfo} from "../../stores/userInfo";
    import {useRouter} from 'vue-router';
    import {generateUUID} from "../../utils/other";
    import Cookies from 'js-cookie';
    import {useMessage} from '../../hooks/message';
    import {Session} from '../../utils/storage';
    import type { FormInstance, FormRules } from 'element-plus'
    import request from '../../utils/request';
    import  {getCaptcha,login,getUserInfo} from '@/api/login'

    const user = ref({});
    const loading = ref(true);
    const error = ref(null);
    const fetchUserData = async () => {
          try {
            // 统一增加Authorization请求头, skipToken 跳过增加token
		        const token = Session.getToken();
            axios.get('/getInfo', {
                headers: {
                  'Authorization': 'Bearer '+token,
                  'Content-Type': 'application/json; charset=UTF-8'
                }
              })
              .then(response => {
                // 处理响应
                var data = response.data.data
                console.log(data.menuList)
                var isShop=data.isShop
                //1 代表是商城  2 超级管理员
                if(isShop===1){
                  // console.log(data,'data')
                }else{
                  router.push({ path: '/menuManager'});

                }
                return response.data.data
              })
              .catch(error => {
                // 处理错误
              });

          } catch (err) {
            error.value = err.message || '获取用户信息失败';
          } finally {
            loading.value = false;
          }
    };
    const router = useRouter()
    //登录表单
    interface RuleForm {
        username: string,
        password:string,
        code:Number,
        randomStr:Number
    }
    const ruleForm = reactive<RuleForm>({
          username: '',
          password:'',
          uuid:'',
          randomStr:''
    })
    //记住账号
    const rememberPassword = ref(false)
    //登录加载状态
    const isLoading = ref(false)

    // 从localStorage加载保存的账号信息
    const loadSavedCredentials = () => {
      const savedCredentials = localStorage.getItem('savedCredentials')
      if (savedCredentials) {
        try {
          const credentials = JSON.parse(savedCredentials)
          if (credentials.username) {
            ruleForm.username = credentials.username
            rememberPassword.value = true
          }
        } catch (error) {
          console.error('解析保存的账号信息失败:', error)
          localStorage.removeItem('savedCredentials')
        }
      }
    }

    onMounted(() => {
      getVerifyCode()
      loadSavedCredentials()
    })
    const imgSrc = ref("");
    //重新获取验证码
    const getVerifyCode=()=>{
       var RefreshValue=generateUUID()
        getCaptcha(RefreshValue).then((data)=>{
          // data.uuid
          imgSrc.value = "data:image/gif;base64," + data.img;
          ruleForm.uuid = data.uuid

      })
    }

    const ruleFormRef = ref<FormInstance>()
    const rules = reactive<FormRules<RuleForm>>({
        username: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },

        ],
        password:[
          { required: true, message: '请输入密码', trigger: 'blur' },
            { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        code:[
          { required: true, message: '请输入验证码', trigger: 'blur' },
        ]
    })
//提交登录

    // const submitForm = async (formEl: FormInstance | undefined) => {

    //       if (!formEl) return
    //       await formEl.validate((valid, fields) => {
    //         if (valid) {
    //           console.log('submit!')
    //           // 如果勾选了记住账号
    //             if(rememberPassword.value) {
    //               localStorage.setItem('savedCredentials', JSON.stringify({
    //                 username: ruleForm.username
    //               }))
    //             } else {
    //             // 不记住则清除保存的凭证
    //               localStorage.removeItem('savedCredentials')
    //             }
    //              onSignIn();

    //         } else {
    //           console.log('error submit!', fields)
    //         }
    //       })
    // }
    //登录成功之后，获取用户信息
        // 账号密码登录
const onSignIn = async () => {
  if (isLoading.value) return; // 防止重复点击

  isLoading.value = true; // 开始加载动画
  ruleForm.captcha = ruleForm.randomStr;

  try {
    const data = await login(ruleForm);
    console.log(data);

    if(data.code === 200) {
      console.log(data)

      // 处理记住账号功能
      if(rememberPassword.value) {
        // 如果勾选了记住账号，保存用户名到localStorage
        localStorage.setItem('savedCredentials', JSON.stringify({
          username: ruleForm.username
        }))
      } else {
        // 如果没有勾选，清除保存的账号信息
        localStorage.removeItem('savedCredentials')
      }

      // 存储token 信息
      localStorage.setItem('employee_name',data.data.employee_name)
      Session.set('token', data.data.token);
      console.log("Session.set('token'",Session.get('token'))
      getInfo()
    } else if(data.code === 500) {
      console.log(11); // 现在这行会执行
      getVerifyCode();
    }
  } catch (error) {
    console.error("登录失败:", error);
    ElMessage.error(error.message );
    getVerifyCode();
  } finally {
    isLoading.value = false; // 结束加载动画
  }
}
const getInfo = () => {
    getUserInfo().then((result) =>{
      console.log(result)
      const status=result.code
      if(status===200){
        // TODO 将数据加载到Session中再跳转 Next()
        Session.set('menuList', result.data.menuList);
        router.push("/homeBg");
      }else if(status===500){
        useMessage().error(result.msg || '获取用户信息失败');
        // TODO 获取用户信息失败，重新登陆
        router.push({ path: '/logout'});
      }
    });
}

      // try {
      //   // 调用登录方法
      //   let result = await useUserInfoLogin().login(ruleForm);
      //   const status=result.code
      //   if(status===200){
      //       // 存储token 信息
			// 			Session.set('token', result.data.token);
      //       fetchUserData();
      //       // router.push({ path: '/menuManager'});
      //       // let res=useUserInfo().setUserInfos();
      //   }else if(status===500){
      //     useMessage().error(result.msg || '验证码错误');
      //     getVerifyCode()
      //   }
      // } finally {
      //   console.log("finally")
      //   // getVerifyCode()
      // }
    // const useUserInfo = async () => {
    //   try {
    //     let result=await useUserInfo().setUserInfos();
    //     console.log(result,'result')
    //   }
    // }


</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  /* 首页背景容器 */
  .home-background-container {
    position: absolute;
    width: 1023px;
    height: 617px;
    z-index: 2;
    margin-left: 80px;
    margin-top: 261px;
    margin-bottom: 118px;

    .home-background {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  /* 系统登录盒子 */
  .login-box {
    position: absolute;
    width: 642px;
    height: 824px;
    left: calc(80px + 1023px + 64px);
    top: 50%;
    transform: translateY(-50%);
    background: #5170ed;
    border-radius: 8px;
    padding: 40px 0;
    box-sizing: border-box;
    z-index: 3;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 138px;
    margin-bottom: 118px; /* 新增底部间距 */

    .login-title {
      text-align: center;
      color: white;
      font-size: 48px;
      width: 193px;
      height: 70px;
      margin: 70px 0 61px 0;
      line-height: 70px;
    }

    .input-group {
      margin-bottom: 25px;
      width: 100%;
      display: flex;
      justify-content: center;

      input {
        width: 465px;
        height: 70px;
        padding: 0 25px;
        font-size: 24px;
        background: #fff;
        color: #000;
        margin: 0 89px;

        &::placeholder {
          color: #000;
          font-size: 24px;
          opacity: 0.8;
        }

        &:focus {
          border-color: #5170ed;
          outline: none;
          box-shadow: 0 0 5px rgba(81, 112, 237, 0.5);
        }
      }
    }
    //深度修改样式
    :deep(.el-form-item__error){
      color: var(--el-color-danger);
      font-size: 12px;
      left: 91px;
      line-height: 1;
      padding-top: 0px;
      position: absolute;
      top: 88%;
    }
    .captcha-group {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 89px 25px;
      .captcha-input {
        width: 174px;
        height: 59px;
        margin-right: 15px;
        input {
          width: 100%;
          height: 100%;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 0 15px;
          background: #fff;
          color: #000;
          font-size: 24px;
        }
      }

      .captcha-code {
        width: 174px;
        height: 59px;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        color: #000;
        border-radius: 4px;
        margin: 0 10px;
      }

      .refresh-captcha {
        font-size: 20px;
        color: #000;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    /* 登录按钮样式 */
    .login-btn {
      width: 465px;
      height: 80px;
      background: #14097a;
      color: white;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: bold;
      margin: 0 89px 30px;
      text-decoration: none;
      position: relative;
      overflow: hidden;
      border: none;
      outline: none;

      &:hover:not(.loading) {
        background: #0e075e;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(20, 9, 122, 0.4);
      }

      &:active:not(.loading) {
        transform: translateY(0px) scale(0.98);
      }

      &.loading {
        background: #1a0d8a;
        cursor: not-allowed;
        transform: none;

        .btn-content {
          opacity: 0.8;
        }
      }

      .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        transition: all 0.3s ease;
        z-index: 2;
        position: relative;
      }

      .btn-text {
        transition: all 0.3s ease;
      }

      .loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .spinner-ring {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .ripple-effect {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: translate(-50%, -50%);
        transition: all 1.2s ease;
        pointer-events: none;
        opacity: 1;
      }

      &:active:not(.loading) .ripple-effect {
        width: 400px;
        height: 400px;
        opacity: 0;
        transition: all 1.2s ease;
      }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .remember-forgot {
      display: flex;
      justify-content: space-between;
      width: 465px;
      margin: 0 89px 128px;
      align-items: center;

      .remember-me {
        color: white;
        font-size: 20px;
        display: flex;
        align-items: center;

        .square-checkbox {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          width: 20px;
          height: 20px;
          border: 2px solid white;
          border-radius: 4px;
          margin-left: 10px;
          position: relative;
          cursor: pointer;

          &:checked::after {
            content: "✓";
            position: absolute;
            color: white;
            font-size: 16px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .forgot-pwd {
        color: white;
        font-size: 20px;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
