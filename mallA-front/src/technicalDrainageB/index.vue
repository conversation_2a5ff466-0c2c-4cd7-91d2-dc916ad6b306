<script setup>
import HomeBg from '../../components/HomeBg.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const form = ref({
  techAmount: '',
  total: '0.00',
  stock: '0.00',
  reduceMethod: 'daily',
  agreeProtocol: false
})

const handleSubmit = () => {
  form.value.agreeProtocol = true // 自动勾选协议
  
  if (!form.value.agreeProtocol) {
    alert('请先阅读并同意服务协议')
    return
  }
  console.log('提交表单:', form.value)
}
const router = useRouter()
const handleButtonClick = (item) => {
  if(item === '平台协议') router.push('/platformAgreement')
  if(item === '技术引流') router.push('/technicalDrainage')
  if(item === '权限设置') router.push('/permissionSetting')
  if(item === '关系链设置') router.push('/relationshipChainSetting')
  if(item === '分量设置') router.push('/heftSetting')
  if(item === '发票设置') router.push('/')
}

const buttonList = [
  '平台协议', '技术引流', '权限设置', '关系链设置', '分量设置','发票设置'
]
</script>

<template>
  <HomeBg>
    <div class="container">
      <div class="left-buttons">
          <el-button 
            v-for="(item, index) in buttonList" 
            :key="index"
            class="data-button"
            @click="handleButtonClick(item)"
          >
            {{ item }}
          </el-button>
      </div>
      <div class="rightBox">
          <span class="fixed-title">技术引流</span>
      <div class="main">
        <!-- 技术引流行 -->
        <div class="form-row">
          <div class="input-group">
            <span class="label">技术引流次</span>
            <el-form :model="form" class="inline-form">
              <el-form-item>
                <el-input 
                  v-model="form.techAmount" 
                  placeholder="请输入10-499的数字"
                  type="number"
                  min="10"
                  max="499"
                  class="custom-number-input"
                />
              </el-form-item>
            </el-form>
            <span class="unit">技术券量1次=100技术分量</span>
          </div>
        </div>

        <!-- 共计行 -->
        <div class="form-row">
          <div class="input-group">
            <span class="label">共计</span>
            <el-form :model="form" class="inline-form">
              <el-form-item>
                <el-input 
                  v-model="form.total" 
                  placeholder="0.00"
                />
              </el-form-item>
            </el-form>
            <span class="unit">元</span>
          </div>
        </div>

        <!-- 库存行 -->
        <div class="form-row">
          <div class="input-group">
            <span class="label">库存</span>
            <el-form :model="form" class="inline-form">
              <el-form-item>
                <el-input 
                  v-model="form.stock" 
                  placeholder="0.00"
                />
              </el-form-item>
            </el-form>
            <span class="unit">个 减库存方式：消耗自动减库存</span>
          </div>
          
        </div>

        <!-- 提交按钮区域 -->
        <div class="submit-area">
          <el-button 
            type="primary" 
            class="submit-btn"
            @click="handleSubmit"
          >
            <div class="btn-content">
              <div>确认协议并支付</div>
              <div class="protocol-agree">
                <el-checkbox 
                  v-model="form.agreeProtocol" 
                  class="square-checkbox"
                  label="我已阅读并同意"
                />
                <a href="/service-agreement" target="_blank" class="protocol-link">《服务协议》</a>
              </div>
            </div>
          </el-button>
        </div>
      </div>
      </div>
    
    </div>
  </HomeBg>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
 display:flex;
  box-sizing: border-box;
 
}
.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #2a48bf;
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
  }
}
.rightBox{
    display: flex;
    justify-content: center;
    
}
.fixed-title {
  margin-left: 20px;
  margin-top: 20px;
  width: 200px;
  height: 100px;
    margin-right: 10px;
  left: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.main {
  margin-top: 200px;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
}

.form-row {
  margin-bottom: 25px;
  .input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .label {
      min-width: 80px;
      font-size: 16px;
      color: #333;
    }
    
    .unit {
      color: #666;
      font-size: 14px;
      white-space: nowrap;
      margin-left: 10px;
    }
    
    .inline-form {
      .el-form-item {
        margin-bottom: 0;
        
        .el-input {
          width: 200px;
        }
      }
    }
  }
}

/* 隐藏数字输入框箭头 */
.custom-number-input {
  :deep(input[type="number"]::-webkit-outer-spin-button),
  :deep(input[type="number"]::-webkit-inner-spin-button) {
    -webkit-appearance: none;
    margin: 0;
  }
  :deep(input[type="number"]) {
    -moz-appearance: textfield;
  }
}

.reduce-method {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 15px;
  
  .method-label {
    font-size: 16px;
    color: #333;
  }
  
  .square-radio {
    :deep(.el-radio__inner) {
      border-radius: 4px;
    }
  }
}

.submit-area {
  margin-top: 40px;
  text-align: center;
  
  .submit-btn {
    width: 300px;
    height: 80px;
    padding: 10px;
    
    .btn-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .protocol-agree {
        display: flex;
        align-items: center;
        justify-content: center;
        
        :deep(.el-checkbox__label) {
          color: #666;
          font-size: 12px;
        }
        
        .protocol-link {
          color: #3A58CF;
          text-decoration: none;
          margin-left: 5px;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>