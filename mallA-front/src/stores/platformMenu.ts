import { defineStore } from 'pinia';
import { save,getMenuList } from '../api/platformMenu/index';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const platformMenu = defineStore('platformMenu', {
	state: () => ({}),
	actions: {
		/**
		 * 创建菜单方法
		 * @function login
		 * @async
		 * @param {Object} data - 创建菜单数据
		 * @returns {Promise<Object>}
		 */
		async SaveMenu(data:any) {
			return new Promise((resolve, reject) => {
				save(data).then((res) => {
						// 存储token 信息
						resolve(res);
					})
					.catch((err) => {
						// useMessage().error(err?.msg || '系统异常请联系管理员');
						// reject(err);
					});
			});
		},
		/**
		 * 创建菜单方法
		 * @function login
		 * @async
		 * @param {Object} data - 创建菜单数据
		 * @returns {Promise<Object>}
		 */
		async GetMenuList(data:any) {
			return  new Promise((resolve, reject) => {
					getMenuList(data).then((res) => {
						// 存储token 信息
						resolve(res);
					}).catch((err) => {
						// useMessage().error(err?.msg || '系统异常请联系管理员');
						// reject(err);
					});
				});
		}
	},
});