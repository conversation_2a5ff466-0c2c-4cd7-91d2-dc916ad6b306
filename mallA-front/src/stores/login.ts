import { defineStore } from 'pinia';
import {Session} from '../utils/storage';
import { login } from '../api/login/index';
import {useMessage} from '../hooks/message';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const useUserInfoLogin = defineStore('userInfo', {
	state: () => ({}),
	actions: {
		/**
		 * 登录方法
		 * @function login
		 * @async
		 * @param {Object} data - 登录数据
		 * @returns {Promise<Object>}
		 */
		async login(data:any) {
			return new Promise((resolve, reject) => {
				login(data).then((res) => {

						resolve(res);
					})
					.catch((err) => {
						useMessage().error(err?.msg || '系统异常请联系管理员');
						reject(err);
					});
			});
		},
	},
});
