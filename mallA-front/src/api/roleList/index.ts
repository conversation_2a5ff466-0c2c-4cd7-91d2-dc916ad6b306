import { id } from 'element-plus/es/locale/index.mjs';
import request from '../../utils/request'
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';
//创建新职员模态u框
// src/api/role.js

// 创建角色
export const createRole = (data) => {
  return request({
    url: '/mall-project/user/add',
    method: 'post',
    data
  });
};
export const getPositions=()=>{
    return request({
        url: '/mall-project/positions/getPositions',
        method: 'get'
    });
}
export const getModules=(id)=>{
    return request({
        url:'/mall-project/modules',
        method:'get',   
        params:{id:id}
    })
}
export const searchByNameOrPhone=(data)=>{
    return request ({
        url:'/mall-project/user/searchByNameOrPhone',
        method:'post',  
        data
    })
}
export const userDelect =(employeeId)=>{
    return request({
        url:'/mall-project/user/delete',
        method:'post',
       data:{employeeId:employeeId }
    })
}
// export const userDelect=(userId)=>{
//     return request({
//         url:`/mall-project/user/delete/${userId}`,
//         method:'post'
//     })
// }
//编辑功能
export const userEdit =(employeeId)=>{
    console.log("发送请求参数:", { employeeId })
    return request({
        url:'/mall-project/user/getEmployeeById',
        method:'post',
        data:{employeeId}
    })
}
//更新功能
export const userUpdate =(data)=>{
    console.log("发送请求参数:", { data })
    return request({
        url:'/mall-project/user/update',
        method:'post',
        data
    })
}
//编辑获取模块信息  
export const userEditModules =(employeeId)=>{
    console.log("发送请求参数:", { employeeId })
    return request({
        url:'/mall-project/modules/user',
        method:'post',
        data:{employeeId}
    })
}
//限制登入
export const userLimit = (employeeId, loginLimit) => {
    console.log("发送请求参数为:", { employeeId, loginLimit });

    return request({
        url: '/mall-project/user/updateLoginLimit',
        method: 'post',
        data: {
            employeeId,
            loginLimit
        },
        headers: {
            'Content-Type': 'application/json'
        }
    });
};
