import request from '../../utils/request';

/**
 * 模块数据接口类型定义
 */
export interface ModuleData {
  id?: number;
  moduleName: string;
  moduleCode: string;
  level: number;
  parentId: number;
  moduleUrl?: string;
  icon?: string;
  sortOrder: number;
  status: number;
}

/**
 * 获取模块列表
 */
export const getModulesList = () => {
  return request({
    url: '/mall-project/modules/list',
    method: 'get'
  });
};

/**
 * 根据ID获取模块详情
 * @param id 模块ID
 */
export const getModuleById = (id: number) => {
  return request({
    url: `/mall-project/modules/${id}`,
    method: 'get'
  });
};

/**
 * 创建新模块
 * @param data 模块数据
 */
export const createModule = (data: ModuleData) => {
  return request({
    url: '/mall-project/modules/create',
    method: 'post',
    data
  });
};

/**
 * 更新模块
 * @param data 模块数据
 */
export const updateModule = (data: ModuleData) => {
  return request({
    url: '/mall-project/modules/update',
    method: 'put',
    data
  });
};

/**
 * 删除模块
 * @param id 模块ID
 */
export const deleteModule = (id: number) => {
  return request({
    url: `/mall-project/modules/${id}`,
    method: 'delete'
  });
};

/**
 * 获取下一个可用的模块代码
 * @param level 级别
 * @param parentId 父模块ID
 * @param moduleId 当前模块ID（编辑时使用）
 */
export const getNextModuleCode = (level: number, parentId: number, moduleId?: number) => {
  const params: any = { level, parentId };
  if (moduleId) {
    params.moduleId = moduleId;
  }
  return request({
    url: '/mall-project/modules/nextCode',
    method: 'get',
    params
  });
};

/**
 * 获取下一个可用的排序值
 * @param level 级别
 * @param parentId 父模块ID
 */
export const getNextSortOrder = (level: number, parentId: number) => {
  return request({
    url: '/mall-project/modules/nextSortOrder',
    method: 'get',
    params: { level, parentId }
  });
};
