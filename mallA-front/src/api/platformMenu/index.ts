
import request from '../../utils/request';
// import other from '../../utils/other';
import other from '../../utils/other'
/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 创建菜单
 * @param data
 */

export const save = (data: Object) => {
	return request({
		url: '/system/menu',
		method: 'post',
		data: data,
	});
};

//查询菜单
export const getMenuList=(data:Object)=>{
	return request({
		url:'/system/menu/treeselect',
		method:'get',
		data:data
	})
}