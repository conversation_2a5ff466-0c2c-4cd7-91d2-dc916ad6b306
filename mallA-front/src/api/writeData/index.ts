import request from '../../utils/request';

/**
 * 查询核销数据
 * @param params 查询参数
 */
export function queryWriteOffData(params: {
  phone: string;
  startDate: string;
  endDate: string;
  pageNum: number;
  pageSize: number;
}) {
  return request({
    url: '/mall-project/api/queryWriteOffData',
    method: 'post',
    data: params
  });
}

/**
 * 导出核销数据
 * @param params 导出参数
 */
export function exportWriteOffData(params: {
  phone: string;
  startDate: string;
  endDate: string;
}) {
  return request({
    url: '/mall-project/api/exportWriteOffDataExcel',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}

/**
 * 获取今日核销补贴金
 */
export function getTodayTotalWriteOffSubsidy() {
  return request({
    url: '/mall-project/api/todayTotalWriteOffSubsidy',
    method: 'get'
  });
}

/**
 * 获取累计已核销补贴金
 */
export function getTotalWriteOffSubsidy() {
  return request({
    url: '/mall-project/api/totalWriteOffSubsidy',
    method: 'get'
  });
}

/**
 * 获取累计未核销
 */
export function getTotalUnWriteOffSubsidy() {
  return request({
    url: '/mall-project/api/totalUnWriteOffSubsidy',
    method: 'get'
  });
} 