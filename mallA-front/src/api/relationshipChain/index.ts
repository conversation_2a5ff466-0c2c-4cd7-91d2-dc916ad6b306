import request from '../../utils/request';

/**
 * 获取关系链用户列表
 * @param data 查询参数
 */
export const getMallBUsers = (data: any) => {
    return request({
        url: '/mall-project/getMallBUsers',
        method: 'post',
        data: data
    });
};

/**
 * 更新用户状态
 * @param data 更新参数 { id: string, status: string }
 */
export const updateMallBUsersStatus = (data: any) => {
    return request({
        url: '/mall-project/updateMallBUsersStatus',
        method: 'post',
        data: data
    });
};
