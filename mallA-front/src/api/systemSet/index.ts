import { id } from 'element-plus/es/locale/index.mjs';
import request from '../../utils/request'
import {log} from "echarts/types/src/util/log";
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';
// 获取下拉企业信息
export const getAllCooperateEnterprise = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/getAllCooperateEnterprise',
        method:'get',
        data
    })
}
//企业开关
export const setEnterpriseDataSwitch = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/setEnterpriseDataSwitch',
        method:'POST',
        data
    })
}
// 获取企业信息
export const getEnterpriseDataSet = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/getEnterpriseDataSet',
        method:'get',
        data
    })
}
//新增企业信息设置
// export const  addEnterpriseDataSet = (name)=>{
//     return request({
//         url:'/mall-project/cooperateEnterprise/addEnterpriseDataSet',
//         method:'post',
//        data:{name}
//     })
// }
// 添加企业
    export const createNewEnterprise = (enterpriseId) => {
    return request({
        url: '/mall-project/cooperateEnterprise/addEnterpriseDataSet',
        method: 'post',
        data: { enterpriseId:enterpriseId }
    });
};


// 删除企业
export const deleteNewEnterprise = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/deleteEnterpriseDataSet',
        method: 'delete',
        data: data
    });
};

//删除企业交易数据
export const deleteNewEnterpriseDataName = (data) =>{
    return request({
        url:'/mall-project/cooperateEnterprise/deleteEnterpriseDataName',
        method:'delete',
        data
    })
}
//查询企业交易数据
export const getEnterpriseDataName = () =>{
    return request({
        url:'/mall-project/api/statisticsData',
        method:'post',
        data: {
            operate: "query",
            type: 0
        }
    })
}
//新增企业交易数据
export const addEnterpriseDataName = (data)=>{
    return request({
        url:'/mall-project/api/statisticsData',
        method:'post',
        data: data
    })
}
//查看自定义常数
export const getCustomConstants = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/getCustomConstants',
        method:'get',
        data
    })
}
//查询自定义常数
export const setCustomConstants = (customConstants)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/setCustomConstants',
        method:'put',
       data:customConstants
    })
}
//获取下拉已添加各企业系统每日更新总累计量化数企业名称
export  const getEnterpriseQuantitySet = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/getEnterpriseQuantitySet',
        method:'get',
        data
    })
}
//添加各企业系统每日更新总累计量化数企业名称
export const addEnterpriseQuantity = (enterpriseId)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/addEnterpriseQuantitySet',
        method:'put',
        data: { enterpriseId:enterpriseId }
    })
}
//删除已添加各企业系统每日更新总累计量化数企业名称
export const deleteEnterpriseQuantitySet = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/deleteEnterpriseQuantitySet',
        method:'delete',
        data
    })
}
//查询各企业系统每日更新总累计量化数交易数据
export const getEnterpriseQuantityName = (params)=>{
    return request({
        url:'/mall-project/api/statisticsData',
        method:'post',
        data: {
            operate: "query",
            type: 1
        }
    })
}
//新增各企业系统每日更新总累计量化数交易数据
export const addEnterpriseQuantityName = (data)=>{
    return request({
        url:'/mall-project/api/statisticsData',
        method:'post',
        data: {
            operate: "add",
            type: 1,
            dataTypeName: data.tradeName
        }
    })
}
//删除各企业系统每日更新总累计量化数交易数据
export const deleteEnterpriseQuantityName = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/deleteEnterpriseQuantityName',
        method:"delete",
        data
    })
}
//时间，手机号，功能数据，今日功能数据，今日总功能数据
// 各企业系统每日更新总累计量化数 开、关
export  const setQuantityDataSwitch = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/setQuantityDataSwitch',
        method:'post',
        data
    })
}
//自定义常数开、关
export const setCustomConstantsSwitch  = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/setCustomConstantsSwitch',
        method:'post',
        data
    })
}

// 保存所有企业各ID每日每笔新交易数据的量化数比设置
export const saveOrUpdateDailyTradePercentage = (data) => {
    return request({
        url: '/mall-project/saveOrUpdateDailyTradePercentage',
        method: 'put',
        data
    })
}

// 获取所有企业各ID每日每笔新交易数据的量化数比设置
export const getDailyTradePercentage = () => {
    return request({
        url: '/mall-project/getDailyTradePercentage',
        method: 'get'
    })
}

// 保存所有合作企业Admain的各ID每日每笔数据量化数比设置
export const saveOrUpdatePartnerEnterpriseAdminData = (data) => {
    return request({
        url: '/mall-project/saveOrUpdatePartnerEnterpriseAdminData',
        method: 'put',
        data
    })
}

// 获取所有合作企业Admain的各ID每日每笔数据量化数比设置
export const getPartnerEnterpriseAdminData = () => {
    return request({
        url: '/mall-project/getPartnerEnterpriseAdminData',
        method: 'get'
    })
}

// 保存合作企业各IDB设置
export const saveOrUpdateBSettings = (data) => {
    return request({
        url: '/mall-project/saveOrUpdateBSettings',
        method: 'put',
        data
    })
}

// 获取合作企业各IDB设置
export const getBSettings = () => {
    return request({
        url: '/mall-project/bSettings',
        method: 'get'
    })
}

// 保存合作企业各IDC设置
export const saveOrUpdateCSettings = (data) => {
    return request({
        url: '/mall-project/saveOrUpdateCSettings',
        method: 'put',
        data
    })
}

// 获取合作企业各IDC设置
export const getCSettings = () => {
    return request({
        url: '/mall-project/cSettings',
        method: 'get'
    })
}

// 获取合作企业列表（用于区域设置）
export const getCooperateEnterpriseForArea = () => {
    return request({
        url: '/mall-project/api/getCooperateEnterprise',
        method: 'get'
    })
}

// 获取地区数据
export const getArea = (data) => {
    return request({
        url: '/mall-project/api/getArea',
        method: 'post',
        data
    })
}

// 保存区域授权
export const saveAreaAuthorize = (data) => {
    return request({
        url: '/mall-project/api/saveAreaAuthorize',
        method: 'post',
        data
    })
}

// 获取每日数据累计量
export const getDailyDataAccrueTotal = () => {
    return request({
        url: '/mall-project/dailyDataAccrueTotal',
        method: 'post'
    })
}

// 获取系统每日余数
export const getDailyRemainderTotal = () => {
    return request({
        url: '/mall-project/dailyRemainderTotal',
        method: 'post'
    })
}

// 获取累计计量数
export const getDailyMeterageTotal = () => {
    return request({
        url: '/mall-project/dailyMeterageTotal',
        method: 'post'
    })
}

// 获取量化数总累计
export const getDailyQuantityTotal = () => {
    return request({
        url: '/mall-project/dailyQuantityTotal',
        method: 'post'
    })
}

// 保存区域比例设置
export const saveOrUpdateAreaProportion = (data) => {
    return request({
        url: '/mall-project/api/saveOrUpdateAreaProportion',
        method: 'put',
        data
    })
}

// 获取区域比例设置
export const getAreaProportion = () => {
    return request({
        url: '/mall-project/api/getAreaProportion',
        method: 'get'
    })
}

// 保存或更新分量进化设置
export const saveOrUpdateQuantifyCount = (data) => {
    return request({
        url: '/mall-project/api/saveOrUpdateQuantifyCount',
        method: 'put',
        data
    })
}

// 获取分量进化设置
export const getQuantifyCount = () => {
    return request({
        url: '/mall-project/api/getQuantifyCount',
        method: 'get'
    })
}