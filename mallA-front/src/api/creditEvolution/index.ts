import request from '../../utils/request';

/**
 * 查询量化进化量数据
 * @param params 查询参数
 */
export function queryCreditEvolvePages(params: {
  phone?: string;
  startDate?: string;
  endDate?: string;
  pageNum: number;
  pageSize: number;
}) {
  return request({
    url: '/mall-project/api/queryCreditEvolvePages',
    method: 'post',
    data: params
  });
}

// 注释：导出功能现在直接使用 axios 调用，因为需要特殊的 blob 响应类型处理
// 导出接口：/mall-project/api/exportCreditEvolveExcel

// 注释：原来的独立统计接口已经合并到 queryCreditEvolvePages 接口中
// 统计数据现在通过 queryCreditEvolvePages 接口的 summary 字段返回
// todayTotalCreditEvolve: 今日总量化进化量
// totalCreditEvolve: 累计量化进化量